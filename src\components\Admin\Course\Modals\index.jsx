import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Select, Radio } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { COURSE } from "constants/AppConstants";
import {
  CourseAddDrawerStatus,
  createCourse,
  getCourse,
  onCloseError
} from "store/slices/Course/manageCourseSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {CourseCategoryData, DrawerStatus, CourseAddDrawer, CourseButtonAndModelLabel, CourseErrors, CourseShowMessage, CourseButtonSpinner, CourseEditData, tablePagination, sorting, filter } = useSelector(
    (state) => state[COURSE]
  );

  // Sample options for "Course Category Name" dropdown
  const courseCategoryOptions = CourseCategoryData
    ? CourseCategoryData?.map((value, i) => {
        return {
          label: value.name,
          value: value.id,
        };
      })
    : [];

  const onClose = () => {
    dispatch(CourseAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  };

  const onSubmit = async (formValues) => {
    if (CourseEditData && CourseEditData.id) {
      formValues.id = CourseEditData.id; // If editing, include the id in the form values
    }
    await dispatch(createCourse(formValues));
  };

  useEffect(() => {
    console.log("working fine")
  }, []);

  useEffect(() => {
    if (Object.keys(CourseErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getCourse({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [CourseErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>
      <Drawer
        title={CourseButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CourseAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            ...CourseEditData, // Ensure that CourseEditData has all necessary fields set
            is_core: CourseEditData?.is_core ? CourseEditData.is_core : 0, // default to 0 if not set
            display_both_names: CourseEditData?.display_both_names ? CourseEditData.display_both_names : 0, // default to 0 if not set
            display_category: CourseEditData?.display_category ? CourseEditData.display_category : 0, // default to 0 if not set
            is_graded: CourseEditData?.is_graded ? CourseEditData.is_graded : 0, // default to 0 if not set
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="course"
                label={setLocale('name')}
                rules={[{ required: true, message: setLocale('name_error') }]}
                validateStatus={CourseShowMessage && CourseErrors.course ? "error" : ""}
                help={CourseShowMessage && CourseErrors.course}
              >
                <Input />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="course_code"
                label={setLocale('course.course_code')}
                rules={[{ required: true, message: setLocale('course.course_code_error') }]}
                validateStatus={CourseShowMessage && CourseErrors.course_code ? "error" : ""}
                help={CourseShowMessage && CourseErrors.course_code}
              >
                <Input />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="display_name_report_card"
                label={"Display Name Report Card"}
                rules={[{ required: true, message: "Please enter the display name for the report card" }]}
                validateStatus={CourseShowMessage && CourseErrors.display_name_report_card ? "error" : ""}
                help={CourseShowMessage && CourseErrors.display_name_report_card}
              >
                <Input />
              </Form.Item>
            </Col>

            {/* Dropdown for Course Category Name */}
            <Col span={12}>
              <Form.Item
                name="course_category_id"
                label={"Course Category Name"}
                rules={[{ required: true, message: "Please select a course category" }]}
                validateStatus={CourseShowMessage && CourseErrors.course_category_id ? "error" : ""}
                help={CourseShowMessage && CourseErrors.course_category_id}
              >
                <Select
                  placeholder="Select a category"
                  options={courseCategoryOptions}
                />
              </Form.Item>
            </Col>

            {/* Radio Button for Is Core */}
            {/* <Col span={12}>
              <Form.Item
                name="is_core"
                label={"Is Core"}
                rules={[{ required: true, message: "Please select whether the course is core or not" }]}
                validateStatus={CourseShowMessage && CourseErrors.is_core ? "error" : ""}
                help={CourseShowMessage && CourseErrors.is_core}
              >
                <Radio.Group>
                  <Radio value={1}>Yes</Radio>
                  <Radio value={0}>No</Radio>
                </Radio.Group>
              </Form.Item>
            </Col> */}

            {/* Radio Button for Display Both Names */}
            <Col span={12}>
              <Form.Item
                name="display_both_names"
                label={"Display Both Names"}
                rules={[{ required: true, message: "Please select whether to display both names" }]}
                validateStatus={CourseShowMessage && CourseErrors.display_both_names ? "error" : ""}
                help={CourseShowMessage && CourseErrors.display_both_names}
              >
                <Radio.Group>
                  <Radio value={1}>Yes</Radio>
                  <Radio value={0}>No</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>

            {/* Radio Button for Display Category */}
            {/* <Col span={12}>
              <Form.Item
                name="display_category"
                label={"Display Category"}
                rules={[{ required: true, message: "Please select whether to display the category" }]}
                validateStatus={CourseShowMessage && CourseErrors.display_category ? "error" : ""}
                help={CourseShowMessage && CourseErrors.display_category}
              >
                <Radio.Group>
                  <Radio value={1}>Yes</Radio>
                  <Radio value={0}>No</Radio>
                </Radio.Group>
              </Form.Item>
            </Col> */}

            {/* Radio Button for Is Graded */}
            <Col span={12}>
              <Form.Item
                name="is_graded"
                label={"Is Graded"}
                rules={[{ required: true, message: "Please select whether the course is graded" }]}
                validateStatus={CourseShowMessage && CourseErrors.is_graded ? "error" : ""}
                help={CourseShowMessage && CourseErrors.is_graded}
              >
                <Radio.Group>
                  <Radio value={1}>Yes</Radio>
                  <Radio value={0}>No</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CourseButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};

export default Index;

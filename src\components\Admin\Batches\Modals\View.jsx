import React, { useEffect } from "react";
import { <PERSON>, <PERSON>, Card, Breadcrumb, Button } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
    ArrowLeftOutlined
} from "@ant-design/icons";
import {
    viewBatches,
    BatchesAddDrawerStatus
} from "store/slices/Batches/manageBatchesSlice.js";
import AddStudentChargesModal from "./index";
import IntlMessage from "components/util-components/IntlMessage"
import { BATCHES } from "constants/AppConstants";
import InvoiceTable from "./InvoiceTable";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function View() {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const params = useParams();
    const { BatchesAddDrawer, permission, invoiceFilter, invoiceSorting, ViewBatchesData, tableInvoicePagination } = useSelector(
        (state) => state[BATCHES]
    );

    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(viewBatches({ id: params?.id, page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    useEffect(() => {
        getModuleData(tableInvoicePagination.current, tableInvoicePagination.pageSize, invoiceFilter, invoiceSorting);
    }, []);

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Batches')}</Breadcrumb.Item>
            </Breadcrumb>
            {BatchesAddDrawer && <AddStudentChargesModal />}
            <Card
                title={
                    <>
                        {/* {permission.includes("Create") && ViewBatchesData?.data && ViewBatchesData?.data.length > 0 && ViewBatchesData?.data[0]?.batch_status !== 'Posted' && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={() => dispatch(BatchesAddDrawerStatus({ errorStatus: 1, status: true }))}
                            >
                                {setLocale("charges_and_invoices.add_student_charges")}
                            </Button>
                        )} */}
                        <h5>
                            <span style={{ cursor: "pointer", float: "left" }} onClick={() => navigate(-1)}>
                                <ArrowLeftOutlined /> Back
                            </span>
                        </h5>
                    </>
                }
                className="profile-card"
            >
                <Row justify="center">
                    <Col sm={24} md={24}>
                        <InvoiceTable />
                    </Col>
                </Row>
            </Card>
        </>
    );
}
export default View;

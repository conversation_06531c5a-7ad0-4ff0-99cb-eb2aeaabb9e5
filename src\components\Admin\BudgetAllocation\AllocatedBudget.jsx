import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    Button,
    Table,
    Pagination,
    Input,
    Space
} from 'antd';
import IntlMessage from "components/util-components/IntlMessage";
import { BUDGET_ALLOCATION } from "constants/AppConstants";
import {
    BudgetAllocationAddDrawerStatus,
    getBudgetAllocations,
    setColumnSearch,
    updateSortFilters,
    setAllocatedBudgetView
} from "store/slices/BudgetAllocation/manageBudgetAllocationSlice.js";
import AddBudgetAllocation from "./Modals/index";
import View from "./Modals/View";

import {
    SearchOutlined,
    EyeOutlined
} from '@ant-design/icons';
import Highlighter from 'react-highlight-words';

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AllocatedBudget() {
    const dispatch = useDispatch()
    const searchInput = useRef(null);

    const handleOpenModal = () => dispatch(BudgetAllocationAddDrawerStatus({ errorStatus: 1, status: true }));
    const {
        BudgetAllocationAddDrawer,
        permission,
        sorting,
        filter,
        tablePagination,
        BudgetAllocationTableLoading,
        BudgetAllocationResult,
        allocatedBudgetViewDrawer
    } = useSelector((state) => state[BUDGET_ALLOCATION]);

    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getBudgetAllocations({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };
    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) { }

    };
    const handleView = (record) => {
        dispatch(setAllocatedBudgetView({ status: true, data: record }));
    };
    const columns = [
        {
            title: setLocale('budgetallocation.userName'),
            dataIndex: "user_name",
            key: "user_name",
            sorter: true,
            ...getColumnSearchProps('user_name'),
        },
        {
            title: setLocale('budgetallocation.gradeLevel'),
            dataIndex: "grade_level",
            key: "grade_level",
            sorter: true,
            ...getColumnSearchProps('grade_level'),
        },
        {
            title: setLocale('budgetallocation.class'),
            dataIndex: "class",
            key: "class",
            sorter: true,
            ...getColumnSearchProps('class'),
        },
        {
            title: setLocale('budgetallocation.createdBy'),
            dataIndex: "created_by",
            key: "created_by",
            sorter: true,
            ...getColumnSearchProps('created_by'),
        },
        {
            title: setLocale('budgetallocation.total_allocated'),
            dataIndex: "total_allocated",
            key: "total_allocated",
        },
        {
            title: setLocale('budgetallocation.total_consumed'),
            dataIndex: "total_consumed",
            key: "total_consumed",
        },
        {
            title: setLocale('budgetallocation.createdAt'),
            dataIndex: "created_at",
            key: "created_at",
            sorter: true,
            ...getColumnSearchProps('created_at'),
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("View") && (
                        <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" onClick={(e) => handleView(record)} />
                    )}
                </>
            )
        }
    ];
    return (
        <>
            <div className="code-box">
                {permission.includes("Create") && (
                    <Button className="ant-btn-round ant-btn-sm"
                        type="primary" style={{ float: "right", margin: "10px" }} onClick={handleOpenModal} >
                        {setLocale('budgetallocation.add')}
                    </Button>
                )}

                <Table
                    onChange={handleTableChange}
                    columns={columns}
                    style={{ width: '100%', overflowX: 'scroll' }}
                    loading={BudgetAllocationTableLoading}
                    rowKey={record => record.id}
                    dataSource={BudgetAllocationResult.data ?? []}
                    pagination={false}
                />
                <Pagination
                    style={{ margin: '16px', float: 'right' }}
                    current={tablePagination.current}
                    pageSize={tablePagination.pageSize}
                    total={tablePagination.total}
                    showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                    pageSizeOptions={['10', '20', '50', '100', '1000']}
                    showQuickJumper
                    onChange={handlePageChange}
                />
            </div>
            {BudgetAllocationAddDrawer && <AddBudgetAllocation />}
            {allocatedBudgetViewDrawer && <View />}
        </>
    );
}

export default AllocatedBudget;

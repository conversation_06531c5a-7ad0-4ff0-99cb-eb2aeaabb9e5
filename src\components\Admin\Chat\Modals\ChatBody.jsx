import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { CHAT } from "constants/chat/index";
import ChatContentBody from './body/ChatContentBody';
import ChatContentFooter from './body/ChatContentFooter';
import ChatContentHeader from './body/ChatContentHeader';
import ChatContentEmptyChatBody from './body/ChatContentEmptyChatBody';
import { Skeleton, Drawer } from 'antd';

const ChatBody = () => {
	const dispatch = useDispatch();
	const { selectedConversation } = useSelector((state) => state[CHAT]);

	return (
		<>
			<div className="chat">
				<div className="chat-content">
					{!selectedConversation ? <ChatContentEmptyChatBody /> :
						<>
							<ChatContentHeader />
							<ChatContentBody />
						</>
					}
				</div>				
			</div>
		</>
	)
}

export default ChatBody

import React, { useEffect } from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CHARGES_CATEGORIES } from "constants/AppConstants";
import {
  ChargesCategoriesAddDrawerStatus,
  createChargesCategories,
  getChargesCategories,
  onCloseError
} from "store/slices/ChargesCategories/manageChargesCategoriesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {DrawerStatus, sorting, filter, ChargesCategoriesAddDrawer, ChargesCategoriesButtonAndModelLabel, ChargesCategoriesErrors, ChargesCategoriesShowMessage, ChargesCategoriesButtonSpinner, ChargesCategoriesEditData, tablePagination } = useSelector(
    (state) => state[CHARGES_CATEGORIES]
  );
  const onClose = () => {
    dispatch(ChargesCategoriesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {

    if (ChargesCategoriesEditData && ChargesCategoriesEditData.id) {
      // If editing, include the id in the form values
      formValues.id = ChargesCategoriesEditData.id;
    }

    await dispatch(createChargesCategories(formValues))
  };
useEffect(() => {
    if (Object.keys(ChargesCategoriesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getChargesCategories({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ChargesCategoriesErrors]);
  
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>

      <Drawer
        title={ChargesCategoriesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ChargesCategoriesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            charge_category: ChargesCategoriesEditData?.charge_category,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="charge_category"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={ChargesCategoriesShowMessage && ChargesCategoriesErrors.charge_category ? "error" : ""}
                help={ChargesCategoriesShowMessage && ChargesCategoriesErrors.charge_category}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={ChargesCategoriesButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useEffect } from "react";
import { Button, Col, Drawer, Form, Input, Row, Space, Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
  COMMENT,
  MANAGE_CLASS,
  COMMENT_CATEGORY,
} from "constants/AppConstants";
import {
  CommentAddDrawerStatus,
  createComment,
  getComment,
  onCloseError,
} from "store/slices/Comment/manageCommentSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { getManageClass } from "store/slices/ManageClass/manageManageClassSlice.js";
import { getClassStudents } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { getCommentCategory } from "store/slices/CommentCategory/manageCommentCategorySlice.js";
import { USER_INFORMATION } from "constants/AuthConstant";

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION));

const { TextArea } = Input;
const { Option } = Select;

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {
    CommentAddDrawer,
    CommentButtonAndModelLabel,
    sorting,
    filter,
    CommentErrors,
    CommentShowMessage,
    CommentButtonSpinner,
    CommentEditData,
    tablePagination,
    DrawerStatus,
  } = useSelector((state) => state[COMMENT]);
  const { ManageClassResult } = useSelector((state) => state[MANAGE_CLASS]);
  const { classStudents } = useSelector((state) => state[COMMENT]);
  const { CommentCategoryResult } = useSelector(
    (state) => state[COMMENT_CATEGORY]
  );
  const onClose = () => {
    dispatch(CommentAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  };
  const onSubmit = async (formValues) => {
    if (CommentEditData && CommentEditData.id) {
      // If editing, include the id in the form values
      formValues.id = CommentEditData.id;
    }
    formValues.teacher_id = currentUser?.teacher_data?.id;
    await dispatch(createComment(formValues));
  };

  useEffect(() => {
    if (Object.keys(CommentErrors).length === 0 && DrawerStatus === 0) {
      dispatch(
        getComment({
          page: tablePagination.current,
          perPage: tablePagination.pageSize,
          filter: filter,
          sorting: sorting,
        })
      );
    }
  }, [CommentErrors]);
  useEffect(() => {
    dispatch(getManageClass(null));
    dispatch(getCommentCategory());
    if (CommentEditData && CommentEditData.id) {
      dispatch(getClassStudents({ id: CommentEditData?.manage_class_id }));
    }
  }, []);
  /**get Class Students by ClassId */
  const getStudentByClass = async (value) => {
    await dispatch(getClassStudents({ id: value }));
    form.setFieldsValue({ student_id: [] });
  };
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>
      <Drawer
        title={CommentButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CommentAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            manage_class_id: CommentEditData?.manage_class_id,
            class_student_id: CommentEditData?.class_student_id,
            comment_category_id: CommentEditData?.comment_category_id,
            comments: CommentEditData?.comments,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="manage_class_id"
                label={setLocale("Class")}
                rules={[
                  {
                    required: true,
                    message: setLocale("Please Select Class"),
                  },
                ]}
                validateStatus={
                  CommentShowMessage && CommentErrors.manage_class_id
                    ? "error"
                    : ""
                }
                help={CommentShowMessage && CommentErrors.manage_class_id}
              >
                <Select onChange={getStudentByClass} optionLabelProp="label">
                  {ManageClassResult
                    ? ManageClassResult.map((clas, index) => (
                        <Option value={clas.id} key={index} label={clas.name}>
                          {clas.name}
                        </Option>
                      ))
                    : null}
                </Select>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="class_student_id"
                label={setLocale("Student")}
                rules={[
                  {
                    required: true,
                    message: setLocale("Please Select Student"),
                  },
                ]}
                validateStatus={
                  CommentShowMessage && CommentErrors.class_student_id
                    ? "error"
                    : ""
                }
                help={CommentShowMessage && CommentErrors.class_student_id}
              >
                <Select
                  optionLabelProp="label"
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) >= 0
                  }
                >
                  {classStudents
                    ? classStudents.map((clsStd, index) => (
                        <Option
                          value={clsStd.id}
                          key={index}
                          label={clsStd.students.full_name}
                        >
                          {clsStd.students.full_name +
                            " (" +
                            clsStd.students.student_id +
                            ")"}
                        </Option>
                      ))
                    : null}
                </Select>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="comment_category_id"
                label={setLocale("Comment Category")}
                rules={[
                  {
                    required: true,
                    message: setLocale("Please Select Comment Category"),
                  },
                ]}
                validateStatus={
                  CommentShowMessage && CommentErrors.comment_category_id
                    ? "error"
                    : ""
                }
                help={CommentShowMessage && CommentErrors.comment_category_id}
              >
                <Select
                  className="rounded-0"
                  showSearch
                  optionLabelProp="label"
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "").toLowerCase().includes(input)
                  }
                  // filterSort={(optionA, optionB) =>
                  //   (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                  // }
                  options={CommentCategoryResult.data ?? []}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="comments"
                label={setLocale("Comments")}
                rules={[
                  {
                    required: true,
                    message: setLocale("Please add Comments"),
                  },
                ]}
                validateStatus={
                  CommentShowMessage && CommentErrors.comments ? "error" : ""
                }
                help={CommentShowMessage && CommentErrors.comments}
              >
                <TextArea rows={4} />
              </Form.Item>
            </Col>
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CommentButtonSpinner}
            >
              {CommentButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale("Cancel")}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;

import React, { useEffect } from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CONVERSATION_TYPES } from "constants/AppConstants";
import {
    ConversationTypesAddDrawerStatus,
    createConversationTypes,
    getConversationTypes,
    onCloseError
  } from "store/slices/ConversationTypes/manageConversationTypesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {DrawerStatus,sorting, filter, ConversationTypesAddDrawer, ConversationTypesButtonAndModelLabel, ConversationTypesErrors, ConversationTypesShowMessage, ConversationTypesButtonSpinner, ConversationTypesEditData, tablePagination } = useSelector(
        (state) => state[CONVERSATION_TYPES]
      );
const onClose = () => {
    dispatch(ConversationTypesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }

const onSubmit = async (formValues) => {

  if (ConversationTypesEditData && ConversationTypesEditData.id) {
    // If editing, include the id in the form values
    formValues.id = ConversationTypesEditData.id;
  }

  await dispatch(createConversationTypes(formValues))
};
useEffect(() => {
    if (Object.keys(ConversationTypesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getConversationTypes({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ConversationTypesErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={ConversationTypesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ConversationTypesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            conversation_type: ConversationTypesEditData?.conversation_type,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="conversation_type"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={ConversationTypesShowMessage && ConversationTypesErrors.conversation_type ? "error" : ""}
                help={ConversationTypesShowMessage && ConversationTypesErrors.conversation_type}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={ConversationTypesButtonSpinner}
            >
              {("Save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


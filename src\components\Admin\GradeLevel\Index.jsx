import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton, Col } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, MenuOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { GRADE_LEVEL } from "constants/AppConstants";
import { USER_INFORMATION } from "constants/AuthConstant";
import AddGradeLevelModal from "./Modals/index";
import {
    GradeLevelAddDrawerStatus,
    GradeLevelEditWithDrawerStatus,
    deleteGradeLevel,
    getGradeLevel,
    updateSortFilters,
    setColumnSearch,
    updateGradeSort
} from "store/slices/GradeLevel/manageGradeLevelSlice.js";
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import OrganizationSelect from "../OrganizationDropdown/OrganizationSelect";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;


const Row = ({ children, ...props }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        setActivatorNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({
        id: props['data-row-key'],
    });
    const style = {
        ...props.style,
        transform: CSS.Transform.toString(
            transform && {
                ...transform,
                scaleY: 1,
            },
        ),
        transition,
        ...(isDragging
            ? {
                position: 'relative',
                zIndex: 9999,
            }
            : {}),
    };
    return (
        <tr {...props} ref={setNodeRef} style={style} {...attributes}>
            {React.Children.map(children, (child) => {
                if (child.key === 'sort') {
                    return React.cloneElement(child, {
                        children: (
                            <MenuOutlined
                                ref={setActivatorNodeRef}
                                style={{
                                    touchAction: 'none',
                                    cursor: 'move',
                                }}
                                {...listeners}
                            />
                        ),
                    });
                }
                return child;
            })}
        </tr>
    );
};

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(GradeLevelAddDrawerStatus({ errorStatus: 1, status: true }));
    const { GradeLevelAddDrawer, GradeLevelResult, tablePagination, sorting, filter, GradeLevelTableLoading, GradeLevelPageLoading, permission } = useSelector(
        (state) => state[GRADE_LEVEL]
    );

    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getGradeLevel({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };


    const handleDelete = (record) => {
        dispatch(deleteGradeLevel(record.id)).then(() => {
            getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        })
    }
    const handleUpdate = (record) => {
        dispatch(GradeLevelEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) {
            console.log(error);
        }

    };


    const onDragEnd = ({ active, over }) => {
        if (active.id !== over?.id) {

            dispatch(updateGradeSort({ from: active.id, to: over?.id })).then(() => {
                getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
            })
            // setDataSource((previous) => {
            //     const activeIndex = previous.findIndex((i) => i.key === active.id);
            //     const overIndex = previous.findIndex((i) => i.key === over?.id);
            //     return arrayMove(previous, activeIndex, overIndex);
            // });
        }
    };

    const columns = [
        {
            title: setLocale('sr_no'),
            dataIndex: 'key',
            key: 'sort',
        },
        {
            title: setLocale('name'),
            dataIndex: "grade_level",
            key: "grade_level",
            sorter: true,
            ...getColumnSearchProps('grade_level'),
        },
        {
            title: setLocale('type'),
            dataIndex: "type",
            key: "type",
            sorter: true,
            ...getColumnSearchProps('type'),
            render: (text) => text.charAt(0).toUpperCase() + text.slice(1),
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                    {/* {permission.includes("View") && (
                        <Link to={`../../app/grade-level_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
                </>
            )
        },
    ];

    return (
        <>
            <div className="flex justify-between items-center">
                <div style={{ width: '50%' }} >
                    <Breadcrumb className="my-2 mx-2">
                        <Breadcrumb.Item>
                            <Link to="/app/default">{setLocale('home')}</Link>
                        </Breadcrumb.Item>
                        <Breadcrumb.Item>{setLocale('grade_level.title')}</Breadcrumb.Item>
                    </Breadcrumb>
                </div>
                <div className="mb-2" style={{ textAlign: 'right', marginTop: "-38px" }}>
                    <OrganizationSelect
                        updateSortFilters={updateSortFilters}
                        filter={filter}
                        sorting={sorting}
                        tablePagination={tablePagination}
                        getModuleData={getModuleData}
                    />
                </div>
            </div>


            <div className="code-box">
                <section className="code-box-demo">
                    {permission.includes("Create") && (
                        <Button
                            className="ant-btn-round ant-btn-sm"
                            type="primary"
                            style={{ float: "right", margin: "5px" }}
                            onClick={handleOpenModal}
                        >
                            {setLocale('grade_level.add')}
                        </Button>
                    )}
                </section>
                {GradeLevelAddDrawer && <AddGradeLevelModal />}
                <section className="code-box-description">
                    {/* <Skeleton active loading={GradeLevelPageLoading} > */}
                    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
                        <SortableContext
                            // rowKey array
                            items={GradeLevelResult?.data?.map((i) => i.key) ?? []}
                            strategy={verticalListSortingStrategy}
                        >

                            <Table
                                onChange={handleTableChange}
                                columns={columns}
                                components={{
                                    body: {
                                        row: Row,
                                    },
                                }}
                                loading={GradeLevelTableLoading}
                                rowKey={record => record.id}
                                dataSource={GradeLevelResult.data ?? []}
                                pagination={false}
                            />
                            <Pagination
                                style={{ margin: '16px', float: 'right' }}
                                current={tablePagination.current}
                                pageSize={tablePagination.pageSize}
                                total={tablePagination.total}
                                showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                                pageSizeOptions={['10', '20', '50', '100', '1000']}
                                showQuickJumper
                                onChange={handlePageChange}
                            />
                        </SortableContext>
                    </DndContext>
                    {/* </Skeleton> */}
                </section>
            </div>
        </>
    );
}

export default Index;

import React, { useState, useEffect, useRef, useMemo } from "react";
import { useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Row,
  Col,
  Card,
  Button,
  Calendar,
  List,
  Drawer,
  Select,
  Badge,
  Modal,
  Skeleton,
  DatePicker,
} from "antd";
import IntlMessage from "components/util-components/IntlMessage";

import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import StatisticWidget from "components/shared-components/StatisticWidget";
import Chart from "react-apexcharts";
import ChartWidget from "components/shared-components/ChartWidget";
import { getGradeLevel } from "store/slices/GradeLevel/manageGradeLevelSlice";
import { useNavigate, Link } from "react-router-dom";
import {
  getCounts,
  getEnrollmentData,
  getMonthlyAttendance,
  getGoogleClassroomStatus,
  setOpenDrawer3,
  setOpenDrawer4,
  getCalendarActivity,
} from "store/slices/dashboardManagement/dashboardSlice";
import Overflow from "views/app-views/components/data-display/badge/Overflow";
import moment from "moment";
import { DASHBOARD, SCHOOL_YEAR } from "constants/AppConstants";
import { DATE_FORMAT_YYYY_MM } from "constants/DateConstant";
import cloneDeep from 'lodash/cloneDeep';
import { saveGoogleToken } from "store/slices/ManageClass/manageManageClassSlice";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const onPanelChange = (value, mode) => {
  // console.log(value.format("YYYY-MM-DD"), mode);
};

function CustomHeader({ value, type, onChange }) {
  const headerText =
    type === "month" ? value.format("MMMM") : value.format("MMMM YYYY");

  const handlePrevMonth = () => {
    onChange(value.subtract(1, "month"));
  };

  const handleNextMonth = () => {
    onChange(value.add(1, "month"));
  };

  return (
    <div className="">
      <div className="d-flex justify-content-between pt-4 pl-4 pr-4 pb-3">
        <LeftOutlined onClick={handlePrevMonth} style={{ cursor: "pointer" }} />
        <span
          style={{ fontSize: "18.57px", fontWeight: "500", marginTop: "-5px" }}
        >
          {headerText}
        </span>
        <RightOutlined
          onClick={handleNextMonth}
          style={{ cursor: "pointer" }}
        />
      </div>
    </div>
  );
}

function DefaultDashboard() {
  const dispatch = useDispatch();
  const carouselRef = useRef(null);
  const { direction } = useSelector((state) => state.theme);
  // const { GradeLevelResult } = useSelector((state) => state[GRADE_LEVEL]);
  const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);

  const {
    calendarEvents,
    studentBirthdays,
    studentBirthdayLoading,
    googleClassroomStatusLoading,
    calendarEventLoading,
    googleClassroomStatus,
    counts,
    countLoading,
    enrollmentArray,
    enrollmentLoading,
    monthlyAttendance,
    attendanceLoading,
    openDrawer3,
    openDrawer4,
  } = useSelector((state) => state[DASHBOARD]);

  useEffect(() => {
    fetchRecord();
  }, [dispatch]);

  const handleGoogleAuthPopupOk = () => {
    // Replace the following URL with the actual Google Classroom authentication URL
    const googleAuthUrl =
      "https://sunfish-proven-akita.ngrok-free.app/api/auth/google"; // Replace with your actual URL

    // Open a new window for Google authentication
    const authWindow = window.open(googleAuthUrl, "_self");

    if (authWindow) {
      const checkWindowClosed = setInterval(() => {
        if (authWindow.closed) {
          clearInterval(checkWindowClosed);
        }
      }, 1000);
    }
  };

  const location = useLocation();
  const token = new URLSearchParams(location.search).get("token");

  useEffect(() => {
    if (token) {
      dispatch(saveGoogleToken({ googleToken: token }));
    }
  }, [dispatch, token]);
  const fetchRecord = async () => {
    // await dispatch(getSchoolYear({ perPage: 1000 }));
    // await dispatch(getGradeLevel({ perPage: 1000 }));
    await dispatch(getCalendarActivity({ type: "upcoming" }));
    await dispatch(getCounts());
    await dispatch(getEnrollmentData({ id: 0 }));
    await dispatch(getMonthlyAttendance({ id: 0 }));
    // await dispatch(getStudentBirthdays());
    await dispatch(getGoogleClassroomStatus());
  };

  const onChangeYear = (value) => {
    dispatch(getEnrollmentData({ id: value }));
  };

  const onChangeMonth = (value) => {
    dispatch(getMonthlyAttendance({ month: value }));
  };

  const onSearch = (value) => {
    // console.log("search:", value);
  };

  const safeEnrollmentSeries = cloneDeep(enrollmentArray?.series ?? []);
  const safeEnrollmentCategories = cloneDeep(enrollmentArray?.categories ?? []);


  const safeMonthlyAttendanceData = useMemo(() => cloneDeep(monthlyAttendance?.data ?? []), [monthlyAttendance]);
  const safeMonthlyAttendanceCategories = useMemo(() => cloneDeep(monthlyAttendance?.categories ?? []), [monthlyAttendance]);

  const state4 = {
    series: safeMonthlyAttendanceData,
    options: {
      chart: {
        stacked: true,
        toolbar: { show: false },
        zoom: { enabled: false }
      },
      colors: ["#04D182", "#C1444A", "#FFC107", "#FD7C05", "#3E82F7"],
      responsive: [{
        breakpoint: 480,
        options: {
          legend: {
            position: "bottom",
            offsetX: -10,
            offsetY: 0,
          },
        },
      }],
      plotOptions: { bar: { horizontal: false } },
      xaxis: {
        type: "category",
        categories: safeMonthlyAttendanceCategories,
        tickAmount: 6,
      },
      legend: {
        position: "bottom",
        offsetY: 10,
        scrollX: false
      },
      fill: { opacity: 1 },
    },
  };


  // Calendars Events

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState([]);

  const dateCellRender = (date) => {
    const eventsForDate = calendarEvents?.events?.filter(
      (event) => event.date === date.format("YYYY-MM-DD")
    );

    if (!eventsForDate || eventsForDate.length === 0) {
      return null; // No events for this date
    }

    return (
      <div className="event-container" style={{ marginTop: "-32px" }}>
        <Badge
          style={{ opacity: "0.4" }}
          status="success"
          text={""}
          onClick={() => handleEventClick(eventsForDate)}
        />
      </div>
    );
  };
  const handleEventClick = (event) => {
    console.log(event);
    setSelectedEvent(event);
    setModalVisible(true);
  };

  const handleModalClose = () => {
    setModalVisible(false);
  };

  const disabledDate = (current) => {
    // Get the current date
    const currentDate = moment();

    const validSchoolYears = SchoolYearResult?.data?.filter((item) => {
      const start = moment(item.start_date);
      const end = moment(item.end_date);
      return currentDate >= start && currentDate <= end;
    });

    // Check if current date is within the valid school years
    return !validSchoolYears.some((item) => {
      const start = moment(item.start_date);
      const end = moment(item.end_date);
      return current >= start && current <= end;
    });
  };

  const handlePrev = () => {
    if (carouselRef.current) {
      carouselRef.current.prev();
    }
  };

  const handleNext = () => {
    if (carouselRef.current) {
      carouselRef.current.next();
    }
  };

  const handleCalenderEventsChange = async (value) => {
    await dispatch(getCalendarActivity({ type: value }));
  };

  return (
    <>
      <Row gutter={16} style={{ marginBottom: "-6px" }}>
        <Col xs={24} sm={24} md={24} lg={24} className="pl-2 pr-2">
          <Card className="dashboard-header-card ml-1">
            <Row gutter={16}>
              <Skeleton loading={countLoading} active>
                {counts
                  ? counts.map((list) => (
                    <Col
                      key={list.id}
                      span={4}
                      className="card-main-dashboard"
                    >
                      <div style={{ padding: 0 }}>
                        <h3>{list.title}</h3>
                        {list?.cards.map((card) => (
                          <Link key={card.id} to={`../../${card.link}`}>
                            <div style={{ marginBottom: "10px" }}>
                              <StatisticWidget
                                title={card.title.toString()}
                                style={{ textAlign: "center" }}
                                value={card.value.toString()}
                                image={card.img.toString()}
                              />
                            </div>
                          </Link>
                        ))}
                      </div>
                    </Col>
                  ))
                  : ""}
              </Skeleton>
            </Row>
          </Card>
        </Col>
      </Row>
      <Row gutter={16} style={{ marginBottom: "-6px" }}>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={16}
          xl={16}
          xxl={16}
          className="pl-2 pr-2"
        >
          <Row gutter={16}>
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={24}
              xl={24}
              xxl={24}
              className="ml-1"
            >
              <Card className="enrolllment-dash">
                <div className="drawer-dash">
                  <div className="d-flex justify-content-between">
                    <h2 className="mx-2 my-2">{setLocale("Enrollments")}</h2>
                    <div>
                      <Button
                        className="dasboard-filter mx-2 my-2"
                        onClick={() => dispatch(setOpenDrawer3(true))}
                      >
                        <span className="text">{setLocale("Year")}</span>
                      </Button>
                    </div>
                    <Drawer
                      className="d-flex justify-content-between main-drawer"
                      title={setLocale("Select Year")}
                      placement="right"
                      closable={true}
                      onClose={() => dispatch(setOpenDrawer3(false))}
                      open={openDrawer3}
                      getContainer={false}
                    >
                      <Select
                        showSearch
                        placeholder={setLocale("Select year")}
                        optionFilterProp="children"
                        onChange={onChangeYear}
                        onSearch={onSearch}
                        filterOption={(input, option) =>
                          (option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        allowClear
                        className="dashboard-select"
                        options={SchoolYearResult.data ?? []}
                      />
                    </Drawer>
                  </div>

                  <Skeleton active loading={enrollmentLoading}>
                    <ChartWidget
                      card={false}
                      series={safeEnrollmentSeries}
                      xAxis={safeEnrollmentCategories}
                      height={300}
                      type="bar"
                      customOptions={{ colors: "#0357E6" }}
                      direction={direction}
                    />
                  </Skeleton>
                </div>
              </Card>
            </Col>
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={24}
              xl={24}
              xxl={24}
              className="ml-1"
              style={{ marginTop: "-8px" }}
            >
              <Card className="monthly-dash">
                <div className="drawer-dash-monthly">
                  <div className="d-flex justify-content-between">
                    <h2 className="mx-2 my-2">
                      {setLocale("Monthly Attendance")}
                    </h2>
                    <div>
                      <Button
                        className="dasboard-filter mx-2 my-2"
                        onClick={() => dispatch(setOpenDrawer4(true))}
                      >
                        <span className="text">{setLocale("Month")}</span>
                      </Button>
                    </div>
                    <Drawer
                      className="d-flex justify-content-between main-drawer"
                      title="Select Month"
                      placement="right"
                      closable={true}
                      onClose={() => dispatch(setOpenDrawer4(false))}
                      open={openDrawer4}
                      getContainer={false}
                    >
                      <DatePicker.MonthPicker
                        onChange={onChangeMonth}
                        disabledDate={disabledDate}
                        format={DATE_FORMAT_YYYY_MM}
                        className="rounded-0 w-100"
                      />
                    </Drawer>
                  </div>
                  <Skeleton loading={attendanceLoading} active>
                    <Chart
                      options={state4.options}
                      series={state4.series}
                      type="bar"
                      height={250}
                    />
                  </Skeleton>
                </div>
              </Card>
            </Col>
          </Row>
        </Col>
        <Col xs={24} sm={24} md={24} lg={8} xl={8} xxl={8}>
          <Row gutter={16}>
            {/* ye wala calender */}
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={24}
              xl={24}
              xxl={24}
              style={{ marginBottom: "21px" }}
            >
              <div className="dash_calendar">
                <Skeleton active loading={calendarEventLoading}>
                  <Calendar
                    fullscreen={false}
                    onPanelChange={onPanelChange}
                    dateCellRender={dateCellRender}
                    headerRender={(params) => (
                      <CustomHeader
                        value={params.value}
                        type={params.type}
                        onChange={params.onTypeChange}
                      />
                    )}
                  />

                  <Modal
                    title={setLocale("Event Details")}
                    open={modalVisible}
                    onCancel={handleModalClose}
                    footer={null}
                  >
                    <div className="calendar-list">
                      {selectedEvent.map((eventItem, i) => (
                        <div
                          key={`${eventItem.date}-${i}`}
                          className="calendar-list-item mb-20"
                          style={{ marginBottom: "20px" }}
                        >
                          <div className="d-flex">
                            <Badge className="mr-2" color={eventItem.color} />
                            <div>
                              <h5 className="mb-1">{eventItem.description}</h5>
                              <span className="text-muted">
                                {eventItem.details}{" "}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Modal>
                </Skeleton>
              </div>
            </Col>
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={24}
              xl={24}
              xxl={24}
              style={{ marginTop: "-8px" }}
            >
              <Card
                className="scrollable-content"
                style={{ borderRadius: "30px" }}
              >
                <div>
                  <h2 className="mx-2 my-2">{setLocale("Events")}</h2>
                  <Select
                    defaultValue="upcoming"
                    style={{
                      width: 160,
                    }}
                    onChange={handleCalenderEventsChange}
                    options={[
                      {
                        value: "upcoming",
                        label: "Upcoming Events",
                      },
                      {
                        value: "all",
                        label: "All Events",
                      },
                    ]}
                  />
                  <Skeleton active loading={calendarEventLoading}>
                    <List
                      dataSource={calendarEvents?.importantDays ?? []}
                      renderItem={(item) => (
                        <List.Item>
                          <span className="item-date">{item.event}</span>
                          <span
                            className="item-event"
                            style={{ marginLeft: "8px" }}
                          >
                            {item.date}
                          </span>
                        </List.Item>
                      )}
                    />
                  </Skeleton>
                </div>
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
      {googleClassroomStatus === true && (
        <Row gutter={16} style={{ marginBottom: "-6px" }}>
          <Col xs={24} sm={24} md={24} lg={24} className="pl-2 pr-2">
            <Button style={{ width: "100%" }} onClick={handleGoogleAuthPopupOk}>
              {setLocale("Authenticate with Google Classroom")}
            </Button>
          </Col>
        </Row>
      )}
    </>
  );
}

export default DefaultDashboard;

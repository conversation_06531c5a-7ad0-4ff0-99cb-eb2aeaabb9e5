import React, { useState, useEffect } from "react";
import {
  Spin,
  Col,
  Row,
  Card,
  Avatar,
  Skeleton,
  Breadcrumb,
  Tabs,
  Menu,
} from "antd";
import { useSelector, useDispatch } from "react-redux";
import { Icon } from "components/util-components/Icon";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
  ArrowLeftOutlined,
  ContainerOutlined,
  EditOutlined,
  MailOutlined,
  EllipsisOutlined,
  ReadOutlined,
  MessageOutlined
} from "@ant-design/icons";
import {
  viewManageClass,
  ManageClassEditWithDrawerStatus,
  ManageConversationDrawerStatus,
  ManageErsDrawerStatus,
  ClassOverviewDrawerStatus,
  ClassCourseScheduleDrawerStatus,
  classCourseFilter,
  classStudentFilter,
  AttendanceDrawerStatus,
  setGroupChatDrawer
} from "store/slices/ManageClass/manageManageClassSlice.js";
import ClassOverview from "./ClassOverview";
import AddClassModel from "./index";
import ConversationModel from "./ConversationModel";
import ErsModel from "./ErsModel";
import ClassCourseScheduleModel from "./ClassCourseScheduleModel";
import OnceDayAttendance from "./../../Attendance/OnceDayAttendance";
import Flex from "components/shared-components/Flex";
import IntlMessage from "components/util-components/IntlMessage";
import { MANAGE_CLASS } from "constants/AppConstants";
import GroupChatDrawer from "./GroupChatDrawer";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function View() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const params = useParams();
  const {
    ConversationAddDrawer,
    classStudentSearchString,
    classCourseSearchString,
    AttendanceDrawer,
    ClassOverviewDrawer,
    classCourseScheduleDrawer,
    ManageClassAddDrawer,
    ViewManageClassData,
    course_schedule,
    ViewManageClassLoader,
    permission,
    addGroupChatDrawer
  } = useSelector((state) => state[MANAGE_CLASS]);

  useEffect(() => {
    const fetchData = async () => {
      await dispatch(viewManageClass(params?.id));
      if (classCourseSearchString) {
        await dispatch(
          classCourseFilter({
            id: ViewManageClassData?.id,
            search: classCourseSearchString,
          })
        );
      }
      if (classStudentSearchString) {
        await dispatch(
          classStudentFilter({
            id: ViewManageClassData?.id,
            search: classStudentSearchString,
          })
        );
      }
    };
    active_state();
    fetchData();
  }, []);

  const items = [
    {
      key: "overview",
      label: `Overview`,
      children: ClassOverviewDrawer && <ClassOverview />,
    },
    ViewManageClassData?.teacherCanMarkAttendances && permission.includes("View Class Attendance")
      ? {
        key: "attendance",
        label: `Attendance`,
        children: AttendanceDrawer && <OnceDayAttendance />,
      }
      : "",
    permission.includes("View Class Schedule")
      ? {
        key: "schedule",
        label: `Schedule`,
        children: classCourseScheduleDrawer && <ClassCourseScheduleModel />,
      }
      : "",
  ];

  const onMenuClick = (e) => {
    if (e.key === "edit") {
      dispatch(ManageClassEditWithDrawerStatus(ViewManageClassData));
    }
    if (e.key === "email") {
      dispatch(
        ManageConversationDrawerStatus({
          status: true,
          data: ViewManageClassData,
        })
      );
    }
    if (e.key === "ers") {
      dispatch(
        ManageErsDrawerStatus({
          status: true,
          data: ViewManageClassData,
        })
      );
    }
    if (e.key === "groupChat") {
      dispatch(setGroupChatDrawer({ status: true }));
    }
  };

  const getItem = (label, key, icon, children, type) => {
    return {
      key,
      icon,
      children,
      label,
      type,
    };
  };

  const btnItems = [
    getItem("", "sub1", <EllipsisOutlined />, [
      permission.includes("Update")
        ? getItem("Edit CLass", "edit", <EditOutlined />)
        : "",
      permission.includes("Send Class Email")
        ? getItem("Send Email", "email", <MailOutlined />)
        : "",
      permission.includes("Group Chat")
        ? getItem("Group Chat", "groupChat", <MessageOutlined />)
        : "",
      getItem("ERS", "ers", <ReadOutlined />),
    ]),
  ];

  const menu =
    ViewManageClassData?.teacherCanMarkAttendances && ViewManageClassData?.totalDayLefts > 0 ? (
      <>
        <Menu
          onClick={onMenuClick}
          mode="horizontal"
          style={{ fontSize: "12px", background: "transparent", border: "0px" }}
          items={btnItems}
        ></Menu>
      </>
    ) : null;

  const onTabChange = (key) => {
    if (key === "attendance") {
      dispatch(AttendanceDrawerStatus(true));
      dispatch(ClassOverviewDrawerStatus(false));
    }
    if (key === "schedule") {
      dispatch(
        ClassCourseScheduleDrawerStatus({
          status: true,
          data: ViewManageClassData?.courseSchedule,
        })
      );
    }
    if (key === "overview") {
      active_state();
    }
  };

  const active_state = () => {
    dispatch(AttendanceDrawerStatus(false));
    dispatch(ClassOverviewDrawerStatus(true));
  };

  return (
    <>
      <Breadcrumb className="my-2 mx-2">
        <Breadcrumb.Item>
          <Link to="/app/default">{setLocale("home")}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{setLocale("Manage Class")}</Breadcrumb.Item>
      </Breadcrumb>
      {permission.includes("Send Class Email")
        ? ConversationAddDrawer && <ConversationModel className="float-right" />
        : null}
      {permission.includes("Update")
        ? ManageClassAddDrawer && <AddClassModel className="float-right" />
        : null}
      <ErsModel className="float-right" />
      {addGroupChatDrawer && <GroupChatDrawer />}
      <Card
        loading={ViewManageClassLoader}
        type="inner"
        title={
          <h5>
            <span style={{ cursor: "pointer" }} onClick={() => navigate(-1)}>
              <ArrowLeftOutlined /> {`${ViewManageClassData.name ?? ""}`}
            </span>{" "}
          </h5>
        }
        extra={menu}
        style={{
          width: "100%",
        }}
      >
        <div className="example">
          <div>
            <Row gutter={16}>
              <Col span={24}>
                <Tabs
                  onTabClick={onTabChange}
                  defaultActiveKey="overview"
                  items={items}
                />
              </Col>
            </Row>
          </div>
        </div>
      </Card>
    </>
  );
}
export default View;

import { Col, Pagination, Row } from "antd";
import { LEAVE_ALLOCATION } from "constants/AppConstants";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import LeaveWidget from "./ui/Widget";
import LoadingSkeleton from "./ui/LoadingSkeleton";
import { LeaveAllocationEditWithDrawerStatus, getLeaveAllocation } from "store/slices/LeaveAllocation/manageLeaveAllocationSlice.js";

export const AllocationDashboard = () => {
  const dispatch = useDispatch();

  const {
    LeaveAllocationResult,
    LeaveAllocationTableLoading,
    tablePagination, // Add pagination state
    filter, // Add filter state
    sorting, // Add sorting state
  } = useSelector((state) => state[LEAVE_ALLOCATION]);

  const handleEditClick = (leaveData) => {
    dispatch(
      LeaveAllocationEditWithDrawerStatus({ errorStatus: 1, data: leaveData })
    );
  };

  // Add pagination handler
  const handlePageChange = (page, pageSize) => {
    dispatch(
      getLeaveAllocation({
        page: page,
        perPage: pageSize,
        filter: filter,
        sorting: sorting,
      })
    );
  };

  const renderContent = () => {
    if (LeaveAllocationTableLoading) {
      return (
        <Row gutter={[16, 18]}>
          {[1, 2, 3, 4].map((item) => (
            <Col xs={24} sm={24} md={12} key={item}>
              <LoadingSkeleton />
            </Col>
          ))}
        </Row>
      );
    }

    // if (error) {
    //   return <Alert type="error" message={error} />;
    // }

    return (
      <>
        <Row gutter={16}>
          {LeaveAllocationResult?.data?.map((elm, i) => (
            <Col xs={24} sm={24} md={12} lg={8} key={i}>
              <LeaveWidget
                leaveData={elm}
                setIsModalVisible={false}
                onEdit={() => handleEditClick(elm)}
              />
            </Col>
          ))}
        </Row>
        <Row justify="end" style={{ marginTop: "24px" }}>
          <Pagination
            current={tablePagination.current}
            pageSize={tablePagination.pageSize}
            total={tablePagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) =>
              `${range[0]}-${range[1]} of ${total} Records`
            }
            pageSizeOptions={["10", "20", "50", "100", "1000"]}
            onChange={handlePageChange}
          />
        </Row>
      </>
    );
  };

  return <>{renderContent()}</>;
};

import { Col, Row } from "antd";
import { LEAVE_ALLOCATION } from "constants/AppConstants";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import LeaveWidget from "./ui/Widget";
import LoadingSkeleton from "./ui/LoadingSkeleton";
import { LeaveAllocationEditWithDrawerStatus } from "store/slices/LeaveAllocation/manageLeaveAllocationSlice.js";
export const AllocationDashboard = () => {
  const dispatch = useDispatch();

  const {
    LeaveAllocationResult,
    LeaveAllocationTableLoading,
    permission,
  } = useSelector((state) => state[LEAVE_ALLOCATION]);
  const handleEditClick = (leaveData) => {
    debugger;
    dispatch(
      LeaveAllocationEditWithDrawerStatus({ errorStatus: 1, data: leaveData })
    );
  };

  const renderContent = () => {
    if (LeaveAllocationTableLoading) {
      return (
        <Row gutter={[16, 18]}>
          {[1, 2, 3, 4].map((item) => (
            <Col xs={24} sm={24} md={12} key={item}>
              <LoadingSkeleton />
            </Col>
          ))}
        </Row>
      );
    }

    // if (error) {
    //   return <Alert type="error" message={error} />;
    // }

    return (
      <Row gutter={16}>
        {LeaveAllocationResult?.data?.map((elm, i) => (
          <Col xs={24} sm={24} md={12} key={i}>
            <LeaveWidget
              leaveData={elm}
              setIsModalVisible={false}
              onEdit={() => handleEditClick(elm)}
            />
          </Col>
        ))}
      </Row>
    );
  };

  return <>{renderContent()}</>;
};

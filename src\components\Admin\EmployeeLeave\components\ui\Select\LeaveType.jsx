import { Select, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Loading from "components/shared-components/Loading";
import { getLeaveType } from "store/slices/LeaveType/manageLeaveTypeSlice";
import { LEAVE_TYPE } from "constants/AppConstants";

export const LeaveType = ({ value, onChange, disabled, showAll, leaveBalances = [] }) => {
  const dispatch = useDispatch();
  const { LeaveTypeResult, LeaveTypeTableLoading } = useSelector((state) => state.LeaveType);
  const [dataFetched, setDataFetched] = useState(false);

  useEffect(() => {
    // Only fetch data if it hasn't been fetched yet and result is empty
    if (!dataFetched && (!LeaveTypeResult || !LeaveTypeResult.data)) {
      dispatch(getLeaveType());
      setDataFetched(true);
    }
  }, [dispatch, LeaveTypeResult, dataFetched]);

  const { Option } = Select;

  const balanceMap = leaveBalances.reduce((acc, item) => {
    acc[item.leave_type_id] = item.balance;
    return acc;
  }, {});

  // if (LeaveTypeTableLoading) return <Loading />;

  return (
    <Select
      placeholder="Leave Type"
      style={{ minWidth: 120, width: "100%" }}
      onChange={onChange}
      value={value}
      disabled={disabled}
      loading={LeaveTypeTableLoading}
    >
      {LeaveTypeResult?.data?.map((leave) => {
        const balance = balanceMap[leave.id] ?? null;
        const isDisabled = balance === 0;

        const optionNode = (
          <Option
            key={leave.id}
            value={leave.id}
            disabled={isDisabled}
            loading={LeaveTypeTableLoading}
            // style={{ color: isDisabled ? "red" : "inherit" }}
            title={
              isDisabled
                ? "Leave limit exceeded. Contact admin"
                : `Balance: ${balance}`
            } // Using title attribute for tooltip
          >
            <Tooltip
              title={
                isDisabled ? "No balance available" : `Balance: ${balance}`
              }
            >
              {leave.name}
            </Tooltip>
          </Option>
        );

        return optionNode;
      })}
    </Select>
  );
};

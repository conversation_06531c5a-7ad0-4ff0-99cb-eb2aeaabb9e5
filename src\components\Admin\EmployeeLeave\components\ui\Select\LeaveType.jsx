import { Select, Tooltip } from "antd";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Loading from "components/shared-components/Loading";
import { getLeaveType } from "store/slices/LeaveType/manageLeaveTypeSlice";
import { LEAVE_TYPE } from "constants/AppConstants";

export const LeaveType = ({ value, onChange, disabled, showAll=false }) => {
  const dispatch = useDispatch();
  const [dataFetched, setDataFetched] = React.useState(false);

  const { LeaveTypeAddDrawer,LeaveTypeErrors, LeaveTypeResult,ViewLeaveTypeLoader, tablePagination, sorting, filter, LeaveTypeTableLoading, permission } = useSelector(
        (state) => state[LEAVE_TYPE]
        );
  const leaveBalances = useSelector(
    (state) => state.leaves?.balances?.data || []
  );

  const getModuleData = React.useCallback(async (page, perPage, filterData, sortingData) => {
    if (!dataFetched) {
      await dispatch(
        getLeaveType({
          page: page,
          perPage: perPage,
          filter: filterData,
          sorting: sortingData,
          showAll: showAll
        })
      );
      setDataFetched(true);
    }
  }, [dispatch, showAll, dataFetched]);

  useEffect(() => {
    // Only fetch data if we don't have any data yet and haven't fetched before
    if (!dataFetched && (!LeaveTypeResult?.data || LeaveTypeResult.data.length === 0)) {
      getModuleData(1, 100, {}, {});
    }
  }, [getModuleData, LeaveTypeResult?.data, dataFetched]);

  const { Option } = Select;

  const balanceMap = leaveBalances.reduce((acc, item) => {
    acc[item.leave_type_id] = item.balance;
    return acc;
  }, {});

  // if (LeaveTypeTableLoading) return <Loading />;

  return (
    <Select
      placeholder="Leave Type"
      style={{ minWidth: 120, width: "100%" }}
      onChange={onChange}
      value={value}
      disabled={disabled}
      loading={LeaveTypeTableLoading}
    >
      {LeaveTypeResult?.data?.map((leave) => {
        const balance = balanceMap[leave.id] ?? null;
        const isDisabled = balance === 0;

        const optionNode = (
          <Option
            key={leave.id}
            value={leave.id}
            disabled={isDisabled}
            loading={LeaveTypeTableLoading}
            // style={{ color: isDisabled ? "red" : "inherit" }}
            title={
              isDisabled
                ? "Leave limit exceeded. Contact admin"
                : `Balance: ${balance}`
            } // Using title attribute for tooltip
          >
            <Tooltip
              title={
                isDisabled ? "No balance available" : `Balance: ${balance}`
              }
            >
              {leave.name}
            </Tooltip>
          </Option>
        );

        return optionNode;
      })}
    </Select>
  );
};

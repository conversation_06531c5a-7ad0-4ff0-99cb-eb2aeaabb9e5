import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, DatePicker, Checkbox, Select, Skeleton } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CALENDAR_CATEGORY, CALENDAR_EVENT, SCHOOL_YEAR } from "constants/AppConstants";
import {
  CalendarEventAddDrawerStatus,
  createCalendarEvent,
  getCalendarEvent,
  setColorPicker,
  onCloseError
} from "store/slices/CalendarEvent/manageCalendarEventSlice.js";
import { getSchoolYear } from "store/slices/SchoolYear/manageSchoolYearSlice";
import { getCalendarCategory } from "store/slices/CalendarCategory/manageCalendarCategorySlice";
import IntlMessage from "components/util-components/IntlMessage";
import moment from "moment";
import 'moment-timezone';
import { DATE_FORMAT_YYYY_MM_DD } from 'constants/DateConstant';
import { DEFAULT_YEAR } from 'constants/AuthConstant';
const defaultYear = JSON.parse(localStorage.getItem(DEFAULT_YEAR));
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { RangePicker } = DatePicker;


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [startDate, setStartDate] = useState(null);
  const [checked, setChecked] = useState(true);
  const [endDate, setEndDate] = useState(null);
  const [pageLoading, setPageLoading] = useState(true);
  const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);
  const { DrawerStatus, sorting, filter, colorPicker, CalendarEventAddDrawer, CalendarEventButtonAndModelLabel, CalendarEventErrors, CalendarEventShowMessage, CalendarEventButtonSpinner, CalendarEventEditData, tablePagination } = useSelector(
    (state) => state[CALENDAR_EVENT]
  );

  const { CalendarCategoryResult } = useSelector(
    (state) => state[CALENDAR_CATEGORY]
  );

  const onClose = () => {
    dispatch(CalendarEventAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      await dispatch(getSchoolYear({ perPage: 1000 })).then((response) => {
        const schoolYear = defaultYear;
        setStartDate(moment(schoolYear?.start_date));
        setEndDate(moment(schoolYear?.end_date));
        setPageLoading(false);
      });
      await dispatch(getCalendarCategory({ perPage: 1000 }));
    } catch (error) {
      console.error('Error fetching school year data:', error);
    }

  };

  const onSubmit = async (formValues) => {

    if (CalendarEventEditData && CalendarEventEditData.id) {
      // If editing, include the id in the form values
      formValues.id = CalendarEventEditData.id;
    }

    await dispatch(createCalendarEvent(formValues))
  };
  useEffect(() => {
    if (Object.keys(CalendarEventErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getCalendarEvent({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [CalendarEventErrors]);
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const [color, setColor] = useState('');

  const handleColorChange = (e) => {
    // setColor(e.target.value);
    dispatch(setColorPicker(e.target.value));

  };
  const disabledDate = (current) => {
    // Can not select days after today and before start Date
    const start = moment(startDate, "YYYY-MM-DD").add(-1, "days");
    const end = moment(endDate, "YYYY-MM-DD").add(1, "days");
    return current <= start || current >= end;
  };

  const eventType = [
    {
      value: 'Public',
      label: 'Public',
    },
    {
      value: 'Internal',
      label: 'Internal',
    },
  ]

  const snooze = [
    {
      value: '10',
      label: '10 minutes',
    },
    {
      value: '30',
      label: '30 minutes',
    },
    {
      value: '60',
      label: '1 Hour',
    },
  ]


  return (
    <>

      <Drawer
        title={CalendarEventButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CalendarEventAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Skeleton active loading={pageLoading}>
          <Form
            layout="vertical"
            onFinish={onSubmit}
            form={form}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            initialValues={{
              ...CalendarEventEditData,
              is_whole_day: CalendarEventEditData && Object.keys(CalendarEventEditData).length > 0 ? (CalendarEventEditData?.is_whole_day === 1 ? true : false) : true,

              eventStartEndDate: CalendarEventEditData && CalendarEventEditData ?
                [
                  moment(CalendarEventEditData?.start_date),
                  moment(CalendarEventEditData?.end_date)
                ]
                : [
                  moment(),
                  moment()
                ],

            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={setLocale('name')}
                  rules={[
                    {
                      required: true,
                      message: setLocale('nameError'),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.name ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.name}
                >
                  <Input />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="eventStartEndDate"
                  label={setLocale("calendarEvent.date")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("calendarEvent.date_error"),
                    },
                    {
                      validator: (_, value) => {
                        const [start, end] = value;
                        if (!start || !end) {
                          return Promise.resolve(); // No validation if start or end date is not selected
                        }

                        // Add your validation logic here based on the school year dates
                        const isValidRange = start >= moment(startDate, "HH:mm") && end <= moment(endDate, "HH:mm");

                        return isValidRange
                          ? Promise.resolve()
                          : Promise.reject('Selected date range is not within the valid school year.');
                      },
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.eventStartEndDate ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.eventStartEndDate}
                >
                  <RangePicker
                    disabledDate={disabledDate}
                    format={DATE_FORMAT_YYYY_MM_DD}
                    className="rounded-0"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="calendar_category_id"
                  label={setLocale("calendarEvent.category")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("calendarEvent.category_error"),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.calendar_category_id ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.calendar_category_id}
                >
                  <Select
                    className="rounded-0"
                    showSearch
                    optionLabelProp="label"
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    }
                    options={CalendarCategoryResult?.data ?? []}
                  />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="event_type"
                  label={setLocale("calendarEvent.event_type")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("calendarEvent.event_type_error"),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.event_type ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.event_type}
                >
                  <Select
                    className="rounded-0"
                    showSearch
                    optionLabelProp="label"
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    }
                    options={eventType ?? []}
                  />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="snooze"
                  label={setLocale("calendarEvent.snooze")}
                  rules={[
                    {
                      required: false,
                      message: setLocale("calendarEvent.snooze_error"),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.snooze ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.snooze}
                >
                  <Select
                    className="rounded-0"
                    showSearch
                    optionLabelProp="label"
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    }
                    options={snooze ?? []}
                  />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="color_code"
                  style={{ marginTop: "25px" }}
                  label={setLocale("calendarEvent.color_code")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("calendarEvent.color_code_error"),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.color_code ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.color_code}
                >
                  <Form.Item
                    name="color_code"
                    noStyle
                    getValueFromEvent={(e) => e.target.value}
                    setValueFromEvent={(value) => value}
                  >
                    <input type="color" id="colorPicker" onChange={handleColorChange} style={{ backgroundColor: colorPicker, borderRadius: '9px' }} />
                  </Form.Item>

                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="is_operational_day"
                  style={{ marginTop: "25px" }}
                  label={' '}
                  valuePropName="checked"
                  rules={[
                    {
                      required: false,
                      message: setLocale("calendarEvent.is_operational_day_error"),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.is_operational_day ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.is_operational_day}
                >
                  <Checkbox>{setLocale("calendarEvent.is_operational_day")}</Checkbox>
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="is_whole_day"
                  style={{ marginTop: "25px" }}
                  label={' '}
                  valuePropName="checked"
                  // initialValue={checked}
                  rules={[
                    {
                      required: false,
                      message: setLocale("calendarEvent.is_whole_day_error"),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.is_whole_day ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.is_whole_day}
                >
                  <Checkbox>{setLocale("calendarEvent.is_whole_day")}</Checkbox>
                </Form.Item>
              </Col>

            </Row>
            <Row>

              <Col span={24}>
                <Form.Item
                  name="description"
                  style={{ marginTop: "25px" }}
                  label={setLocale("calendarEvent.description")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("calendarEvent.description_error"),
                    },
                  ]}
                  validateStatus={CalendarEventShowMessage && CalendarEventErrors.description ? "error" : ""}
                  help={CalendarEventShowMessage && CalendarEventErrors.description}
                >
                  <Input.TextArea rows={4} />
                </Form.Item>
              </Col>

            </Row>

            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={CalendarEventButtonSpinner}
              >
                {CalendarEventButtonAndModelLabel}
              </Button>
              <Button onClick={onClose}>{setLocale('Cancel')}</Button>
            </Space>
          </Form>
        </Skeleton>

      </Drawer>
    </>
  );
};
export default Index;


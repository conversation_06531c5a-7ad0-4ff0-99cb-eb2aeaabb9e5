import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { Row, Form, Col, But<PERSON>, Card, DatePicker, Select, Skeleton } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import {
    getAttendanceByStatus,
    makeNullattendanceByStatus,
    changeReportView,
    updateSortFilters
} from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import AttendanceReportTableView from './ReportView/AttendanceReportTableView'
import AttendanceReportRegisterView from './ReportView/AttendanceReportRegisterView'
import IntlMessage from 'components/util-components/IntlMessage';
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
import { USER_INFORMATION } from 'constants/AuthConstant';
import OrganizationSelect from "components/Admin/OrganizationDropdown/OrganizationSelect";
import { getManageClass } from 'store/slices/ManageClass/manageManageClassSlice';
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { RangePicker } = DatePicker;
const { Option } = Select;


function AttendanceByStatusReport(props) {
    const { classes, attCodes } = props;
    const [defaultDate, setDefaultDate] = useState([moment().subtract(30, 'days'), moment()]);
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [reportLoad, setReportLoad] = useState(false);
    const [attView, setAttView] = useState('table');
    const [reportName, setReportName] = useState(null);

    // const { attendanceByStatus } = useSelector(state => state.reportsSlice);
    const { attendanceByStatus,
        filter,
        sorting,
        tablePagination
    } = useSelector((state) => state[ATTENDANCE_REPORT]);

    useEffect(() => {
        /**this is just to clear previous data  */
        dispatch(makeNullattendanceByStatus());
    }, []);

    const onFinish = (values) => {

        setReportLoad(true)
        const dateRange = values['date'];
        /** make date formate */
        values.from_date = moment(dateRange[0]).format("YYYY-MM-DD")
        values.end_date = moment(dateRange[1]).format("YYYY-MM-DD")

        const formData = {
            from_date: values.from_date,
            end_date: values.end_date,
            class: values.class,
            attendance_code_id: values.attendance_code_id,
            attview: values.attview,
            organization_id: filter?.organization_id ?? selectedOrganization
        }
        /**set report name  */
        setReportName(`Attendance Report By Status ${values.from_date}-To-${values.end_date}`);

        /** class to classWiseAbsentees report */
        dispatch(getAttendanceByStatus(formData)).then((result) => {
            setReportLoad(false)
        });
    }
    const reportViewChange = () => {
        dispatch(changeReportView())
    }

    const onChangeOrganization = (value) => {
        dispatch(getManageClass({ organization_id: value })).then((result) => {
            // setPageLoad(false)
        });
    }

    return (
        <>
            <Row gutter={12} className='absentees-report'>
                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                    <div className='px-2 py-2'>
                        <Form layout="vertical" onFinish={onFinish} form={form} initialValues={{ attview: 'table', date: defaultDate }}>
                            <Row gutter={12} >
                                <Col xs={24} sm={24} md={3} lg={3} xl={3} className='px-2'>
                                    <Form.Item label={setLocale("attendance_report.attendance_view")} name="attview" >
                                        <Select onChange={(e) => { setAttView(e); reportViewChange(); }} optionLabelProp="label">
                                            <Option value={'table'} label={setLocale('attendance_report.table_view')}>{setLocale('attendance_report.table_view')}</Option>
                                            <Option value={'register'} label={setLocale('attendance_report.register_view')}>{setLocale('attendance_report.register_view')}</Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                                {userOrganizations.length > 1 &&
                                    <>
                                        <Col xs={24} sm={24} md={4} lg={4} xl={4} >
                                            <Form.Item
                                                name="organization_id"
                                                label={setLocale('Organization')}
                                                rules={[
                                                    {
                                                        required: false,
                                                        message: setLocale('Please Select Class'),
                                                    },
                                                ]}
                                            >
                                                <OrganizationSelect
                                                    updateSortFilters={updateSortFilters}
                                                    filter={filter}
                                                    onChange={onChangeOrganization}
                                                    sorting={sorting}
                                                    tablePagination={tablePagination}
                                                    getModuleData={onFinish}
                                                    runUseEffect={false}
                                                    dropStyle={true}
                                                />
                                            </Form.Item>
                                        </Col>
                                    </>
                                }

                                <Col xs={24} sm={24} md={6} lg={6} xl={6} className='px-2'>
                                    <Form.Item name="date"
                                        label={setLocale("attendance_report.date_from_to")}
                                        rules={[
                                            {
                                                required: true,
                                                message: setLocale('attendance_report.daterange_error'),
                                            },
                                        ]}>
                                        <RangePicker className='w-100' />
                                    </Form.Item>
                                </Col>

                                <Col xs={24} sm={24} md={3} lg={3} xl={3} className='px-2'>
                                    <Form.Item name="class"
                                        label={setLocale("classes.label")}
                                        rules={[
                                            {
                                                required: false,
                                                message: setLocale('classes.label_error'),
                                            },
                                        ]}>
                                        <Select optionLabelProp="label" mode="multiple">
                                            {classes ? classes.map((clas, index) =>
                                                <Option value={clas.id} key={index} label={clas.name}>{clas.name}</Option>
                                            ) : null}
                                        </Select>
                                    </Form.Item>
                                </Col>

                                <Col xs={24} sm={24} md={4} lg={4} xl={4} className='px-2'>
                                    <Form.Item name="attendance_code_id"
                                        label={setLocale("attendance_report.attendance_status")}
                                        rules={[
                                            {
                                                required: true,
                                                message: setLocale('attendance_report.attendance_error'),
                                            },
                                        ]}>
                                        <Select optionLabelProp="label"
                                            mode="multiple"
                                            filterOption={(inputValue, option) =>
                                                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                                            }>
                                            {attCodes ? attCodes.map((attCode, index) =>
                                                <Option value={attCode.id} key={index} label={attCode.attendance_code}>{attCode.attendance_code}</Option>
                                            ) : null}
                                        </Select>
                                    </Form.Item>
                                </Col>

                                <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                                    <Form.Item label=" " className='text-right'>
                                        <Button loading={reportLoad} type="primary" htmlType="submit">
                                            {setLocale("attendance_report.generate_report")}
                                        </Button>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form>
                        {reportLoad &&
                            <><Skeleton active /><Skeleton active /></>
                        }
                        {(!reportLoad && attendanceByStatus) &&
                            <>
                                {(attView == 'table') ? <AttendanceReportTableView data={attendanceByStatus} reportName={reportName} /> : null}
                                {(attView == 'register') ? <AttendanceReportRegisterView data={attendanceByStatus} reportName={reportName} /> : null}
                            </>
                        }
                    </div>
                </Col>
            </Row>
        </>
    )
}

export default AttendanceByStatusReport


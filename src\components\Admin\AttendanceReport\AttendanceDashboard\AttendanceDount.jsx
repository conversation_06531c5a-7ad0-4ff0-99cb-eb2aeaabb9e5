import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { Form, Row, Col, Card } from 'antd';
import Chart from "react-apexcharts";
// import { 
//   ArrowDownOutlined 
// } from '@ant-design/icons';
// import IntlMessage from "components/util-  components/IntlMessage";
// import {
//   getAttendanceDashboardReport,
//   getClassStudents
// } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import { 
//     MANAGE_CLASS, 
//     SCHOOL_YEAR 
// } from "constants/AppConstants";

// const setLocale = (localeKey, isLocaleOn = true) =>
//     isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function  AttendanceDount() {
    // const [form] = Form.useForm();
    // const dispatch = useDispatch();
    const { 
    //   AttendanceReportButtonSpinner,
      attendanceDashboardReport,
    //   classStudents 
    } = useSelector((state) => state[ATTENDANCE_REPORT]);
    
    const [state2, setState2] = useState({
      series: attendanceDashboardReport?.options?.series || [],
      options: {
        colors: attendanceDashboardReport?.options?.colors || [],
        labels: attendanceDashboardReport?.options?.labels || [],
        legend: {
          show: false,
          position: 'left',
          offsetY: 20,
        },
        responsive: [{
          breakpoint: 480,
          options: {
            chart: {
              width: 200
            },
            legend: {
              position: 'bottom'
            }
          }
        }]
      }
    });
    useEffect(() => {
      if (attendanceDashboardReport?.options) {
        setState2({
          series: attendanceDashboardReport.options.series,
          options: {
            colors: attendanceDashboardReport.options.colors,
            labels: attendanceDashboardReport.options.labels,
            legend: {
              show: false,
              position: 'left',
              offsetY: 20,
            },
            responsive: [{
              breakpoint: 480,
              options: {
                chart: {
                  width: 200
                },
                legend: {
                  position: 'bottom'
                }
              }
            }]
          }
        });
      }
    }, [attendanceDashboardReport]);
    return (
        <>
            <Card className="p-2" style={{height: "350px"}}>
              <div className="d-flex justify-content-between mb-3">                
                  <h1 style={{color: "#3C3C3C"}}>Attendance Overall</h1>
                  <span>Summary of attendance</span>                
              </div>
              <Row gutter={16}>
                <Col span={10} className="mt-4">
                { attendanceDashboardReport?.cardData?.map((item, index) => (
                  <>
                    <div className="d-flex" key={index}>
                        <span className="present-attendance ml-1 mt-2 mr-3"></span>
                        <div className="" style={{width:"150px"}}>{item?.name}</div>
                        <div className="semiHead-3">{item?.total}</div>
                    </div>
                    <hr key={index} />
                  </>
                ))}
                </Col>
                <Col span={14}>
                  <div className="mt-0">
                    <Chart options={state2.options} series={state2.series} height={250} type="donut" />
                  </div>
                </Col>
              </Row>
            </Card>
        </>
    );
}

export default AttendanceDount;

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { notification, Divider, Row, Col, Form, Button, Input, InputNumber } from 'antd';
import { 
  markAssignment,
  singleStudentSaveRubricMarks
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import IntlMessage from "components/util-components/IntlMessage"
import { saveAs } from 'file-saver';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AssigneeRubrics() {
  const { 
    obtainMarksLoad,errors,
    showMessage,
    singleAssignee,
    saveRubricLoading
  } = useSelector((state) => state[ASSIGNMENT_SLICE]);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [assigneeRubrics, setAssigneeRubrics] = useState(null);

  const [api, contextHolder] = notification.useNotification();
  const openNotificationWithIcon = (type, msg) => {
      api[type]({ message: msg })
  }
  useEffect(() => {
    setAssigneeRubrics(singleAssignee?.assignee_rubrics)
  }, [])

  const handleMarkChange = (value, index) => {  
    const updatedRubrics = [...assigneeRubrics];    
    updatedRubrics[index] = { ...updatedRubrics[index] };
    updatedRubrics[index].obtained_mark = value;   
    setAssigneeRubrics(updatedRubrics) 
}
const handleRemarksChange = (e, index) => {    
    const updatedRubrics = [...assigneeRubrics]
    updatedRubrics[index] = { ...updatedRubrics[index] }
    updatedRubrics[index].comment = e.target.value; 
    setAssigneeRubrics(updatedRubrics)     
}
const saveAssigneeRubrics = async () => {
  let hasError = false;
  assigneeRubrics.map((rubric) => {
      if (rubric.obtained_mark > rubric.mark) {
          hasError = true;
          return { ...rubric, error: 'Obtained mark cannot be greater than total mark' };
      }
      return rubric;
  });  
  if (hasError) {
      openNotificationWithIcon('error', `Obtained mark cannot be greater than total mark`)
      return false
  }
  await dispatch(singleStudentSaveRubricMarks({rubrics:assigneeRubrics}))  
}
  return (
    <>
        {contextHolder}
        <Row className='mt-3'>
          <Col xs={24} sm={12} md={6} lg={6} xl={6}> Title</Col>
          <Col xs={24} sm={12} md={6} lg={6} xl={6}> Total</Col>
          <Col xs={24} sm={12} md={6} lg={6} xl={6}> Mark </Col>
          <Col xs={24} sm={12} md={6} lg={6} xl={6}> Remarks </Col>
        </Row>
        <Divider />
          { 
            singleAssignee?.assignee_rubrics.map((rubric, index) => (
              <>
                  <Row gutter={4} key={rubric.id}>
                      <Col xs={24} sm={12} md={6} lg={6} xl={6}> {rubric.rubric}</Col>
                      <Col xs={24} sm={12} md={6} lg={6} xl={6}> {rubric.mark}</Col>
                      <Col xs={24} sm={12} md={6} lg={6} xl={6}> 
                          <InputNumber defaultValue={rubric.obtained_mark} 
                              onChange={(value) => handleMarkChange(value, index)} />
                      </Col>
                      <Col xs={24} sm={12} md={6} lg={6} xl={6}> 
                      <Input defaultValue={rubric.comment} 
                          onChange={(e) => handleRemarksChange(e, index)} 
                      />
                      </Col>
                  </Row>
                  
              </>
            ))
          }  
          <Row gutter={4}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}> 
                <Button type='primary' loading={saveRubricLoading} onClick={() => saveAssigneeRubrics() }>{setLocale('save')}</Button>
              </Col>          
          </Row>             
    </>
  )
}

export default AssigneeRubrics;
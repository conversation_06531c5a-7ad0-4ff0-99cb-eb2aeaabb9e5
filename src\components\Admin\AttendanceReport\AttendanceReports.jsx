import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>crumb, Badge, Row, Col, Skeleton, Card, Select, Input } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { SearchOutlined } from '@ant-design/icons';
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
import { MANAGE_CLASS, SCHOOL_YEAR } from "constants/AppConstants";
// import {
//     getManageClass,
// } from "store/slices/ManageClass/manageManageClassSlice.js";
// import { getAllClasses } from 'store/slices/classManagement/classSlice';
// import {
//     getAttendanceCodes
//   } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import AttendanceByStatusReport from './AttendanceByStatusReport'
import ClassAttendanceReport from './ClassAttendanceReport'
import ClassStudentReport from './ClassStudentReport'
import ClassStudentSummaryReport from './ClassStudentSummaryReport'
import IntlMessage from 'components/util-components/IntlMessage';
import { Link } from "react-router-dom";

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Option } = Select;

function AttendanceReports() {
  const dispatch = useDispatch();
  const [selectedReport, setSelectedReport] = useState('stdReport');
  const [pageLoad, setPageLoad] = useState(true);

  useEffect(() => {
    // dispatch(getAttendanceCodes());
    // dispatch(getManageClass(null)).then((result) => {
    setPageLoad(false)
    // });
  }, []);

  const {
    attendanceCodes,
  } = useSelector((state) => state[ATTENDANCE_REPORT]);
  const { ManageClassResult } = useSelector((state) => state[MANAGE_CLASS])

  const navigate = useNavigate();

  const handleSelectChange = (value) => {
    setSelectedReport(value);
  };

  return (
    <>
      {pageLoad ?
        (<> <Skeleton active /><Skeleton active /></>
        ) : (
          <>
            <div className='d-flex justify-content-between'>
              {/* <Input
                prefix={<SearchOutlined />}
                placeholder="Search"
                style={{ width: "300px", marginTop: "-5px" }}
                className='mb-2'
              /> */}
            </div>
            <div className='code-box'>
              <section className='code-box-demo'>
                <Row gutter={16} className='attendance-report my-3 mx-2'>
                  <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                    <Select
                      placeholder="Select a report"
                      style={{ width: '30%' }}
                      onChange={handleSelectChange}
                      defaultValue={selectedReport}
                    >
                      <Option value="stdReport">Student Attendance Report</Option>
                      <Option value="classattReport">Class Attendance Report</Option>
                      <Option value="attByStatusReport">Attendance by Status</Option>
                      <Option value="attSummary">Attendance Summary</Option>
                    </Select>
                  </Col>
                </Row>
                {selectedReport === 'attByStatusReport' && (
                  <AttendanceByStatusReport classes={ManageClassResult} attCodes={attendanceCodes} />
                )}
                {selectedReport === 'classattReport' && (
                  <ClassAttendanceReport classes={ManageClassResult} />
                )}
                {selectedReport === 'stdReport' && (
                  <ClassStudentReport classes={ManageClassResult} />
                )}
                {selectedReport === 'attSummary' && (
                  <ClassStudentSummaryReport classes={ManageClassResult} />
                )}

              </section>
            </div>
          </>
        )}
    </>
  );
}

export default AttendanceReports;




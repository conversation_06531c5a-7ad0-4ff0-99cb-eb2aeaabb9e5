import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { CHARGES_RATES } from "constants/AppConstants";
import AddChargesRatesModal from "./Modals/index";
import {
    ChargesRatesAddDrawerStatus,
    ChargesRatesEditWithDrawerStatus,
    deleteChargesRates,
    getChargesRates,
    updateSortFilters,
    setColumnSearch
} from "store/slices/ChargesRates/manageChargesRatesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(ChargesRatesAddDrawerStatus({ errorStatus: 1, status: true }));
    const { ChargesRatesAddDrawer, ChargesRatesResult, tablePagination, sorting, filter, ChargesRatesTableLoading, permission } = useSelector(
        (state) => state[CHARGES_RATES]
    );

    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getChargesRates({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        console.log(dataIndex);

        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => {
            // if (dataIndex === 'school_year' || dataIndex === 'grade_level' || dataIndex === 'charge_type') {
            if (dataIndex === 'grade_level' || dataIndex === 'charge_type') {
                const columnValue = dataIndex.split('.').reduce((acc, key) => acc[key], record);
                return columnValue ? columnValue.toString().toLowerCase().includes(value.toLowerCase()) : false;
            } else if (dataIndex === 'is_active') {
                return record[dataIndex].toString() === value;
            } else {
                return record[dataIndex].toString().toLowerCase().includes(value.toLowerCase());
            }
        },
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };

    const handleDelete = (record) => {
        dispatch(deleteChargesRates(record.id)).then(() => {
            getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        })
    }
    const handleUpdate = (record) => {
        dispatch(ChargesRatesEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            let tableFilters;
            if (filters?.is_active?.length > 0) {
                tableFilters = { ...filter, is_active: filters.is_active };
            } else {
                tableFilters = { ...filter, is_active: '' };
            }

            await dispatch(updateSortFilters({ filter: tableFilters, sorting }));
            getModuleData(1, tablePagination.pageSize, tableFilters, sorting);
        } catch (error) {
            console.error(error);
        }
    };


    const columns = [
        // {
        //     title: setLocale("charges_and_invoices.school_year"),
        //     key: "school_year",
        //     dataIndex: "school_year",
        //     // fixed: 'left',
        //     sorter: true,
        //     ...getColumnSearchProps('school_year'),
        // },
        {
            title: setLocale("charges_and_invoices.grade_level"),
            key: "grade_level",
            dataIndex: "grade_level",
            sorter: true,
            ...getColumnSearchProps('grade_level'),
        },
        {
            title: setLocale("charges_and_invoices.charge_type"),
            key: "charge_type",
            dataIndex: "charge_type",
            sorter: true,
            ...getColumnSearchProps('charge_type'),
        },
        {
            title: setLocale("charges_and_invoices.amount"),
            key: "amount",
            dataIndex: "amount",
            sorter: true,
            // ...getColumnSearchProps('amount'),
        },
        {
            title: setLocale("charges_and_invoices.effective_from"),
            key: "effective_from",
            dataIndex: "effective_from",
            sorter: true,
            ...getColumnSearchProps('effective_from'),
        },
        {
            title: setLocale("charges_and_invoices.effective_to"),
            key: "effective_to",
            dataIndex: "effective_to",
            sorter: true,
            ...getColumnSearchProps('effective_to'),
        },
        // {
        //     title: setLocale("charges_and_invoices.is_active"),
        //     key: "is_active",
        //     dataIndex: "is_active",
        //     filters: [
        //         {
        //             text: 'Active',
        //             value: '1',
        //         },
        //         {
        //             text: 'In-Active',
        //             value: '0',
        //         },
        //     ],
        // },
        {
            title: setLocale('operation'),
            key: "action",
            fixed: 'right',
            render: (data, record) => (
                <>

                    {permission.includes("Delete") && <>
                        {record.invoice_status === 0 && record.recurring_charge_count === 0 && <>
                            {
                                record.is_active === 'Active' && (
                                    <Popconfirm placement="left" title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                                        <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                                    </Popconfirm>
                                )}
                        </>}
                    </>}

                    {permission.includes("Update") && <>
                        {(record.invoice_status === 0 && record.recurring_charge_count === 0) && <>
                            {
                                record.is_active === 'Active' && (
                                    <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                                )}
                        </>}
                    </>}

                    {/* {permission.includes("View") && (
                        <Link to={`../../app/charges-types_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('charges_and_invoices.charge_rate')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale('charges_and_invoices.charge_add_rate')}
                            </Button>
                        )}
                    </section>
                    {ChargesRatesAddDrawer && <AddChargesRatesModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            // scroll={{ x: 2500 }}
                            loading={ChargesRatesTableLoading}
                            rowKey={record => record.id}
                            dataSource={ChargesRatesResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

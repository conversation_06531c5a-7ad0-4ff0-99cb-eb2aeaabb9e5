import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { Alert, Row, Dropdown, Button, Space, Col, Tag, Form } from 'antd';
import { EyeOutlined, DownloadOutlined } from '@ant-design/icons';
import { env } from "configs/EnvironmentConfig"
import {
    setAssigneeOpenModal,
    // setSingleAssignment,
    getSingleAssignment
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import AssignmentAssigneesModal from './Modals/AssignmentAssigneesModal'
import IntlMessage from "components/util-components/IntlMessage"
// import { success } from 'concurrently/src/defaults';
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


function AssignmentsDetails(props) {
    const { data } = props
    const { openAssigneeModal } = useSelector((state) => state[ASSIGNMENT_SLICE])

    const dispatch = useDispatch()

    /**look into assignment assignees */
    const assignmentAssignees = async (assignment) => {
        await dispatch(setAssigneeOpenModal())
        await dispatch(getSingleAssignment(assignment.id))
    }
    useEffect(() => {

    }, [])

    return (
        <>
            <Row gutter={4}>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                    <div className='d-flex'>
                        <p className='my-2 mx-5'>
                            {data.instructions}
                        </p>
                    </div>
                    {data.assignmentAttachments.map((attachment, index) => (
                        <Alert className='mb-2'
                            key={index}
                            message={attachment.attachment_name}
                            type="success"
                            action={
                                <Button
                                    size="small"
                                    type="link"
                                    target="_blank"
                                    href={attachment.attachment_type_code === 'file' ? env.FILE_ENDPOINT_URL + attachment.attachment_path : attachment.attachment_path}
                                    icon={<DownloadOutlined />} >
                                    Download
                                </Button>
                            } />
                    ))
                    }
                </Col>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                    <div className='assign-tag'>
                        <Tag onClick={() => { assignmentAssignees(data) }} className='text-center tag cursor-pointer' color="gold"> {data.pendingCount} <br />Pending</Tag>
                        <Tag onClick={() => { assignmentAssignees(data) }} className='text-center tag cursor-pointer' color="green">{data.submittedCount} <br />Submitted</Tag>
                        <Tag onClick={() => { assignmentAssignees(data) }} className='text-center tag cursor-pointer' color="red">{data.reassignedCount} <br />Re-Assigned</Tag>
                    </div>
                </Col>
            </Row>
            {openAssigneeModal ? <AssignmentAssigneesModal /> : null}
        </>
    )
}

export default AssignmentsDetails;

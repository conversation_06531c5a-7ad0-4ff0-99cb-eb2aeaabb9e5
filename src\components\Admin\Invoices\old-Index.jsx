import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Button, Breadcrumb, Skeleton, Row, Col, Select, DatePicker, Form, Card, Badge } from 'antd';
import { INVOICES, GRADE_LEVEL, SCHOOL_YEAR } from "constants/AppConstants";
import AddInvoicesModal from "./Modals/index";
import {
    InvoicesAddDrawerStatus,
    annualDrawerStatus,
    studentDrawerStatus,
    monthlyDrawerStatus,
    monthlyWiseInvoices,
    studentWiseInvoices,
    annualInvoices,
    updateSortFilters,
    mainDrawerStatus,
    invoicesRevenueSummary
} from "store/slices/Invoices/manageInvoicesSlice.js";
import { getGradeLevel } from "store/slices/GradeLevel/manageGradeLevelSlice";
import { getSchoolYear } from "store/slices/SchoolYear/manageSchoolYearSlice";
import MonthlyInvoicesGenerated from "./MonthlyInvoicesGenerated";
import TotalInvoicesGenerated from "./TotalInvoicesGenerated";
import StudentsInvoiced from "./StudentsInvoiced";
import AnnualInvoicesStatus from "./AnnualInvoicesStatus";
import moment from "moment";

import Chart from "react-apexcharts";
import ChartWidget from "components/shared-components/ChartWidget";
import { COLORS, COLOR_1, COLOR_2, COLOR_4 } from "constants/ChartConstant";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import { DATE_FORMAT_YYYY_MM } from "constants/DateConstant";
import Flex from "components/shared-components/Flex";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


const InvoiceChart = ({ annualInvoice }) => {
    const totalPaidInvoice = annualInvoice
        ? annualInvoice?.invoiceChartData[0]?.data?.reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0
        )
        : 0;
    const totalPartiallyPaidInvoice = annualInvoice
        ? annualInvoice?.invoiceChartData[1]?.data?.reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0
        )
        : 0;
    const totalPendingInvoice = annualInvoice
        ? annualInvoice?.invoiceChartData[2]?.data?.reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0
        )
        : 0;

    const { direction } = useSelector((state) => state.theme);
    return (
        <Card title={setLocale("invoices.status_annual_invoices")}>
            <div
                className=""
                style={{
                    float: "right",
                    margin: "5px",
                    marginTop: "-52px",
                    textAlign: "right",
                }}
            >
                <Flex>
                    <div className="mr-5">
                        <h2 className="font-weight-bold mb-1">{totalPaidInvoice}</h2>
                        <p>
                            <Badge color={COLORS[1]} />
                            {setLocale('invoices.paid_invoices')}
                        </p>
                    </div>
                    <div className="mr-5">
                        <h2 className="font-weight-bold mb-1">
                            {totalPartiallyPaidInvoice}
                        </h2>
                        <p>
                            <Badge color={COLORS[2]} />
                            {setLocale('invoices.partially_paid_invoices')}
                        </p>
                    </div>
                    <div>
                        <h2 className="font-weight-bold mb-1">{totalPendingInvoice}</h2>
                        <p>
                            <Badge color={COLORS[0]} />
                            {setLocale('invoices.pending_invoices')}
                        </p>
                    </div>
                </Flex>
            </div>
            <div>
                <ChartWidget
                    card={false}
                    series={annualInvoice?.invoiceChartData}
                    xAxis={annualInvoice?.yearlyInvoiceData?.categories}
                    yAxis={annualInvoice?.yearlyInvoiceData?.series}
                    height={280}
                    direction={direction}
                    customOptions={{
                        colors: [COLORS[1], COLORS[2], COLORS[0]],
                        legend: {
                            show: false,
                        },
                        stroke: {
                            width: 2.5,
                            curve: "smooth",
                        },
                    }}
                />
            </div>
        </Card>
    );
};

function Index() {
    const dispatch = useDispatch();
    const [form] = Form.useForm();
    const {
        InvoicesAddDrawer,
        filter,
        MonthlyWiseInvoicesLoading,
        monthlyWiseInvoice,
        StudentWiseInvoicesLoading,
        studentWiseInvoice,
        AnnualInvoicesLoading,
        annualInvoice,
        ChargeTypeDetailStatus,
        MonthlyWiseInvoiceStatus,
        StudentWiseInvoiceStatus,
        AnnualInvoiceStatus,
        revenueSummary,
        revenueSummaryLoading,
        MainComponentStatus
    } = useSelector((state) => state[INVOICES]);
    const { GradeLevelResult } = useSelector((state) => state[GRADE_LEVEL]);
    const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                await dispatch(getSchoolYear({ perPage: 1000 }));
                await dispatch(getGradeLevel({ perPage: 1000 }));
                await dispatch(mainDrawerStatus());

                // await dispatch(chargeTypeInvoices(filter));
                await dispatch(monthlyWiseInvoices(filter));
                await dispatch(studentWiseInvoices(filter));
                await dispatch(annualInvoices(filter));
                await dispatch(invoicesRevenueSummary(filter));
            } catch (error) {
                console.error('Error fetching school year data:', error);
            }
        };

        fetchData();
    }, []);

    const fetchRecord = async () => {
        await dispatch(monthlyWiseInvoices(filter));
        await dispatch(studentWiseInvoices(filter));
        await dispatch(annualInvoices(filter));
        await dispatch(invoicesRevenueSummary(filter));
    };

    const disabledDate = (current) => {
        // Get the current date
        const currentDate = moment();

        const validSchoolYears = SchoolYearResult?.data?.filter(item => {
            const start = moment(item.start_date);
            const end = moment(item.end_date);
            return currentDate >= start && currentDate <= end;
        });

        // Check if current date is within the valid school years
        return !validSchoolYears.some(item => {
            const start = moment(item.start_date);
            const end = moment(item.end_date);
            return current >= start && current <= end;
        });
    };

    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };


    const state5 = {
        series: [{
            name: 'Invoice Amount',
            data: revenueSummary?.invoice_amount ?? []
        }, {
            name: 'Payment Amount',
            data: revenueSummary?.payment_amount ?? []
        }],
        options: {
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                },
            },
            colors: [COLOR_1, COLOR_2],
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: revenueSummary?.categories ?? [],
                convertedCatToNumeric: false
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: val => (`$${val} `)
                }
            }
        }

    };


    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('invoices.title')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                {ChargeTypeDetailStatus && (
                    <TotalInvoicesGenerated />
                )}
                {MonthlyWiseInvoiceStatus && (
                    <MonthlyInvoicesGenerated />
                )}
                {StudentWiseInvoiceStatus && (
                    <StudentsInvoiced />
                )}
                {AnnualInvoiceStatus && (
                    <AnnualInvoicesStatus />
                )}
                {MainComponentStatus && (
                    <div className="code-box">
                        <section className="code-box-demo" style={{ padding: '6px 10px 0px' }}>
                            <Form
                                layout="horizontal"
                                form={form}
                                name="form"
                                onFinishFailed={onFinishFailed}
                                autoComplete="off"
                                initialValues={{
                                    grade_level: filter?.grade_level,
                                    invoice_date: filter?.invoice_date
                                }}
                            >
                                <Row gutter={16} className="mt-3">
                                    <Col xs={11} sm={11} lg={11} xl={11} xxl={11}>
                                        <Form.Item
                                            name="grade_level"
                                            rules={[
                                                {
                                                    required: false,
                                                    message: setLocale('classes.school_year_error'),
                                                },
                                            ]}
                                        >
                                            <Select className='rounded-0' showSearch optionLabelProp="label"
                                                allowClear
                                                onChange={(value, rec) => {
                                                    dispatch(updateSortFilters({
                                                        ...filter,
                                                        grade_level: value ?? '',
                                                        grade_name: rec?.grade_level ?? '',
                                                    }))
                                                }}
                                                placeholder={setLocale("invoices.school_grade")}
                                                optionFilterProp="children"
                                                value={filter?.grade_level}
                                                filterOption={(input, option) => (
                                                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                                                )}
                                                // filterSort={(optionA, optionB) =>
                                                //     (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                                                // }
                                                options={GradeLevelResult?.data ?? []}
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={11} sm={11} lg={11} xl={11} xxl={11}>
                                        <Form.Item name="invoice_date">
                                            <DatePicker.MonthPicker
                                                disabledDate={disabledDate}
                                                value={filter?.invoice_date}
                                                onChange={(value) =>
                                                    dispatch(updateSortFilters({
                                                        ...filter,
                                                        invoice_date: value ? moment(value).format('YYYY-MM') : ''
                                                    }))}
                                                format={DATE_FORMAT_YYYY_MM}
                                                className="rounded-0 w-100"
                                            />
                                        </Form.Item>
                                    </Col>

                                    <Col xs={2} sm={2} lg={2} xl={2} xxl={2}>
                                        <Form.Item>
                                            <Button
                                                onClick={fetchRecord}
                                                className="ant-btn-round ant-btn-sm"
                                                type="primary"
                                                style={{ float: "right", margin: "5px" }}
                                            >
                                                {setLocale("invoices.fetch")}
                                            </Button>
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </Form>
                        </section>
                        {InvoicesAddDrawer && <AddInvoicesModal />}
                        <section className="code-box-description">

                            <Row gutter={16} className="my-1">
                                {/* <Col xs={24} sm={24} md={12} lg={8} xl={8}>
                                    <Skeleton loading={ChargeTypeInvoicesLoading} active >
                                        <span
                                            style={{ cursor: "pointer" }}
                                            onClick={() => {
                                                dispatch(chargeTypeDrawerStatus({ drawerStatus: true, mainStatus: false }))
                                            }}
                                        >
                                            <Card className="mx-4">
                                                <div className="d-flex justify-content-between">
                                                    <h4>{setLocale('invoices.invoices_generated_for_charge_types')}</h4>
                                                </div>

                                                <Chart
                                                    options={chargeTypeInvoice?.options ?? []}
                                                    series={chargeTypeInvoice?.series ?? {}}
                                                    height={300}
                                                    type="pie"
                                                />
                                            </Card>
                                        </span>
                                    </Skeleton>
                                </Col> */}

                                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                    <Skeleton loading={MonthlyWiseInvoicesLoading} active>
                                        <span
                                            style={{ cursor: "pointer" }}
                                            onClick={() => {
                                                dispatch(monthlyDrawerStatus({ drawerStatus: true, mainStatus: false }))
                                            }}
                                        >
                                            <Card className="">
                                                <div className="d-flex justify-content-between">
                                                    <h4>{setLocale('invoices.invoices_generated_monthly')}</h4>
                                                </div>
                                                <Chart
                                                    options={monthlyWiseInvoice?.options ?? []}
                                                    series={monthlyWiseInvoice?.series ?? {}}
                                                    height={300}
                                                    type="pie"
                                                />
                                            </Card>
                                        </span>
                                    </Skeleton>
                                </Col>
                                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                    <Skeleton loading={StudentWiseInvoicesLoading} active >
                                        <span
                                            style={{ cursor: "pointer" }}
                                            onClick={() => {
                                                dispatch(studentDrawerStatus({ drawerStatus: true, mainStatus: false }))
                                            }}
                                        >
                                            <Card className="">
                                                <div className="d-flex justify-content-between">
                                                    <h4>{setLocale("invoices.invoices_by_status")}</h4>
                                                </div>
                                                <Chart
                                                    options={studentWiseInvoice?.options ?? []}
                                                    series={studentWiseInvoice?.series ?? {}}
                                                    height={300}
                                                    type="pie"
                                                />
                                            </Card>
                                        </span>
                                    </Skeleton>
                                </Col>

                            </Row>


                            <Row gutter={16}>
                                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                    <Skeleton loading={revenueSummaryLoading} active >
                                        <Card className="">
                                            <div className="d-flex justify-content-between">
                                                <h4>{setLocale('invoices.invoices_revenue_summary')}</h4>
                                            </div>
                                            <span
                                                style={{ cursor: "pointer" }}
                                            // onClick={() => {
                                            //     dispatch(annualDrawerStatus({ drawerStatus: true, mainStatus: false }))
                                            // }}
                                            >
                                                <Chart
                                                    options={state5?.options}
                                                    series={state5?.series}
                                                    height={300}
                                                    type="bar"
                                                />
                                            </span>
                                        </Card>
                                    </Skeleton>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                    <Skeleton loading={AnnualInvoicesLoading} active >
                                        <span
                                            style={{ cursor: "pointer" }}
                                            onClick={() => {
                                                dispatch(annualDrawerStatus({ drawerStatus: true, mainStatus: false }))
                                            }}
                                        >
                                            <InvoiceChart annualInvoice={annualInvoice ?? []} />
                                        </span>
                                    </Skeleton>
                                </Col>
                            </Row>

                        </section>
                    </div>
                )}
            </>
        </>
    );
}

export default Index;

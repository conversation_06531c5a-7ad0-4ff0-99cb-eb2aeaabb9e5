import React from "react";
import { Form, Col, Row, Input, Space, Button } from "antd";
import { useSelector, useDispatch } from "react-redux";
import IntlMessage from "components/util-components/IntlMessage";
import { 
    BUDGET_ALLOCATION
  } from "constants/AppConstants";
  import {
    BudgetAllocationAddDrawerStatus,
    onCloseError,
    saveTeacherBudgetAllocation,
    setTeacherAllocatedBudget,
    getBudgetAllocations
} from "store/slices/BudgetAllocation/manageBudgetAllocationSlice.js";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function BudgetAccountHeads() {
    const [budgetForm] = Form.useForm();
    const dispatch = useDispatch();
    const {
        BudgetAllocationButtonAndModelLabel,
        BudgetAllocationErrors,
        BudgetAllocationShowMessage,
        saveBudgetBtnLoader,
        teacherAllocatedBudget,
        allocatedBudgetFilter,
        sorting,
        filter,
        tablePagination,
    } = useSelector((state) => state[BUDGET_ALLOCATION]);
    const onClose = async () => {
        await dispatch(setTeacherAllocatedBudget([]));
        await dispatch(BudgetAllocationAddDrawerStatus({ errorStatus: 0, status: false }));
        await dispatch(onCloseError());
    }
    const saveTeacherBudget = async (values) => {
        const data = {
            account_heads: values,
            teacher_data: allocatedBudgetFilter
        }
        await dispatch(saveTeacherBudgetAllocation(data)); 
        await dispatch(getBudgetAllocations(tablePagination.current, tablePagination.pageSize, filter, sorting));      
        onClose();
    }
    return (
        <>            
            { teacherAllocatedBudget.length > 0 &&
            <>
                <Row gutter={16}>
                    <Col sm={24} md={8} lg={9} className="text-center mb-2">
                       <h3>{ setLocale('budgetallocation.account_heads') }</h3> 
                    </Col>
                    <Col sm={24} md={5} lg={5} className="text-center mb-2">
                        <h3>{ setLocale('budgetallocation.total_allocated') }</h3>
                    </Col>
                    <Col sm={24} md={5} lg={5} className="text-center mb-2">
                       <h3>{ setLocale('budgetallocation.total_consumed') }</h3>
                    </Col>
                    <Col sm={24} md={6} lg={5} className="text-center mb-2">
                       <h3>{ setLocale('budgetallocation.total_remaining') }</h3> 
                    </Col>
                    <hr />
                </Row>
                <Form  onFinish={saveTeacherBudget} form={budgetForm} >
                    {teacherAllocatedBudget?.map((item, index) => (
                        <Row gutter={16} key={`row_${index}`}>
                                <Col key={`head_${item.account_head_id}`} sm={24} md={8} lg={9} className="text-center pr-5 pl-5" style={{marginTop: "-8px"}}>
                                    <Form.Item
                                        name={`${item.account_head_id}`}
                                        label={`${item.account_head_code}`}
                                        initialValue={item.total_allocated}
                                        rules={[
                                            {
                                                required: false,
                                                message: `${item.account_head_code} is required!`,
                                            },
                                        ]}
                                        style={{marginLeft: "20px"}}
                                        validateStatus={BudgetAllocationShowMessage && BudgetAllocationErrors.days ? "error" : ""}
                                        help={BudgetAllocationShowMessage && BudgetAllocationErrors.days}
                                    >
                                        <Input style={{ width: "70px", height: "30px", float: "right", borderRadius: "5px", marginRight: '25px' }} />
                                    </Form.Item>
                                    <div>
                                    <hr  style={{marginTop: "-19px"}}/>
                                    </div> 
                                </Col>
                                <Col key={`allocated_${item.account_head_id}`} sm={24} md={5} lg={5} className="text-center pl-5 pr-5 pt-2 pb-2" style={{borderLeft: "1px solid #000000"}}>                                    
                                    {item.total_allocated}  
                                    <hr />                                  
                                </Col>
                                <Col key={`consumed_${item.account_head_id}`} sm={24} md={5} lg={5} className="text-center pl-5 pr-5 pt-2 pb-2" style={{borderLeft: "1px solid #000000"}}>                                  
                                    {item.consumed_amount}
                                    <hr />
                                </Col>
                                <Col key={`remaining_${item.account_head_id}`} sm={24} md={5} lg={5} className="text-center pl-5 pr-5 pt-2 pb-2" style={{borderLeft: "1px solid #000000"}}>                                    
                                    {item.total_allocated - item.consumed_amount}
                                    <hr/>
                                </Col>
                        </Row>
                    ))}
                    <Space className="mt-4">
                        <Button type="primary" htmlType="submit" loading={saveBudgetBtnLoader} >
                            {BudgetAllocationButtonAndModelLabel}
                        </Button>
                        <Button onClick={onClose}>{setLocale('Cancel')}</Button>
                    </Space>
                </Form>
            </>
            }
        </>
    );
}

export default BudgetAccountHeads;

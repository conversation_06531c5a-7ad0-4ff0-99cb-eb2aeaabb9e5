import React, { useEffect, useState } from 'react';
import { Drawer, Tabs, Col, Row, Skeleton, Breadcrumb, Card } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { ArrowLeftOutlined } from '@ant-design/icons';
import {
    ClassCourseViewDrawerStatus,
    ClassCourseScheduleDrawerStatus,
    ClassCourseLessonDrawerStatus,
    ClassCourseGradeDrawerStatus,
    ClassCourseAssignmentDrawerStatus,
    getClassCourseData,
    viewManageClass
} from "store/slices/ManageClass/manageManageClassSlice";
import { useParams, useNavigate, Link } from 'react-router-dom';
import ClassCourseScheduleModel from './ClassCourseScheduleModel';
import CourseLessonPlan from './CourseLessonPlan';
// import StudentGrades from 'components/Admin/Grade/StudentGrades';
// import StudentGradesTable from 'components/Admin/Grade/StudentGradesTable';
import Assignments from '../../Assignments/Assignments'
import Topics from '../../Assignments/Topics'


import IntlMessage from 'components/util-components/IntlMessage';
import { MANAGE_CLASS } from 'constants/AppConstants';
import { USER_INFORMATION } from "constants/AuthConstant";
const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION));

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function ViewClassCourseModel() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const params = useParams();
    const [pageLoading, setPageLoading] = useState(true);
    const [topicTab, setTopicTab] = useState(false);
    // const [teacherData, setTeacherData] = useState([]);
    // const [userRoles, setUserRoles] = useState('');

    const {
        permission,
        classCourseScheduleDrawer,
        ClassCourseEditData,
        classCourseLessonDrawer,
        course_schedule,
        classCourseGradeDrawer,
        teacherData, userRoles, currentUserData,
        classCourseAssignmentDrawer,
    } = useSelector((state) => state[MANAGE_CLASS]);

    const fetchData = async () => {
        await dispatch(getClassCourseData({ id: params?.id, class_course_id: params?.class_course_id })).then(async (result) => {
            dispatch(ClassCourseViewDrawerStatus({ status: true, data: result.payload[0] }));
            await dispatch(viewManageClass(result?.payload[0]?.enc_manage_class_id ?? ''));
            setPageLoading(false);
        });
    }

    useEffect(() => {
        fetchData();
    }, [dispatch]);

    const closeClassCourseViewModel = (status) => {
        dispatch(ClassCourseViewDrawerStatus({ status: status, data: [] }));
        navigate(-1);
    };
    const onTabChange = (key) => {
        setTopicTab(false)
        // if (key === 'schedule') { dispatch(ClassCourseScheduleDrawerStatus({ status: true, data: course_schedule ?? [] })) }
        if (key === 'grades') { dispatch(ClassCourseGradeDrawerStatus({ status: true, data: [] })) }
        if (key === 'assignments') { dispatch(ClassCourseAssignmentDrawerStatus({ status: true, data: [] })) }
        if (key === 'lessons') { dispatch(ClassCourseLessonDrawerStatus({ status: true, data: course_schedule })); }
        if (key === 'topics') { setTopicTab(true) }
    }

    const items = [
        // permission.includes('View Class Schedule') ?
        //     {
        //         key: 'schedule',
        //         label: `Schedule`,
        //         children: classCourseScheduleDrawer && <ClassCourseScheduleModel />,
        //     } : '',
        permission.includes('View Class Lessons') ?
            {
                key: 'lessons',
                label: `Lessons`,
                children: classCourseLessonDrawer && <CourseLessonPlan />,
            } : '',
        // permission.includes('View Class Grades') ?
        //     {
        //         key: 'grades',
        //         label: `Grades`,
        //         children: classCourseGradeDrawer && <StudentGradesTable />,
        //     } : '',
        permission.includes('View Class Assignments') ?
            {
                key: 'assignments',
                label: `Assignments`,
                children: classCourseAssignmentDrawer && <Assignments />,
            } : '',
        {
            key: 'topics',
            label: `Topics`,
            children: topicTab && <Topics />,
        }
    ];
    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Manage Class')}</Breadcrumb.Item>
            </Breadcrumb>
            <Card
                // loading={pageLoading}
                type="inner"
                title={
                    <h5><span style={{ cursor: 'pointer' }} onClick={() => closeClassCourseViewModel(false)}><ArrowLeftOutlined /> {`${ClassCourseEditData?.course ?? ''}`}</span> </h5>
                }
                style={{
                    width: '100%',
                }}>
                <Skeleton active loading={pageLoading} >
                    <div className="example">
                        <div>
                            <Row gutter={16}>
                                <Col span={24}>

                                    <Tabs
                                        onTabClick={onTabChange}
                                        defaultActiveKey="lessons"
                                        size={'small'}
                                        style={{
                                            marginBottom: 32,
                                            marginTop: 32,
                                        }}
                                        items={items}
                                    />
                                </Col>

                            </Row>
                        </div>
                    </div>
                </Skeleton>

            </Card>
        </>
    )
}

export default ViewClassCourseModel

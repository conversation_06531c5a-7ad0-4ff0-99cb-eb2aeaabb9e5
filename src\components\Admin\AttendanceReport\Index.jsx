import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Toolt<PERSON>,
  Button,
  // Badge, Row, Col, Skeleton, Card,Input , Select, 
} from 'antd';
import { useSelector, useDispatch } from 'react-redux';
// import { useNavigate } from 'react-router-dom';
import {
  DashboardOutlined,
  FileDoneOutlined
} from '@ant-design/icons';
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import { MANAGE_CLASS, SCHOOL_YEAR } from "constants/AppConstants";
// import { DEFAULT_YEAR } from "constants/AuthConstant";
import {
  getManageClass,
} from "store/slices/ManageClass/manageManageClassSlice.js";
// import { getAllClasses } from 'store/slices/classManagement/classSlice';
import {
  getAttendanceCodes
} from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
// import AttendanceByStatusReport from './AttendanceByStatusReport'
// import ClassAttendanceReport from './ClassAttendanceReport'
// import ClassStudentReport from './ClassStudentReport'
// import ClassStudentSummaryReport from './ClassStudentSummaryReport'
import IntlMessage from 'components/util-components/IntlMessage';
import { Link } from "react-router-dom";
import AttendanceDashboard from './AttendanceDashboard/Index';
import AttendanceReports from './AttendanceReports';
import OrganizationSelect from "../OrganizationDropdown/OrganizationSelect";
import { USER_INFORMATION } from 'constants/AuthConstant';
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

// const { Option } = Select;
// const defaultYear = JSON.parse(localStorage.getItem(DEFAULT_YEAR));

function Index() {
  const dispatch = useDispatch();
  // const [selectedReport, setSelectedReport] = useState('stdReport');
  // const [pageLoad, setPageLoad] = useState(true);

  useEffect(() => {
    dispatch(getAttendanceCodes());
    dispatch(getManageClass(null)).then((result) => {
      // setPageLoad(false)
    });
  }, []);

  // const { ManageClassResult } = useSelector((state) => state[MANAGE_CLASS])  

  // const navigate = useNavigate();

  // const handleSelectChange = (value) => {
  //   setSelectedReport(value);
  // };

  const [view, setView] = useState('dashboard');
  const switchToView = (viewType) => {
    setView(viewType);
  };
  return (
    <>
      <div className='d-flex justify-content-between'>
        <Breadcrumb className="mt-2 mx-2">
          <Breadcrumb.Item>
            <Link to="/app/default">{setLocale('home')}</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{setLocale('attendance_report.title')}</Breadcrumb.Item>
        </Breadcrumb>
        <div className="d-flex justify-content-end mt-1 mb-2 mr-3" style={{ marginTop: "-40px" }}>
          <Tooltip title="Dashboard">
            <Button
              icon={<DashboardOutlined />}
              onClick={() => switchToView('dashboard')}
              style={{ marginRight: '8px' }}
              type={view === 'dashboard' ? 'primary' : 'default'}
            />
          </Tooltip>
          <Tooltip title="Reports">
            <Button
              icon={<FileDoneOutlined />}
              onClick={() => switchToView('reports')}
              type={view === 'reports' ? 'primary' : 'default'}
            />
          </Tooltip>
        </div>
      </div>

      {view === 'dashboard' ? <AttendanceDashboard /> : <AttendanceReports />}

    </>
  );
}

export default Index;




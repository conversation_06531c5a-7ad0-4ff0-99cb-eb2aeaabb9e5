import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import moment from "moment";
import {
  Row,
  Col,
  Table,
  Modal,
  Card,
  DatePicker,
  Skeleton,
  Tag,
  Button,
  Select,
} from "antd";
import { EyeOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import {
  detailChargeTypeInvoices,
  chargeTypeDrawerStatus,
  updateChargeTypeFilters,
} from "store/slices/Invoices/manageInvoicesSlice";
import { getSchoolYear } from "store/slices/SchoolYear/manageSchoolYearSlice";
import { getGradeLevel } from "store/slices/GradeLevel/manageGradeLevelSlice";
import { GRADE_LEVEL, INVOICES, SCHOOL_YEAR } from "constants/AppConstants";
import { DATE_FORMAT_YYYY_MM } from "constants/DateConstant";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function TotalInvoicesGenerated() {
  const dispatch = useDispatch();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [detailData, setDetailData] = useState(false);

  const {
    chargeTypeFilter,
    detailChargeTypeInvoice,
    DetailChargeTypeInvoicesLoading
  } = useSelector((state) => state[INVOICES]);
  const { GradeLevelResult } = useSelector((state) => state[GRADE_LEVEL]);
  const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);

  const disabledDate = (current) => {
    // Get the current date
    const currentDate = moment();

    const validSchoolYears = SchoolYearResult?.data?.filter(item => {
      const start = moment(item.start_date);
      const end = moment(item.end_date);
      return currentDate >= start && currentDate <= end;
    });

    // Check if current date is within the valid school years
    return !validSchoolYears.some(item => {
      const start = moment(item.start_date);
      const end = moment(item.end_date);
      return current >= start && current <= end;
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        await dispatch(getSchoolYear({ perPage: 1000 }));
        await dispatch(getGradeLevel({ perPage: 1000 }));
        await dispatch(detailChargeTypeInvoices(chargeTypeFilter));
      } catch (error) {
        console.error('Error fetching school year data:', error);
      }
    };
    fetchData();

  }, []);

  const getSelectedMonth = (date) => {
    dispatch(detailChargeTypeInvoices({ ...chargeTypeFilter, date: date }));
  };

  const getSelectedGrade = (grade) => {
    dispatch(detailChargeTypeInvoices({ ...chargeTypeFilter, grade: grade }));
  };



  const showModal = (record) => {
    let filteredData = record;

    if (chargeTypeFilter.date !== null || chargeTypeFilter.grade !== null) {
      const date = moment(chargeTypeFilter.date);
      const month = date.month();
      filteredData = record.filter((item) => {
        const invoiceDate = new Date(item.invoice.invoice_date);
        const organizationGradeLevelId =
          item.invoice.organization_grade_level_id;
        const isDateMatch =
          chargeTypeFilter.date === null || invoiceDate.getMonth() === month;
        const isGradeMatch =
          chargeTypeFilter.grade === null || organizationGradeLevelId === chargeTypeFilter.grade;
        return isDateMatch || isGradeMatch;
      });
    }

    setDetailData(filteredData);
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const formattedAmount = (record) => {
    if (record === null) {
      return "";
    }
    const formattedValue = record.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    return `$ ${formattedValue}`;
  }

  const columns = [
    {
      title: setLocale("invoices.charge_category"),
      key: "grade_level",
      render: (record) => <>{record.charge_category.charge_category}</>,
    },
    {
      title: setLocale("invoices.charge_type"),
      dataIndex: "charge_type",
      key: "charge_type",
    },
    {
      title: setLocale("invoices.total_invoices_detail_count"),
      key: "invoice_details_count",
      render: (record) => {
        let tagColor = 'gold';

        if (record.invoice_details_count === 'Invoice') {
          tagColor = 'geekblue';
        } else if (record.invoice_details_count === 'Paid') {
          tagColor = 'green';
        } else if (record.invoice_details_count === 'Partially Paid') {
          tagColor = 'purple';
        }

        return <Tag color={tagColor}>{record.invoice_details_count}</Tag>;
      }
    },
    {
      title: setLocale("invoices.detail"),
      dataIndex: "operation",
      render: (data, record) => (
        <>
          {/* <EyeOutlined
            style={{ fontSize: "15px" }}
            className="text-success"
            onClick={(e) => showModal(record.invoice_details)}
          /> */}
        </>
      ),
    },
  ];

  const detailDataColumns = [
    {
      title: setLocale("invoices.student_name"),
      key: "full_name",
      render: (record) => <>{record.invoice.student.full_name}</>,
    },
    {
      title: setLocale("invoices.invoice"),
      key: "invoice_no",
      render: (record) => <>{record.invoice.invoice_no}</>,
    },
    {
      title: setLocale("invoices.invoice_date"),
      key: "invoice_date",
      render: (record) => (
        <>
          <Tag color="gold">{record.invoice.invoice_date}</Tag>
        </>
      ),
    },
    {
      title: setLocale("invoices.invoice_status"),
      key: "invoice_status_code",
      render: (record) => {
        let tagColor = 'gold';

        if (record.invoice.invoice_status_code === 'Invoice') {
          tagColor = 'geekblue';
        } else if (record.invoice.invoice_status_code === 'Paid') {
          tagColor = 'green';
        } else if (record.invoice.invoice_status_code === 'Partially Paid') {
          tagColor = 'purple';
        }

        return <Tag color={tagColor}>{record.invoice.invoice_status_code}</Tag>;
      }
    },
    {
      title: setLocale("invoices.charge_amount"),
      key: "charge_amount",
      render: (record) => (
        <>
          <span style={{ display: 'inline-block', width: '100%', textAlign: 'right' }}>
            <Tag color="cyan">{formattedAmount(record.charge_amount)}</Tag>
          </span>
        </>
      ),
    },
  ];

  return (
    <>
      <Card
        title={
          <h5>
            <span style={{ cursor: "pointer", float: "right" }}
              onClick={() => {
                dispatch(chargeTypeDrawerStatus({ drawerStatus: false, mainStatus: true }))
              }}
            >
              <ArrowLeftOutlined /> {setLocale('invoices.invoices_generated_for_charge_types')}
            </span>{" "}
          </h5>
        }
      >

        <Row gutter={16}>
          <Col span={12}>
            <Select
              className="rounded-0 w-100"
              showSearch
              optionLabelProp="label"
              allowClear
              placeholder={setLocale("invoices.school_grade")}
              value={chargeTypeFilter?.grade}
              optionFilterProp="children"
              filterOption={(input, option) => (
                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
              )}
              // filterSort={(optionA, optionB) =>
              //   (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
              // }
              onChange={(value) => {
                dispatch(updateChargeTypeFilters({
                  ...chargeTypeFilter,
                  grade: value,
                }));
                getSelectedGrade(value);
              }}
              options={GradeLevelResult.data ?? []}
            />
          </Col>

          <Col span={12}>
            <DatePicker.MonthPicker
              disabledDate={disabledDate}
              value={chargeTypeFilter?.date}
              onChange={(value) => {
                dispatch(updateChargeTypeFilters({
                  ...chargeTypeFilter,
                  date: value,
                }));
                getSelectedMonth(value)
              }}
              format={DATE_FORMAT_YYYY_MM} className="rounded-0 w-100" />
          </Col>
        </Row>

        <Row gutter={16} className="my-5">
          <Skeleton loading={DetailChargeTypeInvoicesLoading} active >
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Table
                columns={columns}
                dataSource={detailChargeTypeInvoice ?? []}
              />
              <Modal
                title="Detail"
                open={isModalOpen}
                width={800}
                onOk={handleOk}
                onCancel={handleCancel}
                footer={[
                  <Button key="back" onClick={handleCancel}>
                    {("invoices.close")}
                  </Button>,
                ]}
              >
                <Table
                  columns={detailDataColumns}
                  dataSource={detailData ?? []}
                />
              </Modal>
            </Col>
          </Skeleton>
        </Row>
      </Card>
    </>
  );
}

export default TotalInvoicesGenerated;

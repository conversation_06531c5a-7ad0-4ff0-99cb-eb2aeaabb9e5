import React, { useEffect } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { Popconfirm, Modal, Tooltip, Avatar, List, Tag, Skeleton, Card, Button } from 'antd';
import { env } from "configs/EnvironmentConfig"
import { DeleteOutlined, UserOutlined } from '@ant-design/icons';
import {
  setSingleAssignee, setAddStudentOnlyModal
  , removeSingleStudent
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import { getColor } from "components/composeable"
import IntlMessage from "components/util-components/IntlMessage"
import AddStudentOnly from './../AddStudentOnly'
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function StudentAssignees() {
  const { addStudentOnlyModal, singleAssignment, assigneeLoading } = useSelector((state) => state[ASSIGNMENT_SLICE]);
  const dispatch = useDispatch();

  useEffect(() => {
    //  
  }, [])

  const handleSingleAssignee = async (values) => {
    await dispatch(setSingleAssignee(values))
  }

  const handleRemoveAssignee = async (values) => {
    const data = { id: values?.id }
    await dispatch(removeSingleStudent(data))
  }
  return (
    <>
      <Card type="inner" title={setLocale('All Students')} style={{ maxHeight: '90vh', overflowX: 'auto' }}
        extra={
          !assigneeLoading &&
          <Button type="link" danger onClick={() => { dispatch(setAddStudentOnlyModal(true)) }}>
            <UserOutlined /> {setLocale('Add')}
          </Button>
        }>
        <Skeleton avatar title={false} loading={assigneeLoading} active></Skeleton>
        <Skeleton avatar title={false} loading={assigneeLoading} active></Skeleton>
        <Skeleton avatar title={false} loading={assigneeLoading} active></Skeleton>
        {!assigneeLoading ? <List itemLayout="horizontal"
          dataSource={singleAssignment ? singleAssignment?.assignees : []}
          renderItem={(assignee, index) => (
            <List.Item key={index} className='cursor-pointer'
              extra={
                <Tooltip title={setLocale('Remove Assignee')}>
                  <Popconfirm
                    title={setLocale('assignment.sure_to_remove_assignee')}
                    onConfirm={() => setTimeout(() => handleRemoveAssignee(assignee), 0)}
                    onCancel={() => { }}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button type="link" danger >
                      <DeleteOutlined className="text-danger" />
                    </Button>
                  </Popconfirm>
                </Tooltip>
              }>
              <List.Item.Meta key={index}
                onClick={() => handleSingleAssignee(assignee)}
                avatar={
                  <Avatar src={assignee?.student?.profile_picture ? env.FILE_ENDPOINT_URL + assignee?.student?.profile_picture : <Avatar icon={<UserOutlined />} />} />
                } title={assignee?.student?.full_name + ' (' + assignee?.student?.student_id + ')'}
                description={<Tag color={getColor(assignee?.submission_status)}>{assignee?.submission_status}</Tag>}
              />
            </List.Item>
          )}
        /> : null}
      </Card>
      {addStudentOnlyModal && <AddStudentOnly />}
    </>
  )
}

export default StudentAssignees;
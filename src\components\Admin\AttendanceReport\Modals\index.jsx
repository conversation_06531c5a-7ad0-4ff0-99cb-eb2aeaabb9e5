import React from 'react';
// import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
// import { useDispatch, useSelector } from "react-redux";
// import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import {
//     AttendanceReportAddDrawerStatus,
//     createAttendanceReport,
//     getAttendanceReport
//   } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
// import IntlMessage from "components/util-components/IntlMessage";
// const setLocale = (localeKey, isLocaleOn = true) =>
//     isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

// const { TextArea } = Input;


const Index = () => {
// const [form] = Form.useForm();
// const dispatch = useDispatch();
//     const { AttendanceReportAddDrawer, AttendanceReportButtonAndModelLabel, AttendanceReportErrors, AttendanceReportShowMessage, AttendanceReportButtonSpinner, AttendanceReportEditData, tablePagination } = useSelector(
//         (state) => state[ATTENDANCE_REPORT]
//       );
// const onClose = () => dispatch(AttendanceReportAddDrawerStatus(false));

// const onSubmit = async (formValues) => {

  // if (AttendanceReportEditData && AttendanceReportEditData.id) {
  //   // If editing, include the id in the form values
  //   formValues.id = AttendanceReportEditData.id;
  // }

  // await dispatch(createAttendanceReport(formValues))
  //   .then(() => {
  //     if (Object.keys(AttendanceReportErrors).length == 0) {
  //       dispatch(getAttendanceReport({
  //         page: tablePagination.current,
  //         perPage: tablePagination.pageSize
  //       }));
  //       // form.resetFields();
  //     }
  //   })
  //   .catch((error) => {
  //     // Handle delete error
  //     console.error("Error deleting module:", error);
  //   });
// };

// const onFinishFailed = (errorsInfo) => {
//   console.log("Form submission failed:", errorsInfo);
// };

  return (
    <>

      {/* <Drawer
        title={AttendanceReportButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        // onClose={onClose}
        open={AttendanceReportAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: AttendanceReportEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={AttendanceReportShowMessage && AttendanceReportErrors.name ? "error" : ""}
                help={AttendanceReportShowMessage && AttendanceReportErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AttendanceReportButtonSpinner}
            >
              {AttendanceReportButtonAndModelLabel}
            </Button>
           <Button onClick={onClose}>{setLocale('Cancel')}</Button> 
          </Space>
        </Form>
      </Drawer> */}
    </>
  );
};
export default Index;


import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CALENDAR_CATEGORY } from "constants/AppConstants";
import {
    CalendarCategoryAddDrawerStatus,
    createCalendarCategory,
    getCalendarCategory,
    onCloseError
  } from "store/slices/CalendarCategory/manageCalendarCategorySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {DrawerStatus,sorting, filter, CalendarCategoryAddDrawer, CalendarCategoryButtonAndModelLabel, CalendarCategoryErrors, CalendarCategoryShowMessage, CalendarCategoryButtonSpinner, CalendarCategoryEditData, tablePagination } = useSelector(
        (state) => state[CALENDAR_CATEGORY]
      );
const onClose = () => {
    dispatch(CalendarCategoryAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (CalendarCategoryEditData && CalendarCategoryEditData.id) {
    // If editing, include the id in the form values
    formValues.id = CalendarCategoryEditData.id;
  }

  await dispatch(createCalendarCategory(formValues))
};
useEffect(() => {
    if (Object.keys(CalendarCategoryErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getCalendarCategory({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [CalendarCategoryErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={CalendarCategoryButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CalendarCategoryAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: CalendarCategoryEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={CalendarCategoryShowMessage && CalendarCategoryErrors.name ? "error" : ""}
                help={CalendarCategoryShowMessage && CalendarCategoryErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CalendarCategoryButtonSpinner}
            >
              
              {CalendarCategoryButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


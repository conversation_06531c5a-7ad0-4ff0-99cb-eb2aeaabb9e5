import React, { useRef, useState, useEffect } from 'react'
// import moment from 'moment';
import { useSelector, useDispatch } from "react-redux";
import { CHAT } from "constants/chat/index";
import { Avatar, Divider, Input, Form, Button, Menu } from 'antd';
import {
	SendOutlined,
	PaperClipOutlined, 
	SmileOutlined, 
} from '@ant-design/icons';
import {
	sendMessage,
	setPushNewMessage,
	setUploadFileFormDrawer	
} from "store/slices/Chat/manageChatSlice.js";
import { USER_INFORMATION } from "constants/AuthConstant";
import debounce from 'lodash.debounce';
import Echo from 'broadcasting/laravelEcho'
import moment from 'moment-timezone';
import { DEFAULT_TIME_ZONE } from "constants/DateConstant";

const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION)) || null;

const ChatContentFooter = () => {
	const dispatch = useDispatch();
	const { 
		selectedConversation,
		uploadFileFormDrawer 
	} = useSelector((state) => state[CHAT]);

	const formRef = useRef();
	const onSend = async (values) => {

		if (values.message) {			
			const newMsgData = {
				conversation_id: selectedConversation.conversation_id,
				type: "text",
				message: values.message,
				created_at: moment().tz(DEFAULT_TIME_ZONE).format('MMM D, YYYY hh:mm A'),
				user_id: currentUser.id,
				name: currentUser.name
			};

			await dispatch(setPushNewMessage(newMsgData));
			formRef.current.setFieldsValue({
				message: ''
			});
			await dispatch(sendMessage(newMsgData));

		}
	};

	const uploadFile = (e) => {
		e.preventDefault();
		dispatch(setUploadFileFormDrawer(true));
	};
	
	const handleTyping = debounce(() => {
		Echo.private(`user-chat-${selectedConversation.conversation_id}`)
        .whisper('typing', {
            user_id: currentUser.id,
            name: currentUser.name,
            // conversation_id: selectedConversation.conversation_id,
        });
		
	}, 300);

	return (
		<div className="chat-content-footer">
			<Form name="msgInput" ref={formRef} onFinish={onSend} className="w-100" onKeyPress={(e) => {
				if (e.key === 'Enter') {
					onSend(e);
				}
			}}>
				<Form.Item name="message" className="mb-0">
					<Input
						autoComplete="off"
						placeholder="Type a message..."
						onChange={(e) => { handleTyping(); }}
						suffix={
							<div className="d-flex align-items-center">
								{/* <a href="/#"  className="text-dark font-size-lg mr-3" onClick={emptyClick}>
									<SmileOutlined />
								</a> */}
								<a href="/#" className="text-dark font-size-lg mr-3" onClick={uploadFile}>
									<PaperClipOutlined />
								</a>
								<Button shape="circle" type="primary" size="small" onClick={onSend} htmlType="submit">
									<SendOutlined />
								</Button>
							</div>
						}
					/>
				</Form.Item>
			</Form>
		</div>
	)
}

export default ChatContentFooter

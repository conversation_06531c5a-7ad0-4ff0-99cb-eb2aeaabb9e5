import React, { useState, useEffect, useRef } from "react";
import { Row, Col, Card, Button, Progress, Divider, Tabs, Select, Table, Badge } from 'antd';
import Flex from 'components/shared-components/Flex'
import { EyeOutlined, DownloadOutlined, PlusOutlined, EditOutlined  } from '@ant-design/icons';
import Chart from "react-apexcharts";
import IntlMessage from "components/util-components/IntlMessage";
import ChartWidget from 'components/shared-components/ChartWidget';
import ApexChart from "react-apexcharts";
import { apexSparklineChartDefultOption, COLORS } from 'constants/ChartConstant';


const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {


  const handleChange = (value) => {
    console.log(`selected ${value}`);
  };


  // const conicColors = {
  //   '0%': '#338417',    
  //   '35%': '#338417',   
  //   '40%': '#21B252',  
  //   '55%': '#21B252',   
  //   '75%': '#7FD74B',   
  //   '100%': '#7FD74B',  
  // };

  const conicColors = {
    '0%': '#338417',
    '34.9%': '#338417',
    '35%': '#000000',  // Black border
    '35.1%': '#21B252',
    '64.9%': '#21B252',
    '65%': '#000000',  // Black border
    '65.1%': '#7FD74B',
    '100%': '#7FD74B',
  };
    return (
        <>
            <Row className="all-filter">
              <Col span={24}>
                <Card className="pl-3 card">
                <Row>
                  <Col span={24}>
                  <div className="d-flex justify-content-between">
                    <div className="mr-4">
                      <div className="d-flex">
                        <span className="mediumHead-2 mt-2 mr-2">Select Grade: </span>
                        <Select
                          defaultValue="All Selected"
                          onChange={handleChange}
                          options={[
                            { value: '1', label: 'All Selected',},
                          ]}
                        />
                      </div>
                    </div>
                    <div>
                        <Button><PlusOutlined /> Add Request</Button>
                    </div> 
                  </div>
                  </Col>
                </Row>
                </Card>
              </Col>
            </Row>
            <h1>Grade 1</h1>
            <Row gutter={16}>
                <Col span={12}>
                  <Card className="p-2">
                    <div className="d-flex justify-content-between">
                         <h5 className="semiHead-3">Consumable</h5>
                         <div>
                            <EditOutlined />
                         </div>                        
                    </div>

                    <Progress percent={99.9} strokeColor={conicColors} showInfo={false}/>
                    <Row className="mt-2">
                      <Col span={8}style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="total-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Total Budget</h5>
                          <span>100%</span>
                        </div>
                        <div className="semiHead-2 ">$5,000</div>
                      </Col>
                      <Col span={8} className="pl-2" style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="consume-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Consumed</h5>
                          <span>14%</span>
                        </div>
                        <div className="semiHead-2 ">$3,000</div>
                      </Col>
                      <Col span={8} className="pl-2">
                        <div className="d-flex">
                          <span className="balance-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Balance</h5>
                          <span>88%</span>
                        </div>
                        <div className="semiHead-2 ">$2,000</div>
                      </Col>
                    </Row>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card className="p-2">
                    <div className="d-flex justify-content-between">
                         <h5 className="semiHead-3">Field Trip</h5>
                         <div>
                            <EditOutlined />
                         </div>                        
                    </div>

                    <Progress percent={99.9} strokeColor={conicColors} showInfo={false}/>
                    <Row className="mt-2">
                      <Col span={8}style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="total-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Total Budget</h5>
                          <span>100%</span>
                        </div>
                        <div className="semiHead-2 ">$5,000</div>
                      </Col>
                      <Col span={8} className="pl-2" style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="consume-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Consumed</h5>
                          <span>14%</span>
                        </div>
                        <div className="semiHead-2 ">$3,000</div>
                      </Col>
                      <Col span={8} className="pl-2">
                        <div className="d-flex">
                          <span className="balance-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Balance</h5>
                          <span>88%</span>
                        </div>
                        <div className="semiHead-2 ">$2,000</div>
                      </Col>
                    </Row>
                  </Card>
                </Col>
            </Row>
            <h1>Grade 2</h1>
            <Row gutter={16}>
                <Col span={12}>
                  <Card className="p-2">
                    <div className="d-flex justify-content-between">
                         <h5 className="semiHead-3">Consumable</h5>
                         <div>
                            <EditOutlined />
                         </div>                        
                    </div>

                    <Progress percent={99.9} strokeColor={conicColors} showInfo={false}/>
                    <Row className="mt-2">
                      <Col span={8}style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="total-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Total Budget</h5>
                          <span>100%</span>
                        </div>
                        <div className="semiHead-2 ">$5,000</div>
                      </Col>
                      <Col span={8} className="pl-2" style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="consume-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Consumed</h5>
                          <span>14%</span>
                        </div>
                        <div className="semiHead-2 ">$3,000</div>
                      </Col>
                      <Col span={8} className="pl-2">
                        <div className="d-flex">
                          <span className="balance-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Balance</h5>
                          <span>88%</span>
                        </div>
                        <div className="semiHead-2 ">$2,000</div>
                      </Col>
                    </Row>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card className="p-2">
                    <div className="d-flex justify-content-between">
                         <h5 className="semiHead-3">Field Trip</h5>
                         <div>
                            <EditOutlined />
                         </div>                        
                    </div>

                    <Progress percent={99.9} strokeColor={conicColors} showInfo={false}/>
                    <Row className="mt-2">
                      <Col span={8}style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="total-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Total Budget</h5>
                          <span>100%</span>
                        </div>
                        <div className="semiHead-2 ">$5,000</div>
                      </Col>
                      <Col span={8} className="pl-2" style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="consume-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Consumed</h5>
                          <span>14%</span>
                        </div>
                        <div className="semiHead-2 ">$3,000</div>
                      </Col>
                      <Col span={8} className="pl-2">
                        <div className="d-flex">
                          <span className="balance-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Balance</h5>
                          <span>88%</span>
                        </div>
                        <div className="semiHead-2 ">$2,000</div>
                      </Col>
                    </Row>
                  </Card>
                </Col>
            </Row>
            <h1>Grade 3</h1>
            <Row gutter={16}>
                <Col span={12}>
                  <Card className="p-2">
                    <div className="d-flex justify-content-between">
                         <h5 className="semiHead-3">Consumable</h5>
                         <div>
                            <EditOutlined />
                         </div>                        
                    </div>

                    <Progress percent={99.9} strokeColor={conicColors} showInfo={false}/>
                    <Row className="mt-2">
                      <Col span={8}style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="total-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Total Budget</h5>
                          <span>100%</span>
                        </div>
                        <div className="semiHead-2 ">$5,000</div>
                      </Col>
                      <Col span={8} className="pl-2" style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="consume-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Consumed</h5>
                          <span>14%</span>
                        </div>
                        <div className="semiHead-2 ">$3,000</div>
                      </Col>
                      <Col span={8} className="pl-2">
                        <div className="d-flex">
                          <span className="balance-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Balance</h5>
                          <span>88%</span>
                        </div>
                        <div className="semiHead-2 ">$2,000</div>
                      </Col>
                    </Row>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card className="p-2">
                    <div className="d-flex justify-content-between">
                         <h5 className="semiHead-3">Field Trip</h5>
                         <div>
                            <EditOutlined />
                         </div>                        
                    </div>

                    <Progress percent={99.9} strokeColor={conicColors} showInfo={false}/>
                    <Row className="mt-2">
                      <Col span={8}style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="total-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Total Budget</h5>
                          <span>100%</span>
                        </div>
                        <div className="semiHead-2 ">$5,000</div>
                      </Col>
                      <Col span={8} className="pl-2" style={{borderRight: "1px solid #c9c9c9"}}>
                        <div className="d-flex">
                          <span className="consume-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Consumed</h5>
                          <span>14%</span>
                        </div>
                        <div className="semiHead-2 ">$3,000</div>
                      </Col>
                      <Col span={8} className="pl-2">
                        <div className="d-flex">
                          <span className="balance-budget mt-2 mr-1"></span>
                          <h5 className="semiHead-5 mr-3">Balance</h5>
                          <span>88%</span>
                        </div>
                        <div className="semiHead-2 ">$2,000</div>
                      </Col>
                    </Row>
                  </Card>
                </Col>
            </Row>

        </>
    );
}

export default Index;

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Col,
  Drawer,
  Form,
  Input,
  Row,
  Space,
  DatePicker,
  Checkbox,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LEAVE_ALLOCATION } from "constants/AppConstants";
import {
  LeaveAllocationAddDrawerStatus,
  createLeaveAllocation,
  getLeaveAllocation,
  onCloseError,
} from "store/slices/LeaveAllocation/manageLeaveAllocationSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { LeaveType } from "components/Admin/EmployeeLeave/components/ui/Select/LeaveType";
import { AllocationType } from "components/Admin/EmployeeLeave/components/ui/Select/AllocationType";
import { Range } from "components/Admin/EmployeeLeave/components/ui/Select/Range";
import { Days } from "components/Admin/EmployeeLeave/components/ui/Select/Days";
import { Designation } from "components/Admin/EmployeeLeave/components/ui/Select/Designation";
import moment from "moment";
const { RangePicker } = DatePicker;
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [allocationType, setAllocationType] = useState("designation");

  const {
    LeaveAllocationAddDrawer,
    LeaveAllocationButtonAndModelLabel,
    sorting,
    filter,
    LeaveAllocationErrors,
    LeaveAllocationShowMessage,
    LeaveAllocationButtonSpinner,
    LeaveAllocationEditData,
    tablePagination,
    DrawerStatus,
    selectedLeaveType, // Access the selected leave type record
  } = useSelector((state) => state[LEAVE_ALLOCATION]);

  useEffect(() => {
    if (LeaveAllocationEditData?.allocation_type) {
      setAllocationType(LeaveAllocationEditData.allocation_type);
    }
  }, [LeaveAllocationEditData]);

  const onClose = () => {
    dispatch(LeaveAllocationAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  };
  const onSubmit = async (formValues) => {
    if (LeaveAllocationEditData && LeaveAllocationEditData.id) {
      formValues.id = LeaveAllocationEditData.id;
    }
    const formatedValues = {
      ...formValues,
      start_date: formValues.date
        ? formValues.date[0].format("YYYY-MM-DD")
        : null,
      end_date: formValues.date
        ? formValues.date[1].format("YYYY-MM-DD")
        : null,
    };
    await dispatch(createLeaveAllocation(formatedValues));
  };

  useEffect(() => {
    if (Object.keys(LeaveAllocationErrors).length === 0 && DrawerStatus === 0) {
      dispatch(
        getLeaveAllocation({
          page: tablePagination.current,
          perPage: tablePagination.pageSize,
          filter: filter,
          sorting: sorting,
        })
      );
    }
  }, [LeaveAllocationErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>
      <Drawer
        title={LeaveAllocationButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={LeaveAllocationAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            ...LeaveAllocationEditData,
            designation_id:
              LeaveAllocationEditData?.designation?.id || undefined,
            leave_type_id: LeaveAllocationEditData?.leave_type?.id || selectedLeaveType?.id || undefined,
            carry_forward:
              LeaveAllocationEditData?.carry_forward === 1 ? true : false,
            document_required:
              LeaveAllocationEditData?.document_required === 1 ? true : false,
            date:
              LeaveAllocationEditData?.start_date &&
              LeaveAllocationEditData?.end_date
                ? [
                    moment(LeaveAllocationEditData.start_date),
                    moment(LeaveAllocationEditData.end_date),
                  ]
                : undefined,
            allocation_type: LeaveAllocationEditData?.allocation_type || "designation",
          }}
        >
          <Row gutter={[16, 16]} style={{ marginTop: "1rem" }}>
            <Col xs={12}>
              <Form.Item
                label="Allocation Type :"
                name="allocation_type"
                rules={[
                  { required: true, message: "Please select allocation type" },
                ]}
              >
                <AllocationType onChange={setAllocationType} />
              </Form.Item>
            </Col>
            <Col xs={12}>
              {allocationType === "designation" ? (
                <Form.Item
                  label="Choose Designation :"
                  name="designation_id"
                  rules={[
                    { required: true, message: "Please select designation" },
                  ]}
                >
                  <Designation />
                </Form.Item>
              ) : (
                <Form.Item
                  label="Employment Length :"
                  name="employment_length"
                  rules={[
                    {
                      required: true,
                      message: "Please select employment length",
                    },
                  ]}
                >
                  <Range />
                </Form.Item>
              )}
            </Col>
          </Row>
          <Row gutter={[16, 16]} style={{ marginTop: "1rem" }}>
            <Col xs={12}>
              <Form.Item
                label="Type of Leave :"
                name="leave_type_id"
                rules={[
                  { required: true, message: "Please select leave type" },
                ]}
              >
                <LeaveType showAll={true} />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label="No of Days :"
                name="no_of_days"
                rules={[{ required: true, message: "Please select days" }]}
              >
                <Days />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={12}>
              <Form.Item
                label="Should a document be required :"
                name="document_required"
                valuePropName="checked"
              >
                <Checkbox>yes</Checkbox>
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label="Valid from - Valid till:"
                name="date"
                rules={[{ required: true, message: "Please enter date" }]}
              >
                <RangePicker style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                label="Carry Forward:"
                name="carry_forward"
                valuePropName="checked"
              >
                <Checkbox>yes</Checkbox>
              </Form.Item>
            </Col>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.carry_forward !== currentValues.carry_forward
              }
            >
              {({ getFieldValue }) =>
                getFieldValue("carry_forward") === true && (
                  <Col xs={12}>
                    <Form.Item
                      label="Max Days Carry Forward:"
                      name="max_days_carry_forward"
                      rules={[
                        { required: true, message: "Please select max days" },
                      ]}
                    >
                      <Days />
                    </Form.Item>
                  </Col>
                )
              }
            </Form.Item>
          </Row>

          <Row>
            <Col xs={12}>
              <Form.Item
                label="Max Leaves Allowed Together:"
                name="max_leaves_together"
                rules={[
                  {
                    required: true,
                    message: "Please enter max leaves allowed together",
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={LeaveAllocationButtonSpinner}
            >
              {LeaveAllocationButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale("Cancel")}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;

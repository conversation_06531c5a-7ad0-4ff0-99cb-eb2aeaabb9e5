import React, { useEffect, useState, useMemo } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Radio, DatePicker } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LEAVE_ALLOCATION } from "constants/AppConstants";
import {
    LeaveAllocationAddDrawerStatus,
    createLeaveAllocation,
    getLeaveAllocation,
    onCloseError
  } from "store/slices/LeaveAllocation/manageLeaveAllocationSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { LeaveType } from 'components/Admin/EmployeeLeave/components/ui/Select/LeaveType';
import { AllocationType } from 'components/Admin/EmployeeLeave/components/ui/Select/AllocationType';
import { Range } from 'components/Admin/EmployeeLeave/components/ui/Select/Range';
import { Days } from 'components/Admin/EmployeeLeave/components/ui/Select/Days';
import { Designation } from 'components/Admin/EmployeeLeave/components/ui/Select/Designation';
import moment from "moment";
const { RangePicker } = DatePicker;
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [allocationType, setAllocationType] = useState("designation");
  const [dataLoaded, setDataLoaded] = useState(false);
  const {
    LeaveAllocationAddDrawer,
    LeaveAllocationButtonAndModelLabel,
    sorting,
    filter,
    LeaveAllocationErrors,
    LeaveAllocationShowMessage,
    LeaveAllocationButtonSpinner,
    LeaveAllocationEditData,
    tablePagination,
    DrawerStatus
  } = useSelector(
    (state) => state[LEAVE_ALLOCATION]
  );
  debugger;
  const onClose = () => {
    dispatch(LeaveAllocationAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  };
  const onSubmit = async (formValues) => {
    const formattedValues = {
      ...formValues,
      start_date: formValues.date ? formValues.date[0].format("YYYY-MM-DD") : null,
      end_date: formValues.date ? formValues.date[1].format("YYYY-MM-DD") : null,
    };

    if (LeaveAllocationEditData && LeaveAllocationEditData.id) {
      // If editing, include the id in the form values
      formattedValues.id = LeaveAllocationEditData.id;
    }

    // Create FormData for proper handling in CommonService
    const formData = new FormData();
    Object.entries(formattedValues).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        formData.append(key, value);
      }
    });

    await dispatch(createLeaveAllocation(formData));
  };

  useEffect(() => {
    // Only fetch data if drawer is closed AND we haven't loaded data yet
    if (Object.keys(LeaveAllocationErrors).length === 0 && DrawerStatus === 0 && !dataLoaded) {
      if (tablePagination && sorting && filter) {
        dispatch(getLeaveAllocation({
          page: tablePagination.current,
          perPage: tablePagination.pageSize,
          filter: filter, 
          sorting: sorting
        }));
        setDataLoaded(true); // Mark data as loaded
      }
    }
  }, [LeaveAllocationErrors, DrawerStatus, dataLoaded]);

  useEffect(() => {
    // Reset the dataLoaded flag when drawer closes
    if (!LeaveAllocationAddDrawer) {
      setDataLoaded(false);
    }
  }, [LeaveAllocationAddDrawer]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>
      <Drawer
        title={LeaveAllocationButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={LeaveAllocationAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            ...LeaveAllocationEditData,
            designation_id: LeaveAllocationEditData?.designation?.id || undefined,
            leave_type_id: LeaveAllocationEditData?.leave_type?.id || undefined,
            carry_forward: LeaveAllocationEditData?.carry_forward === 1 ? true : false,
            document_required: LeaveAllocationEditData?.document_required === 1 ? true : false,
            date:
              LeaveAllocationEditData?.start_date && LeaveAllocationEditData?.end_date
                ? [
                    moment(LeaveAllocationEditData.start_date),
                    moment(LeaveAllocationEditData.end_date),
                  ]
                : undefined,
          }}
        >
          <Row gutter={[16, 16]} style={{ marginTop: "1rem" }}>
            <Col xs={12}>
              <Form.Item
                label="Allocation Type :"
                name="allocation_type"
                rules={[{ required: true, message: "Please select allocation type" }]}
              >
                <AllocationType onChange={setAllocationType} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]} style={{ marginTop: "1rem" }}>
            <Col xs={12}>
              {allocationType === "designation" ? (
                <Form.Item
                  label="Choose Designation :"
                  name="designation_id"
                  rules={[{ required: true, message: "Please select designation" }]}
                >
                  <Designation />
                </Form.Item>
              ) : (
                <Form.Item
                  label="Employment Length :"
                  name="employment_length"
                  rules={[{ required: true, message: "Please select employment length" }]}
                >
                  <Range />
                </Form.Item>
              )}
            </Col>
            <Col xs={12}>
              <Form.Item
                label="Type of Leave :"
                name="leave_type_id"
                rules={[{ required: true, message: "Please select leave type" }]}
              >
                <LeaveType showAll={true} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: "1rem" }}>
            <Col xs={12}>
              <Form.Item
                label="No of Days :"
                name="no_of_days"
                rules={[{ required: true, message: "Please select days" }]}
              >
                <Days />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label="Max Days Carry Forward:"
                name="max_days_carry_forward"
                // rules={[{ required: true, message: "Please select max days" }]}
              >
                <Days />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={12}>
              <Form.Item
                label="Max Leaves Allowed Together:"
                name="max_leaves_together"
                rules={[{ required: true, message: "Please enter max leaves allowed together" }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label="Date :"
                name="date"
                rules={[{ required: true, message: "Please enter date" }]}
              >
                <RangePicker style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                label="Carry Forward:"
                name="carry_forward"
                // rules={[{ required: true, message: "Please select carry forward option" }]}
              >
                <Radio.Group>
                  <Radio value={true}>Yes</Radio>
                  <Radio value={false}>No</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label="Should a document be required :"
                name="document_required"
                rules={[{ required: true, message: "Please select an option" }]}
              >
                <Radio.Group>
                  <Radio value={true}>Yes</Radio>
                  <Radio value={false}>No</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={LeaveAllocationButtonSpinner}
            >
              {LeaveAllocationButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { ENROLLMENT_STATUS } from "constants/AppConstants";
import AddEnrollmentStatusModal from "./Modals/index";
import {
    EnrollmentStatusAddDrawerStatus,
    EnrollmentStatusEditWithDrawerStatus,
    deleteEnrollmentStatus,
    getEnrollmentStatus,
    updateSortFilters,
    setColumnSearch

} from "store/slices/EnrollmentStatus/manageEnrollmentStatusSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const [searchText, setSearchText] = useState('');
    const [searchedColumn, setSearchedColumn] = useState('');
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(EnrollmentStatusAddDrawerStatus({ errorStatus: 1, status: true }));
    const { EnrollmentStatusAddDrawer, EnrollmentStatusResult, tablePagination, sorting, filter, EnrollmentStatusTableLoading, permission } = useSelector(
        (state) => state[ENROLLMENT_STATUS]
    );
    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getEnrollmentStatus({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }
    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };
    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };
    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };
    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        dispatch(getEnrollmentStatus({ page: page, perPage: pageSize, filter: filter, sorting: sorting }));
    };

    const handleDelete = (record) => {
        dispatch(deleteEnrollmentStatus(record.id)).then(() => {
            dispatch(getEnrollmentStatus({ page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting }));
        })
    }
    const handleUpdate = (record) => {
        dispatch(EnrollmentStatusEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: { ...filter, type: filters?.type?.length > 0 ? filters.type : "" }, sorting: sorting }));
            // await dispatch(
            //     getEnrollmentStatus({
            //         page: 1,
            //         perPage: tablePagination.pageSize,
            //         filter: filter,
            //         sorting: sorting,
            //     })
            // );
        } catch (error) {
            console.log(error);
        }

    };

    useEffect(()=>{
        dispatch(
            getEnrollmentStatus({
                page: 1,
                perPage: tablePagination.pageSize,
                filter: filter,
                sorting: sorting,
            })
        );
    },[filter,sorting]);


    const columns = [
        {
            title: setLocale('enrollment_status.title'),
            dataIndex: "enrollment_status",
            key: "enrollment_status",
            sorter: true,
            ...getColumnSearchProps('enrollment_status'),
        },
        {
            title: setLocale('enrollment_status.status_code'),
            dataIndex: "status_code",
            key: "status_code",
            sorter: true,
            ...getColumnSearchProps('status_code'),
        },
        {
            title: setLocale('enrollment_status.type'),
            dataIndex: "type",
            key: "type",
            sorter: true,
            filters: [
                {
                  text: 'Active',
                  value: 'active',
                },
                {
                  text: 'Inactive',
                  value: 'inactive',
                }
            ],
            render: (text, record) => (
                <>
                    <span>{text.charAt(0).toUpperCase() + text.slice(1)}</span>
                </>
            ),
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('enrollment_status.title')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale('enrollment_status.add')}
                            </Button>
                        )}
                    </section>
                    {EnrollmentStatusAddDrawer && <AddEnrollmentStatusModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={EnrollmentStatusTableLoading}
                            rowKey={record => record.id}
                            dataSource={EnrollmentStatusResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

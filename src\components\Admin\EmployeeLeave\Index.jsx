import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  Popconfirm,
  Pagination,
  Button,
  Input,
  Space,
  Breadcrumb,
  Tooltip,
  Tabs,
  Row,
  Col,
  Dropdown,
  Menu,
  Form,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  AppstoreOutlined,
  TableOutlined,
  SettingOutlined,
  DownloadOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import {
  EMPLOYEE_LEAVE,
  GET_ALL_EMPLOYEE_LEAVE_API_URL,
  GET_EMPLOYEE_LEAVE_API_URL,
} from "constants/AppConstants";
import AddEmployeeLeaveModal from "./Modals/index";
import ReasonModal from "./Modals/ReasonModal";
import ViewModal from "./Modals/ViewModal"; // Import the new component
import {
  EmployeeLeaveAddDrawerStatus,
  EmployeeLeaveEditWithDrawerStatus,
  deleteEmployeeLeave,
  getEmployeeLeave,
  updateSortFilters,
  setColumnSearch,
  createEmployeeLeave,
  exportEmployeeLeaveExcel,
  clearExportError,
} from "store/slices/EmployeeLeave/manageEmployeeLeaveSlice";
import IntlMessage from "components/util-components/IntlMessage";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Dashboard } from "./components/Dashboard/Dashboard";
import LeaveFilters from "./components/ui/Filter/LeaveFilters";

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
  const dispatch = useDispatch();
  const searchInput = useRef(null);
  const [view, setView] = useState("grid");
  const [selectedTab, setSelectedTab] = useState("history");
  const [isReasonModalVisible, setIsReasonModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [currentAction, setCurrentAction] = useState(null);
  const [reasonForm] = Form.useForm();
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [viewRecord, setViewRecord] = useState(null);

  const handleOpenModal = () =>
    dispatch(EmployeeLeaveAddDrawerStatus({ errorStatus: 1, status: true }));
  const {
    EmployeeLeaveAddDrawer,
    EmployeeLeaveResult,
    tablePagination,
    sorting,
    filter,
    EmployeeLeaveTableLoading,
    permission,
    EmployeeLeaveExportLoading,
    EmployeeLeaveExportError,
  } = useSelector((state) => state[EMPLOYEE_LEAVE]);

  const location = useLocation();
  const navigate = useNavigate();

  const params = new URLSearchParams(location.search);
  const leaveTypeFromUrl = params.get("leave_type");
  const [selectedLeaveType, setSelectedLeaveType] = useState(
    leaveTypeFromUrl || undefined
  );

  const getModuleData = async (
    page,
    perPage,
    filterData,
    sortingData,
    key = "history"
  ) => {
    const payload = {
      page,
      perPage,
      filter: filterData,
      sorting: sortingData,
    };
    if (key === "requests") {
      payload.apiUrl = GET_ALL_EMPLOYEE_LEAVE_API_URL;
    } else {
      payload.apiUrl = GET_EMPLOYEE_LEAVE_API_URL;
    }
    await dispatch(getEmployeeLeave(payload));
  };

  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, tablePagination.pageSize, filter, sorting, selectedTab);
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      newObject,
      sorting,
      selectedTab
    );
  };

  useEffect(() => {
    setSelectedLeaveType(Number(leaveTypeFromUrl) || undefined);
  }, [leaveTypeFromUrl]);

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === "") {
      confirm();
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        { ...filter, [dataIndex]: value },
        sorting,
        selectedTab
      );
    }
  };
  const handleLeaveTypeChange = async (value) => {
    setSelectedLeaveType(value);

    // Update URL - join array values with comma
    const newParams = new URLSearchParams(location.search);
    if (value && value.length > 0) {
      newParams.set(
        "leave_type",
        Array.isArray(value) ? value.join(",") : value
      );
    } else {
      newParams.delete("leave_type");
    }
    navigate({ search: newParams.toString() });

    // Update filter using setColumnSearch with leave_type_id as array
    const newFilters = {
      ...filter,
      leave_type_id: Array.isArray(value) ? value : value ? [value] : [],
    };

    await dispatch(setColumnSearch(newFilters));

    // Fetch data with updated filters
    getModuleData(
      1,
      tablePagination.pageSize,
      newFilters,
      sorting,
      selectedTab
    );
  };
  // Update the initial state to handle comma-separated values from URL
  useEffect(() => {
    if (leaveTypeFromUrl) {
      const leaveTypeArray = leaveTypeFromUrl.includes(",")
        ? leaveTypeFromUrl.split(",").map(Number)
        : [Number(leaveTypeFromUrl)];
      setSelectedLeaveType(leaveTypeArray);
    } else {
      setSelectedLeaveType(undefined);
    }
  }, [leaveTypeFromUrl]);

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  useEffect(() => {
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      filter,
      sorting,
      selectedTab
    );
  }, []);
  useEffect(() => {
    // Only run if in table view
    if (view === "table") {
      const leaveTypeIds = Array.isArray(selectedLeaveType)
        ? selectedLeaveType
        : selectedLeaveType
        ? [selectedLeaveType]
        : [];

      const newFilters = {
        ...filter,
        leave_type_id: leaveTypeIds,
      };

      dispatch(setColumnSearch(newFilters));

      getModuleData(
        1,
        tablePagination.pageSize,
        newFilters,
        sorting,
        selectedTab
      );
    }
    // eslint-disable-next-line
  }, [selectedLeaveType, view]);
  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, filters, sorting, selectedTab);
  };

  const handleDelete = (record) => {
    dispatch(deleteEmployeeLeave(record.id)).then(() => {
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        filter,
        sorting,
        selectedTab
      );
    });
  };
  const handleUpdate = (record) => {
    dispatch(
      EmployeeLeaveEditWithDrawerStatus({ errorStatus: 1, data: record })
    );
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
    };

    try {
      await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
      getModuleData(1, tablePagination.pageSize, filter, sorting, selectedTab);
    } catch (error) {
      console.log(error);
    }
  };

  const handleMenuClick = (record, e) => {
    const action = e.key;

    // If action is reject or cancel, show modal
    if (action === "rejected" || action === "cancelled") {
      setCurrentRecord(record);
      setCurrentAction(action);
      setIsReasonModalVisible(true);
    } else {
      // For approve, directly update status
      handleStatusUpdate(record, action);
    }
  };

  const handleReasonModalSubmit = async (values) => {
    const formattedValues = {
      description: currentRecord.description || "",
      from: currentRecord.from,
      to: currentRecord.to,
      leave_type_id: currentRecord.leave_type_id,
      id: currentRecord.id,
      status: currentAction,
      reason: values.reason || "", // Add reason to the payload
    };

    const formData = new FormData();
    Object.entries(formattedValues).forEach(([key, value]) => {
      formData.append(key, value);
    });

    // If you want to send the document again (optional, if needed)
    if (
      currentRecord.documents &&
      typeof currentRecord.documents !== "string"
    ) {
      formData.append("documents", currentRecord.documents);
    }

    await dispatch(createEmployeeLeave(formData));

    // Close modal and reset form
    setIsReasonModalVisible(false);
    setCurrentRecord(null);
    setCurrentAction(null);
    reasonForm.resetFields();
  };

  const handleReasonModalCancel = () => {
    setIsReasonModalVisible(false);
    setCurrentRecord(null);
    setCurrentAction(null);
    reasonForm.resetFields();
  };

  const handleStatusUpdate = async (record, status) => {
    const formattedValues = {
      description: record.description || "",
      from: record.from,
      to: record.to,
      leave_type_id: record.leave_type_id,
      id: record.id,
      status, // set the new status here
    };

    const formData = new FormData();
    Object.entries(formattedValues).forEach(([key, value]) => {
      formData.append(key, value);
    });

    // If you want to send the document again (optional, if needed)
    if (record.documents && typeof record.documents !== "string") {
      formData.append("documents", record.documents);
    }

    await dispatch(createEmployeeLeave(formData));
  };
  const columns = [
    // Only show Employee column if not in history view
    ...(selectedTab !== "history"
      ? [
          {
            title: setLocale("Employee"),
            key: "user",
            ...getColumnSearchProps("email"),
            render: (_, record) => {
              return (
                <div dangerouslySetInnerHTML={{ __html: record.user?.email }} />
              );
            },
            sorter: true,
          },
        ]
      : []),
    {
      title: setLocale("Leave Type"),
      key: "leave_type",
      render: (_, record) => {
        return (
          <div dangerouslySetInnerHTML={{ __html: record.leave_type?.name }} />
        );
      },
      sorter: true,
    },

    {
      title: setLocale("From"),
      dataIndex: "from",
      key: "from",
      render: (from) => <span>{from}</span>,
      sorter: true,
    },
    {
      title: setLocale("To"),
      dataIndex: "to",
      key: "to",
      render: (to) => <span>{to}</span>,
      sorter: true,
    },
    {
      title: setLocale("status"),
      dataIndex: "status",
      key: "status",
      render: (status) => (
        <span className="text-capitalize font-weight-semibold">{status}</span>
      ),
    },
    {
      title: setLocale("operation"),
      key: "operation",
      render: (data, record) => {
        let menuItems = [];
        // if (permission.includes("Approve Reject Leaves")) {
        if (record.status === "pending") {
          if (selectedTab === "requests") {
            menuItems = [
              { key: "approved", label: "Approve Leave" },
              { key: "rejected", label: "Reject Leave" },
            ];
          } else {
            menuItems = [{ key: "cancelled", label: "Cancel Leave" }];
          }
        } else if (record.status === "approved") {
          menuItems = [{ key: "cancelled", label: "Cancel Leave" }];
        }else if( record.status === "cancel pending") {
          menuItems = [{ key: "approved", label: "Approve Leave" }];
        }

        return (
          <>
            {permission.includes("Delete") && (
              <Popconfirm
                title={setLocale("sure_to_delete")}
                onConfirm={(e) => handleDelete(record)}
              >
                <DeleteOutlined
                  style={{ fontSize: "15px" }}
                  className="text-danger"
                />{" "}
                &nbsp;
              </Popconfirm>
            )}
            <Tooltip title="Download Excel">
              <DownloadOutlined
                style={{
                  fontSize: "15px",
                  // marginRight: "9px",
                  cursor: EmployeeLeaveExportLoading
                    ? "not-allowed"
                    : "pointer",
                  color: EmployeeLeaveExportLoading ? "#ccc" : "#1890ff",
                  opacity: EmployeeLeaveExportLoading ? 0.5 : 1,
                }}
                onClick={
                  EmployeeLeaveExportLoading
                    ? undefined
                    : () => handleDownloadExcel(record)
                }
              />
            </Tooltip>
            {permission.includes("Update") && (
              <>
                <Button
                  type="text"
                  style={{ border: "0px" }}
                  icon={
                    <EditOutlined
                      style={{ fontSize: "15px" }}
                      className="text-success"
                    />
                  }
                  onClick={(e) => handleUpdate(record)}
                  disabled={record.status !== "pending" } // Disable if not pending
                />
                <Dropdown
                  overlay={
                    <Menu
                      onClick={(e) => handleMenuClick(record, e)}
                      items={menuItems}
                    />
                  }
                  trigger={["click"]}
                  disabled={menuItems.length === 0}
                >
                  <SettingOutlined
                    className="text-danger"
                    style={{
                      cursor:
                        menuItems.length === 0 ? "not-allowed" : "pointer",
                      opacity: menuItems.length === 0 ? 0.5 : 1,
                    }}
                  />
                </Dropdown>
              </>
            )}
            <Tooltip title="View Details">
              <Button
                type="text"
                style={{ border: "0px" }}
                icon={
                  <EyeOutlined
                    style={{ fontSize: "15px" }}
                    className="text-success"
                  />
                }
                onClick={(e) => handleViewRecord(record)}
              />
            </Tooltip>
          </>
        );
      },
    },
  ];
  const switchToGridView = () => {
    setView("grid");
  };

  const switchToTableView = () => {
    setView("table");
    setSelectedTab("history");
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      filter,
      sorting,
      "history"
    );
  };
  const items = [
    {
      key: "history",
      label: "Leave History",
    },
    ...(permission.includes("Employee Leave Requests")
      ? [
          {
            key: "requests",
            label: "Leave Requests",
          },
        ]
      : []),
  ];

  const [filters, setFilters] = useState({
    leave_type_id: undefined,
    dateRange: undefined,
    status: undefined,
    searchText: "",
  });
  const handleFilterChange = async (newFilters) => {
    // Ensure leave_type_id is always an array
    if (newFilters.leave_type_id && !Array.isArray(newFilters.leave_type_id)) {
      newFilters.leave_type_id = [newFilters.leave_type_id];
    }

    // Update the Redux filter state
    await dispatch(setColumnSearch(newFilters));

    // Update local filters state
    setFilters(newFilters);

    getModuleData(
      1,
      tablePagination.pageSize,
      newFilters,
      sorting,
      selectedTab
    );
  };
  const addButton = permission.includes("Create") && (
    <Button
      className="ant-btn-round ant-btn-sm"
      type="primary"
      style={view === "grid" ? { marginLeft: 16 } : undefined}
      onClick={handleOpenModal}
    >
      {setLocale("employeeleave.add")}
    </Button>
  );
  const onChange = (key) => {
    setSelectedTab(key);
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      filter,
      sorting,
      key
    );
  };
  const handleDownloadExcel = async (record, filtersData = null) => {
    try {
      dispatch(clearExportError());

      let exportData;
      
      if (filtersData) {
        // For Generate Report - use filters data
        exportData = {
          filter: filtersData,
        };
      } else {
        // For individual record download
        exportData = {
          filter: {
            employee_leave_ids: [record.id],
          },
        };
      }

      const response = await dispatch(exportEmployeeLeaveExcel(exportData));
      if (response.payload) {
        let fileUrl = response.payload;

        if (typeof response.payload === "object" && response.payload.file_url) {
          fileUrl = response.payload.file_url;
        } else if (
          typeof response.payload === "object" &&
          response.payload.data
        ) {
          fileUrl = response.payload.data;
        }

        if (typeof fileUrl === "string") {
          fileUrl = fileUrl.replace(/\\\//g, "/");

          const link = document.createElement("a");
          link.href = fileUrl;
          link.download = filtersData 
            ? `employee-leave-report.xlsx` 
            : `employee-leave-${record.id}.xlsx`;
          link.target = "_blank";

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          console.log("Excel file download initiated successfully");
        }
      }
    } catch (error) {
      console.error("Download error:", error);
    }
  };

  const handleViewRecord = (record) => {
    setViewRecord(record);
    setIsViewModalVisible(true);
  };

  const handleViewModalClose = () => {
    setIsViewModalVisible(false);
    setViewRecord(null);
  };

  return (
    <>
      <Breadcrumb className="my-2 mx-2">
        <Breadcrumb.Item>
          <Link to="/app/default">{setLocale("home")}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{setLocale("Employee Leave")}</Breadcrumb.Item>
      </Breadcrumb>

      {/* Use the new ReasonModal component */}
      <ReasonModal
        visible={isReasonModalVisible}
        onCancel={handleReasonModalCancel}
        onSubmit={handleReasonModalSubmit}
        currentAction={currentAction}
        form={reasonForm}
      />

      {/* Use the new ViewModal component */}
      <ViewModal
        visible={isViewModalVisible}
        onClose={handleViewModalClose}
        viewRecord={viewRecord}
      />

      <div
        className="d-flex justify-content-end mb-3 mr-3"
        style={{ marginTop: "-30px" }}
      >
        <Tooltip title="Grid View">
          <Button
            icon={<AppstoreOutlined />}
            onClick={switchToGridView}
            style={{ marginRight: "8px" }}
            type={view === "grid" ? "primary" : "default"}
          />
        </Tooltip>
        <Tooltip title="Table View">
          <Button
            icon={<TableOutlined />}
            onClick={switchToTableView}
            type={view === "table" ? "primary" : "default"}
          />
        </Tooltip>
      </div>

      {view === "table" && (
        <>
          <Row gutter={16} className="mt-3">
            <Col xs={24} sm={24} md={24} lg={24}>
              <div className="d-flex justify-content-between align-items-center">
                <Tabs
                  defaultActiveKey="history"
                  items={items}
                  onChange={onChange}
                />
                {permission.includes("Employee Leave Requests") &&
                  selectedTab === "requests" && (
                    <Button onClick={() => handleDownloadExcel(null, filter)}>
                      Generate Report
                    </Button>
                  )}
              </div>
            </Col>
          </Row>
        </>
      )}

      <>
        <div className="code-box">
          <section className="code-box-demo mt-2 pb-4">
            <div
              className="d-flex align-items-center"
              style={{
                justifyContent: view === "grid" ? "flex-end" : "space-between",
              }}
            >
              {view === "grid" ? (
                addButton
              ) : (
                <>
                  <LeaveFilters
                    onFilterChange={handleFilterChange}
                    leaveTypeValue={selectedLeaveType}
                    onLeaveTypeChange={handleLeaveTypeChange}
                    selectedTab={selectedTab}
                  />
                  {addButton}
                </>
              )}
            </div>
          </section>

          {EmployeeLeaveAddDrawer && <AddEmployeeLeaveModal />}
          <section className="code-box-description">
            {view === "grid" ? (
              <Dashboard setView={setView} />
            ) : (
              <>
                <Table
                  onChange={handleTableChange}
                  columns={columns}
                  loading={EmployeeLeaveTableLoading}
                  rowKey={(record) => record.id}
                  dataSource={EmployeeLeaveResult.data ?? []}
                  pagination={false}
                />
                <Pagination
                  style={{ margin: "16px", float: "right" }}
                  current={tablePagination.current}
                  pageSize={tablePagination.pageSize}
                  total={tablePagination.total}
                  showTotal={(total, range) =>
                    `${range[0]}-${range[1]} of ${total} Records`
                  }
                  pageSizeOptions={["10", "20", "50", "100", "1000"]}
                  showQuickJumper
                  onChange={handlePageChange}
                />
              </>
            )}
          </section>
        </div>
      </>
    </>
  );
}

export default Index;

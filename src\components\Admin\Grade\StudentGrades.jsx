import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { Statistic, message, Form, DatePicker, Select, Tooltip, Dropdown, Menu, Space, Button, Col, Row, Avatar, List, Input, Typography, Skeleton, Modal, Table, Tag } from 'antd';
import { ExclamationCircleFilled, CloseCircleTwoTone, CheckCircleTwoTone, EllipsisOutlined, UserOutlined, PlusOutlined, BookOutlined, CalendarFilled, BookFilled, EyeFilled, SortAscendingOutlined, DownloadOutlined } from '@ant-design/icons';
// import { finalizedCourseGradeCategory,addStudentObtainedMarks,addCourseGradeBookCategory,getClassStudentsForGrades } from 'store/slices/gradeSystem/studentGradeBookSlice';
// import { getAllGradeBookCategory } from 'store/slices/gradeSystem/gradeSystemSlice';

import {
  finalizedCourseGradeCategory,
  addStudentObtainedMarks,
  addCourseGradeBookCategory,
  getClassStudentsForGrades
} from "store/slices/Grade/studentGradeBookSlice.js";
import { GRADE_BOOK_CATEGORY, STUDENT_GRADE_BOOK_SLICE, MANAGE_CLASS } from "constants/AppConstants";
import { getGradeBookCategory } from "store/slices/GradeBookCategory/manageGradeBookCategorySlice.js";

import IntlMessage from 'components/util-components/IntlMessage';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;


function StudentGrades(props) {
  const { GradeBookCategoryResult } = useSelector((state) => state[GRADE_BOOK_CATEGORY])
  const { classStudentsForGrades, activeStudents } = useSelector((state) => state[STUDENT_GRADE_BOOK_SLICE])
  const { ClassCoursesData, ClassCourseEditData } = useSelector((state) => state[MANAGE_CLASS]);
  console.log(ClassCoursesData, ClassCourseEditData)

  const iconColor = { color: '#BC404F' };
  const [form] = Form.useForm();

  const { data } = props;
  const [courseId, setCourseId] = useState(ClassCourseEditData?.course_id);

  const dispatch = useDispatch();
  const [loadStudents, setLoadStudents] = useState(true);
  const [studenMarkPercentage, setStudenMarkPercentage] = useState({});
  const [loading, setLoading] = useState(false);
  const [addNewGradeCategory, setAddNewGradeCategory] = useState(false);


  const [messageApi, contextHolder] = message.useMessage();
  const success = (msg) => {
    messageApi.open({
      type: 'success',
      content: msg,
      style: {
        marginTop: '90vh',
      },
    })
  }
  const error = (msg) => {
    messageApi.open({
      type: 'error',
      content: msg,
      style: {
        marginTop: '90vh',
      },
    })
  }


  useEffect(() => {
    /**get All GradeBook Categories  */
    dispatch(getGradeBookCategory())
    /**get All CourseGradeBookCategories with student Marks */
    dispatch(getClassStudentsForGrades(
      { class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }))
      .then((response) => {
        setLoadStudents(false);
      });

  }, []);

  /**change Student Obtained Marks */
  const handleStudentMarkChange = (e, student, catStdId) => {
    setStudenMarkPercentage((prevInputValues) => {
      return {
        ...prevInputValues,
        [catStdId]: e.target.value,
      };
    });
  };

  /**when teacher add Student mark and move to next It will update that student marks in DB */
  const handleStudentMarkBlur = (obtaibMarks, classStudentId, category) => {
    const { total_marks, id } = category;
    let courseGradeBookCategoryId = id;
    let totalMarks = total_marks;
    if (obtaibMarks > totalMarks) {
      error('Obtain marks should be less or equal to total marks')
      return false
    }
    if (obtaibMarks) {
      dispatch(addStudentObtainedMarks({ totalMarks, obtaibMarks, classStudentId, courseGradeBookCategoryId }))
    }
  }

  /**open CourseGradeCategory Modal to add new Category */
  const openCategoryGradeModal = () => {
    form.resetFields();
    setAddNewGradeCategory(true);
  }
  const saveCourseGradeCategory = (values) => {
    /**add some variables into request for some processing */
    values.class_id = ClassCourseEditData?.manage_class_id;
    values.course_id = ClassCourseEditData?.course_id;
    /**Date formate before saving */
    values.due_date = moment(values.due_date).format('YYYY-MM-DD')

    setLoading(true);
    dispatch(addCourseGradeBookCategory(values)).then(() => {
      setLoading(false);
      setAddNewGradeCategory(false);
      /** get Updated date for GradeBook Category */
      dispatch(getClassStudentsForGrades({ class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }))

    })
  }
  /**on cancel CourseGrade Modal */
  const handleCancel = () => {
    setAddNewGradeCategory(false);
    setLoading(false);
  }
  /**when teacher make a category Finalized after He is unable to change any one marks */
  const finalizedHandler = (category) => {
    confirm({
      title: 'Are you sure? you want to finalize it!',
      icon: <ExclamationCircleFilled />,
      zIndex: 1011,
      style: {
        top: '50%',
        transform: 'translateY(-50%)',
      },
      onOk() {
        const { id } = category;
        let status = 'Finalized';
        dispatch(finalizedCourseGradeCategory({ id, status })).then(() => {
          /** get Updated date for GradeBook Category */
          dispatch(getClassStudentsForGrades({ class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }))

        })
      },
      onCancel() {

      },
    });
  }

  /** handle course change get new Course categories */
  const handleCourseChange = (value) => {
    setCourseId(value);
    setLoading(true);

    /**get All CourseGradeBookCategories with student Marks */
    dispatch(getClassStudentsForGrades({ class_id: ClassCourseEditData?.manage_class_id, course_id: value }))
      .then((response) => {
        setLoading(false);
      });
  };

  const courseOptions = ClassCoursesData.map(item => ({
    value: item.course_id,
    label: item.course
  }))

  const items = [
    {
      label: 'Finalized',
      key: 1,
    },
  ];
  const menuItems = items.map(item => (
    <Menu.Item key={item.key}>{item.label}</Menu.Item>
  ));

  return (
    <>
      {contextHolder}
      {loadStudents ? <><Skeleton active /><Skeleton active /></> :
        <>
          <Row gutter={16} >
            <Col xs={24} sm={24} md={12} lg={6} xl={6}>
              <div>
                <Button size='small' onClick={() => { openCategoryGradeModal() }} >
                  <PlusOutlined /> {setLocale("course_gradebook_category.add_column")}
                </Button>
              </div>
            </Col>

            <Col xs={24} sm={24} md={12} lg={6} xl={6}>
              <div>
                <Select
                  size='small'
                  showSearch
                  allowClear
                  defaultValue={ClassCourseEditData?.course_id}
                  style={{ display: 'block' }}
                  onChange={handleCourseChange}
                  options={courseOptions}
                />
              </div>
            </Col>
          </Row>

          <Row gutter={16} className='my-3'>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>

              {loading ?
                <Skeleton loading={loading} active></Skeleton> :
                <List
                  size="small"
                  header={<div style={{ height: '50px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>Students</div>}
                  bordered
                  itemLayout="horizontal"
                  dataSource={activeStudents}
                  renderItem={(student, index) => (
                    <List.Item style={{ height: '50px' }}>
                      <List.Item.Meta
                        avatar={
                          student.profile_picture ? <Avatar src={`${student.profile_picture}`} /> :
                            <Avatar icon={<UserOutlined />} />
                        }
                        title={<a
                          style={{ marginTop: '12px', display: 'flex', alignItems: 'left' }}
                          href="/">
                          {student.full_name}
                        </a>}
                      />
                    </List.Item>
                  )}
                />
              }
            </Col>
            {/* grade assignment category */}
            {classStudentsForGrades.map((category, catIndex) => (
              <Col xs={24} sm={24} md={12} lg={6} xl={6} key={category.id}>
                <List
                  size="small"
                  header={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '50px' }}>
                      <div>
                        {category.name}
                        <Typography>{category.due_date}</Typography>
                        Total: <Text type="success">{category.total_marks}</Text>
                      </div>
                      {category.status == 'Pending' ?
                        <>
                          <Tooltip title={`${category.status} / Not finalized yet / You can change`} color={`red`} key={'red'}>
                            <CloseCircleTwoTone twoToneColor="#eb2f96" />
                          </Tooltip>
                          <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                            <Dropdown overlay={<Menu onClick={() => finalizedHandler(category)}>{menuItems}</Menu>} placement='bottomRight'>
                              <Space style={{ cursor: 'pointer' }}>
                                <EllipsisOutlined style={{ fontSize: '25px' }} />
                              </Space>
                            </Dropdown>
                          </div>
                        </>
                        :
                        <>
                          <Tooltip title={`${category.status} / You can't change / It's Finalized`} color={`green`} key={'green'}>
                            <CheckCircleTwoTone twoToneColor="#52c41a" />
                          </Tooltip>
                        </>
                      }
                    </div>
                  }
                  bordered
                  itemLayout="horizontal"
                  dataSource={category.students}
                  renderItem={(student, index) => (
                    <List.Item style={{ height: '50px' }}>
                      {category.status == 'Pending' ?
                        <>
                          <Input
                            name="marks_obtained"
                            value={studenMarkPercentage[category.id + '_' + student.id] ?? student.marks_obtained}
                            onChange={(e) => handleStudentMarkChange(e, student, category.id + '_' + student.id)}
                            onBlur={(e) => handleStudentMarkBlur(e.target.value, student.class_student_id, category)}
                          />
                        </>
                        :
                        <>
                          <Space size={80}>
                            <Statistic title="Marks" value={student.marks_obtained} />
                            <Statistic title="Grade" value={student.grade} />
                          </Space>
                        </>
                      }
                    </List.Item>
                  )}
                />
              </Col>
            ))}
          </Row>

          {/* Modal For new column */}
          {addNewGradeCategory ?
            <Modal zIndex={1010} title={'Course Grade Book Category'}
              centered
              open={addNewGradeCategory}
              // okText={'Save'}
              // confirmLoading={loading}
              onCancel={() => handleCancel()}
              // onOk={onFinish} 
              footer={null}
            >
              <Space direction="vertical" style={{ width: '100%' }} >
                <Form onFinish={saveCourseGradeCategory} layout="vertical" form={form} autoComplete="off">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="name"
                        label={setLocale("name")}
                        rules={[
                          {
                            required: true,
                            message: setLocale('name_error'),
                          },
                        ]}>
                        <Input className='rounded-0' />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="due_date"
                        label={setLocale("course_gradebook_category.due_date")}
                        rules={[
                          {
                            required: true,
                            message: setLocale('course_gradebook_category.due_date_error'),
                          },
                        ]}>
                        <DatePicker style={{ display: 'block' }} format={'YYYY-MM-DD'} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="grade_book_category_id"
                        label={setLocale("course_gradebook_category.grade_book_category")}
                        rules={[
                          {
                            required: true,
                            message: setLocale('course_gradebook_category.grade_book_category_error'),
                          },
                        ]}>
                        <Select className='rounded-0' optionLabelProp="label">
                          {GradeBookCategoryResult.map((gradeBC, index) =>
                            <Option value={gradeBC.id} key={index} label={gradeBC.name}>{gradeBC.name}</Option>
                          )}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="final_grade_weightage"
                        label={setLocale("course_gradebook_category.final_grade_weightage")}
                        rules={[
                          {
                            required: true,
                            message: setLocale('course_gradebook_category.final_grade_weightage_error'),
                          },
                        ]}>
                        <Input className='rounded-0' />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="total_marks"
                        label={setLocale("course_gradebook_category.total_marks")}
                        rules={[
                          {
                            required: true,
                            message: setLocale('course_gradebook_category.total_marks_error'),
                          },
                        ]}>
                        <Input className='rounded-0' />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={8}>
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                      <Form.Item >
                        <Button loading={loading} type="primary" htmlType="submit" className='rounded-0'>
                          {setLocale("save")}
                        </Button>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </Space>
            </Modal> : null
          }

        </>
      }
    </>
  )
}

export default StudentGrades

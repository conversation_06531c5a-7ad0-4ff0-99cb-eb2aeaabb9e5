import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  Popconfirm,
  Pagination,
  Button,
  Input,
  Space,
  Breadcrumb,
  Skeleton,
  Col,
  Badge,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { MANAGE_CLASS, STRANDS } from "constants/AppConstants";
import {
  getErs,
  updateStrandsSortFilters,
  ManageErsEditData,
  deleteErs,
  setClassCourseStrandsColumnSearch,
  updateCourseStrandsSort,
  deleteClassCourseStrands,
  setClassCourseStrands,
} from "store/slices/ManageClass/manageManageClassSlice";
import { DndContext } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const Row = ({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props["data-row-key"],
  });

  const style = {
    ...props.style,
    transform: CSS.Transform.toString(
      transform && {
        ...transform,
        scaleY: 1,
      }
    ),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 9999,
        }
      : {}),
  };

  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if (child.key === "sort") {
          return React.cloneElement(child, {
            children: (
              <MenuOutlined
                ref={setActivatorNodeRef}
                style={{
                  touchAction: "none",
                  cursor: "move",
                }}
                {...listeners}
              />
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

function ViewErsModal() {
  const dispatch = useDispatch();
  const searchInput = useRef(null);
  const {
    Ers,
    ManageClassEditData,
    // ClassCourseEditData,
    ErsTablePagination,
    StrandsSorting,
    strandsFilters,
    ErsTableLoading,
    ErsButtonSpinner,
    ErsEditData,
    permission,
  } = useSelector((state) => state[MANAGE_CLASS]);

  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getErs({
        class_id: ManageClassEditData?.id,
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
      })
    );
  };

  const handleSearch = async (confirm) => {
    // confirm();
    // try {
    //   await getModuleData(
    //     1,
    //     strandsTablePagination.pageSize,
    //     strandsFilters,
    //     StrandsSorting
    //   );
    // } catch (error) {
    //   console.error("Error in search:", error);
    // }
  };

  const handleReset = async (dataIndex) => {
    // const { [dataIndex]: removedProperty, ...newObject } = strandsFilters;
    // await dispatch(setClassCourseStrandsColumnSearch(newObject));
    // try {
    //   await getModuleData(
    //     strandsTablePagination.current,
    //     strandsTablePagination.pageSize,
    //     newObject,
    //     StrandsSorting
    //   );
    // } catch (error) {
    //   console.error("Error resetting filters:", error);
    // }
  };

  const handleOnChange = async (dataIndex, value, confirm) => {
    // await dispatch(
    //   setClassCourseStrandsColumnSearch({
    //     ...strandsFilters,
    //     [dataIndex]: value,
    //   })
    // );
    // if (value === "") {
    //   confirm();
    //   getModuleData(
    //     strandsTablePagination.current,
    //     strandsTablePagination.pageSize,
    //     { ...strandsFilters, [dataIndex]: value },
    //     StrandsSorting
    //   );
    // }
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={
            strandsFilters[dataIndex]
              ? strandsFilters[dataIndex]
              : selectedKeys[0]
          }
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            strandsFilters[dataIndex] && strandsFilters[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      strandsFilters[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[strandsFilters[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  useEffect(() => {
    getModuleData(
      ErsTablePagination.current,
      ErsTablePagination.pageSize,
      strandsFilters,
      StrandsSorting
    );
  }, [ErsButtonSpinner]);

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, strandsFilters, StrandsSorting);
  };

  const handleDelete = (record) => {
    dispatch(deleteErs(record.id)).then(() => {
      getModuleData(
        1,
        ErsTablePagination.pageSize,
        strandsFilters,
        StrandsSorting
      );
    });
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
    };

    try {
      await dispatch(
        updateStrandsSortFilters({ filter: strandsFilters, sorting: sorting })
      );
      getModuleData(1, ErsTablePagination.pageSize, strandsFilters, sorting);
    } catch (error) {
      console.log(error);
    }
  };

  const onDragEnd = ({ active, over }) => {
    // if (active.id !== over?.id) {
    //   dispatch(updateCourseStrandsSort({ from: active.id, to: over?.id })).then(
    //     () => {
    //       getModuleData(
    //         ErsTablePagination.current,
    //         ErsTablePagination.pageSize,
    //         strandsFilters,
    //         StrandsSorting
    //       );
    //     }
    //   );
    // }
  };

  const handleUpdate = (record) => {
    dispatch(ManageErsEditData(record));
  };

  const columns = [
    {
      title: setLocale("#"),
      key: "index",
      render: (text, record, rowIndex) => rowIndex + 1,
    },
    {
      title: setLocale("Name"),
      dataIndex: "name",
      key: "name",
      //   sorter: true,
      //   ...getColumnSearchProps("name"),
    },
    {
      title: setLocale("Ers Completed"),
      dataIndex: "ers_completed",
      key: "ers_completed",
      sorter: true,
      render: (data, record) => (
        <>
          <Badge
            className="site-badge-count-109"
            count={record.ers_completed ? "YES" : "NO"}
            style={{
              backgroundColor: record.ers_completed ? "#52c41a" : "#f5222d",
            }}
          />
        </>
      ),
    },
    {
      title: setLocale("Completion Date"),
      dataIndex: "completion_date",
      key: "completion_date",
      sorter: true,
      // ...getColumnSearchProps("completion_date"),
    },
    {
      title: setLocale("Benchmark Met"),
      dataIndex: "benchmark_met",
      key: "benchmark_met",
      sorter: true,
      render: (data, record) => (
        <>
          <Badge
            className="site-badge-count-109"
            count={record.benchmark_met ? "YES" : "NO"}
            style={{
              backgroundColor: record.benchmark_met ? "#52c41a" : "#f5222d",
            }}
          />
        </>
      ),
    },
    {
      title: setLocale("operation"),
      key: "action",
      render: (data, record) => (
        <>
          <EditOutlined
            className="text-success"
            onClick={(e) => handleUpdate(record)}
          />
          <Popconfirm
            title={setLocale("sure_to_delete")}
            onConfirm={(e) => handleDelete(record)}
            className="ml-1"
          >
            <DeleteOutlined
              style={{ fontSize: "15px" }}
              className="text-danger"
            />{" "}
            &nbsp;
          </Popconfirm>
        </>
      ),
    },
  ];

  return (
    <>
      <div>
        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            // rowKey array
            items={Ers?.data?.map((i) => i.key) ?? []}
            strategy={verticalListSortingStrategy}
          >
            <Table
              onChange={handleTableChange}
              columns={columns}
              components={{
                body: {
                  row: Row,
                },
              }}
              loading={ErsTableLoading}
              rowKey={(record) => record.id}
              dataSource={Ers?.data ?? []}
              pagination={false}
            />
            <Pagination
              style={{ margin: "16px", float: "right" }}
              current={ErsTablePagination.current}
              pageSize={ErsTablePagination.pageSize}
              total={ErsTablePagination.total}
              showTotal={(total, range) =>
                `${range[0]}-${range[1]} of ${total} Records`
              }
              pageSizeOptions={["10", "20", "50", "100", "1000"]}
              showQuickJumper
              onChange={handlePageChange}
            />
          </SortableContext>
        </DndContext>
      </div>
    </>
  );
}

export default ViewErsModal;

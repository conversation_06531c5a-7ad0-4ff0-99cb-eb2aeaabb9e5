import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Col, DatePicker, Divider, Form, Input, Row, Select, Skeleton, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { INCIDENT_REPORT, INCIDENT_TYPE, LOCATIONS, SCHOOL_YEAR } from "constants/AppConstants";
import { useNavigate } from 'react-router-dom';
import { PlusOutlined } from '@ant-design/icons';
import {
  IncidentReportAddDrawerStatus,
  createIncidentReport,
  getIncidentReport
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import {
  createLocations,
  getLocations
} from "store/slices/Locations/manageLocationsSlice.js";
import { getIncidentType } from 'store/slices/IncidentType/manageIncidentTypeSlice';
import IntlMessage from "components/util-components/IntlMessage";
import moment from "moment";
import 'moment-timezone';
import { DATE_FORMAT_YYYY_MM_DD } from 'constants/DateConstant';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const IncidentForm = ({ submitForm }) => {
  const [form] = Form.useForm();
  const [selectedDate, setSelectedDate] = useState(moment()); // Set initial selected date to current date
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(true);
  const inputRef = useRef(null);
  const onNameChange = (event) => {
    setName(event.target.value);
  };
  const {
    IncidentReportButtonAndModelLabel,
    IncidentReportErrors,
    IncidentReportShowMessage,
    IncidentReportButtonSpinner,
    IncidentReportEditData,
    tablePagination
  } = useSelector(
    (state) => state[INCIDENT_REPORT]
  );

  const { LocationsResult } = useSelector((state) => state[LOCATIONS]);
  const { IncidentTypeResult } = useSelector((state) => state[INCIDENT_TYPE]);
  const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);


  const fetchData = async () => {
    await dispatch(getLocations({ perPage: 1000 }));
    await dispatch(getIncidentType({ perPage: 1000 })).then(() => {
      setLoading(false);
    })
  };

  useEffect(() => {
    fetchData();
  }, [])

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const timeFrame = [
    {
      label: 'Before School hours',
      value: 'Before School hours',
    },
    {
      label: 'After School hours',
      value: 'After School hours',
    },
    {
      label: 'During School hours',
      value: 'During School hours',
    }
  ];

  const handleFormSubmit = async (formValues) => {
    if (IncidentReportEditData && IncidentReportEditData.id) {
      // If editing, include the id in the form values
      formValues.id = IncidentReportEditData.id;
    }

    await dispatch(createIncidentReport(formValues))
      .then((result) => {
        if (Object.keys(IncidentReportErrors).length == 0) {
          submitForm(formValues);
          const { enc_id } = result.payload.data;
          // Navigate to the edit incident report page
          navigate(`/app/edit-incident-report/${enc_id}`)
        }
      })
      .catch((error) => {
        // Handle delete error
        console.error("Error deleting module:", error);
      });
  };

  const disabledDate = (current) => {
    // Get the current date
    const currentDate = moment();

    const validSchoolYears = SchoolYearResult?.data?.filter(item => {
      const start = moment(item.start_date);
      const end = moment(item.end_date);
      return currentDate >= start && currentDate <= end;
    });

    // Check if current date is within the valid school years
    return !validSchoolYears.some(item => {
      const start = moment(item.start_date);
      // const end = moment(item.end_date);
      const end = currentDate;
      return current >= start && current <= end;
    });
  };

  const addLocation = (e) => {
    dispatch(createLocations({ 'name': name })).then((result) => {
      dispatch(getLocations({ perPage: 1000 }));
    })
    e.preventDefault();
    setName('');
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  }

  return (
    <>

      <Row gutter={16}>
        <Col span={24} style={{ textAlign: "center" }}>
          <h2>
            Where and when did the incident take place?
          </h2>
        </Col>
        <Col span={24}>
          <Skeleton
            loading={loading}
            active
          >
            <Form
              layout="vertical"
              form={form}
              onFinish={handleFormSubmit}
              onFinishFailed={onFinishFailed}
              autoComplete="off"
              initialValues={{
                ...IncidentReportEditData,
                title: IncidentReportEditData && IncidentReportEditData?.title ? IncidentReportEditData?.title : '',
                location_id: IncidentReportEditData && IncidentReportEditData?.location_id ? IncidentReportEditData?.location_id : '',
                prepared_by: IncidentReportEditData && IncidentReportEditData?.prepared_by ? IncidentReportEditData?.prepared_by : '',
                reported_by: IncidentReportEditData && IncidentReportEditData?.reported_by ? IncidentReportEditData?.reported_by : '',
                description: IncidentReportEditData && IncidentReportEditData?.description ? IncidentReportEditData?.description : '',
                time_frame: IncidentReportEditData && IncidentReportEditData?.time_frame ? IncidentReportEditData?.time_frame : '',
                incident_date: IncidentReportEditData && IncidentReportEditData?.incident_date ? moment(IncidentReportEditData?.incident_date) : moment(selectedDate),
                incident_type_id: IncidentReportEditData && IncidentReportEditData?.incident_types ? IncidentReportEditData?.incident_types.map(incidentType => incidentType.id) : []
              }}
            >
              <Row gutter={16}>

                <Col span={8}>
                  <Form.Item
                    name="title"
                    label={setLocale('incidentReport.title')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('nameError'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.title ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.title}
                  >
                    <Input />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item
                    name="incident_type_id"
                    label={setLocale('incidentReport.incident_type_id')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('incidentReport.incident_type_id_Error'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.incident_type_id ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.incident_type_id}
                  >
                    <Select
                      className="rounded-0"
                      showSearch
                      allowClear
                      mode='multiple'
                      optionLabelProp="label"
                      optionFilterProp="children"
                      filterOption={(input, option) => (
                        (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                      )}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                      }
                      options={IncidentTypeResult.data ?? []}

                    />
                  </Form.Item>
                </Col>


                <Col span={8}>
                  <Form.Item
                    name="incident_date"
                    label={setLocale("incidentReport.incident_date")}
                    rules={[
                      {
                        required: true,
                        message: setLocale("incidentReport.incident_dateError"),
                      },
                    ]}
                    // initialValue={selectedDate}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.incident_date ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.incident_date}
                  >
                    <DatePicker
                      // value={selectedDate}
                      // onChange={handleDateChange}
                      disabledDate={disabledDate}
                      format={DATE_FORMAT_YYYY_MM_DD}
                      className="rounded-0 w-100"
                    />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    name="time_frame"
                    label={setLocale('incidentReport.time_frame')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('incidentReport.time_frameError'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.time_frame ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.time_frame}
                  >
                    <Select
                      className="rounded-0"
                      showSearch
                      optionLabelProp="label"
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) => (
                        (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                      )}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                      }
                      options={timeFrame ?? []}

                    />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    name="prepared_by"
                    label={setLocale('incidentReport.prepared_by')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('incidentReport.prepared_byError'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.prepared_by ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.prepared_by}
                  >
                    <Input />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    name="reported_by"
                    label={setLocale('incidentReport.reported_by')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('incidentReport.reported_byError'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.reported_by ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.reported_by}
                  >
                    <Input />
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    name="location_id"
                    label={setLocale('incidentReport.location_id')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('incidentReport.location_idError'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.location_id ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.location_id}
                  >
                    <Select
                      className="rounded-0"
                      showSearch
                      optionLabelProp="label"
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) => (
                        (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                      )}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                      }
                      dropdownRender={(menu) => (
                        <>
                          {menu}
                          <Divider
                            style={{
                              margin: '8px 0',
                            }}
                          />
                          <Space
                            style={{
                              padding: '0 8px 4px',
                            }}
                          >
                            <Input
                              placeholder="Please enter item"
                              ref={inputRef}
                              value={name}
                              onChange={onNameChange}
                              onKeyDown={(e) => e.stopPropagation()}
                            />
                            <Button type="text" icon={<PlusOutlined />} onClick={addLocation}>
                              Add Location
                            </Button>
                          </Space>
                        </>
                      )}
                      options={LocationsResult?.data ?? []}
                    />
                  </Form.Item>
                </Col>

              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="description"
                    label={setLocale('description')}
                    rules={[
                      {
                        required: false,
                        message: setLocale('description_error'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.description ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.description}
                  >
                    <Input.TextArea rows={4} className="rounded-0" />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="location_description"
                    label={setLocale('incidentReport.location_description')}
                    rules={[
                      {
                        required: false,
                        message: setLocale('location_description_error'),
                      },
                    ]}
                    validateStatus={IncidentReportShowMessage && IncidentReportErrors.location_description ? "error" : ""}
                    help={IncidentReportShowMessage && IncidentReportErrors.location_description}
                  >
                    <Input.TextArea rows={4} className="rounded-0" />
                  </Form.Item>
                </Col>

              </Row>

              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={IncidentReportButtonSpinner}
                >
                  {IncidentReportButtonAndModelLabel}
                </Button>
              </Space>
            </Form>
          </Skeleton>
        </Col>
      </Row>
    </>
  );
};
export default IncidentForm;


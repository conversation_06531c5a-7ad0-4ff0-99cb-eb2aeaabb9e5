import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { message, Space, Row, Col, Button, Form, Input } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
    addMoreRubricsIntoAssignment,
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import IntlMessage from "components/util-components/IntlMessage"

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AddRubricOnly() {
    const [messageApi, contextHolder] = message.useMessage();
    const openNotificationWithIcon = (msg, type='error') => {
        messageApi.open({
            type,
            content: msg,
            style: { marginTop: '90vh',  },
        });
    }

    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const { singleAssignment, saveStudentOnlyBtn, showMessage, errors, loading, openModal, moduleLabel } = useSelector((state) => state[ASSIGNMENT_SLICE]);    
    useEffect(() => {
            
    }, [])

    const onFinish = async (values) => {        
        if (!values.assignment_rubrics || values.assignment_rubrics.length === 0) {
            openNotificationWithIcon(`Please add at least one rubric!`);
            return false
        }
        if (singleAssignment) { 
            values.assignment_rubrics = JSON.stringify(values.assignment_rubrics);
            values.id = singleAssignment?.id; 
            await dispatch(addMoreRubricsIntoAssignment(values))
            form.resetFields();
        }               
    }
    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };

    return (
        <>
        {contextHolder}
        <Form layout="vertical" onFinish={onFinish} onFinishFailed={onFinishFailed} form={form} encType="multipart/form-data" >                 
            <Row gutter={16}>                               
                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                    <Form.List name="assignment_rubrics">
                        {(fields, { add, remove }) => (
                            <>
                                {fields.map(({ key, name, ...restField }) => (
                                    <Space key={key}
                                        style={{ display: 'flex', marginBottom: 8 }} align="baseline" >
                                        <Form.Item
                                            {...restField}
                                            name={[name, 'rubric']}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: 'Rubric is required',
                                                },
                                            ]}>
                                            <Input placeholder="Rubric" />
                                        </Form.Item>
                                        <Form.Item
                                            {...restField}
                                            name={[name, 'mark']}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: 'Marks is required',
                                                },
                                            ]} >
                                            <Input placeholder="marks" />
                                        </Form.Item>
                                        <MinusCircleOutlined onClick={() => { remove(name) }} />
                                    </Space>
                                ))}
                                <Form.Item>
                                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                                        {setLocale('Add Rubrics')}
                                    </Button>
                                </Form.Item>
                            </>
                        )}
                    </Form.List>
                </Col>            
            </Row>
            <Row>
                <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                    <Form.Item label="">
                        <Button className='rounded-0' loading={saveStudentOnlyBtn} type="primary" htmlType="submit">
                            {setLocale('save')}
                        </Button>
                    </Form.Item>
                </Col>
            </Row>
        </Form>            
        </>
    )
}

export default AddRubricOnly;

import React,{useEffect, useState} from 'react';
import { Select, Button, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { COURSE_CATEGORIES } from "constants/AppConstants";
import {
    CourseCategoriesAddDrawerStatus,
    createCourseCategories,
    getCourseCategories,
    onCloseError
  } from "store/slices/CourseCategories/manageCourseCategoriesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
const {
    CourseCategoriesAddDrawer,
    CourseCategoriesButtonAndModelLabel,
    sorting,
    filter,
    CourseCategoriesErrors,
    CourseCategoriesShowMessage,
    CourseCategoriesButtonSpinner,
    CourseCategoriesEditData,
    tablePagination,
    DrawerStatus
} = useSelector((state) => state[COURSE_CATEGORIES]);
const onClose = () => {
    dispatch(CourseCategoriesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (CourseCategoriesEditData && CourseCategoriesEditData.id) {
    // If editing, include the id in the form values
    formValues.id = CourseCategoriesEditData.id;
  }

  await dispatch(createCourseCategories(formValues));
};

  useEffect(() => {
    if (Object.keys(CourseCategoriesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getCourseCategories({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [CourseCategoriesErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={CourseCategoriesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CourseCategoriesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: CourseCategoriesEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={CourseCategoriesShowMessage && CourseCategoriesErrors.name ? "error" : ""}
                help={CourseCategoriesShowMessage && CourseCategoriesErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CourseCategoriesButtonSpinner}
            >
              {CourseCategoriesButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


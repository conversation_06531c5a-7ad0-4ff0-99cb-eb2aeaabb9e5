import React from "react";
import { useSelector } from "react-redux";
import { Row, Col, Card, Skeleton, Progress } from 'antd';
import Chart from "react-apexcharts";
import { COLOR_1, COLOR_2, COLOR_4 } from 'constants/ChartConstant';
import { INVOICES } from "constants/AppConstants";

function RevenueAndPaymentStatus() {
    const { dashboardData, dashboardDataLoading } = useSelector((state) => state[INVOICES]);

    const state = {
        series:
            dashboardData?.annualInvoices?.invoiceChartData ?? [],
        options: {
            chart: {
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                },
            },
            colors: [COLOR_2, COLOR_1, COLOR_4],
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                // categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],
                categories: dashboardData?.annualInvoices?.categories ?? [],
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    // formatter: val => (`$${val} thousands`)
                    formatter: val => (val)
                }
            }
        }

    };

    const chartSeries = (dashboardData?.annualInvoices?.invoiceChartData ?? [])
        .map(s => ({
            // shallow clone each series object...
            name: s.name,
            // ...and shallow-clone its data array
            data: Array.isArray(s.data) ? [...s.data] : []
        }));

    const chartOptions = {
        ...state.options,
        xaxis: {
            // clone nested structures if you find Apex mutating them
            ...state.options.xaxis,
            categories: [...(dashboardData?.annualInvoices?.categories ?? [])]
        }
        // copy any other nested objects you suspect Apex might write to
    };

    return (
        <>
            <Row gutter={16}>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                    <Card className="p-3 invoices-card">
                        <div className="d-flex justify-content-between">
                            <div>
                                <h5>Payment Status</h5>
                            </div>
                        </div>
                        {dashboardDataLoading ? (
                            <Skeleton loading={true} shape="round" active size="small" />
                        ) : (
                            <Chart
                                options={chartOptions}
                                series={chartSeries}
                                type="bar"
                                height={235}
                            />
                        )}
                    </Card>
                </Col>
                <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                    <Card className="p-3 invoices-card">
                        <div className="d-flex justify-content-center">
                            <div>
                                <h3>Fee Collection</h3>
                            </div>
                        </div>
                        <div className="d-flex justify-content-center mt-3">
                            {dashboardDataLoading ? (
                                <Skeleton loading={true} shape="round" active size="small" />
                            ) : (
                                <Progress type="circle" width={180} strokeWidth={10} strokeColor={"#388C45"} percent={dashboardData?.invoicesDueDateCount?.feeCollectionPercentage ?? 0} />
                            )}
                        </div>
                    </Card>
                </Col>
                <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                    <Card className="p-3 invoices-card">
                        <div className="d-flex justify-content-center">
                            <div>
                                <h3>Revenue Status</h3>
                            </div>
                        </div>
                        {dashboardDataLoading ? (
                            <Skeleton loading={true} shape="round" active size="small" />
                        ) : (
                            <>
                                <div className="d-flex justify-content-center">
                                    <Progress type="circle" width={130} strokeWidth={10} strokeColor={"#FFB931"} percent={dashboardData?.invoicesDueDateCount?.totalPaidPercentage ?? 0} />
                                </div>
                                <div className="d-flex justify-content-center mt-1">
                                    <h3>$ {dashboardData?.invoicesDueDateCount?.totalInvoicesSum ?? 0}</h3>
                                </div>
                                <div className="d-flex justify-content-between">
                                    <div> <span style={{ color: "#FFB931", fontSize: "15px", height: "40px" }}>.</span> Receivable</div>
                                    <span>$ {dashboardData?.invoicesDueDateCount?.totalPendingInvoicesSum ?? 0}</span>
                                </div>
                                <div className="d-flex justify-content-between">
                                    <div> <span style={{ color: "#FFB931", fontSize: "15px", height: "40px" }}>.</span> Partial Received</div>
                                    <span>$ {dashboardData?.invoicesDueDateCount?.totalPartialPaidInvoicesSum ?? 0}</span>
                                </div>
                                <div className="d-flex justify-content-between">
                                    <div> <span style={{ color: "#EEF0F7", fontSize: "15px", height: "40px" }}>.</span> Received</div>
                                    <span>$ {dashboardData?.invoicesDueDateCount?.totalPaidInvoicesSum ?? 0}</span>
                                </div>
                            </>
                        )}

                    </Card>
                </Col>
            </Row>
        </>
    );
}

export default RevenueAndPaymentStatus;

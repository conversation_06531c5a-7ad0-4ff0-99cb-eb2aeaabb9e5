import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from "react-redux";
import { CHAT } from "constants/chat/index";
import { env } from "configs/EnvironmentConfig"

import EllipsisDropdown from 'components/shared-components/EllipsisDropdown'
import { Avatar, Menu } from 'antd';
import AvatarStatus from 'components/shared-components/AvatarStatus/chatAvatar';
import {
	getConversationMessages,
	setSelectedConversation,
	setWhoIsTyping,
	setChatDetailDrawer
} from "store/slices/Chat/manageChatSlice.js";
import { 
	// AudioMutedOutlined,
	UserOutlined,
	// DeleteOutlined,
	CloseCircleOutlined
} from '@ant-design/icons';
import Echo from 'broadcasting/laravelEcho'

import { USER_INFORMATION } from "constants/AuthConstant";
import ChatDetail from './ChatDetail';
const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION)) || null;


const ChatContentHeader = () => {
	const dispatch = useDispatch();
	const { 
		selectedConversation, 
		whoIstypingUser,
		chatDetailDrawer
	} = useSelector((state) => state[CHAT]);

	useEffect(() => {
		if (!selectedConversation) return;
			dispatch(getConversationMessages({ conversation_id: selectedConversation.conversation_id }));

			const channel = Echo.private(`user-chat-${selectedConversation.conversation_id}`)
			// .subscribed(() => {
			// 	console.log("Admin Successfully subscribed to private channel");
			// })
			.listenForWhisper('typing', (data) => {
				console.log(data, 'typing');
				if (data.user_id !== currentUser.id) {
					dispatch(setWhoIsTyping(data.name));
					// Clear after 3 seconds
					setTimeout(() => dispatch(setWhoIsTyping(null)), 3000);
				}
			});
			return () => {
				channel.stopListeningForWhisper('typing');
			};		
	}, [selectedConversation]);

	const closeChat = async() => { 
		await dispatch(setSelectedConversation(null))		
	};
	
	const openChatDetail = async() => {
		await dispatch(setChatDetailDrawer(true))
	}
	const menu = () => (
		<Menu>
			 <Menu.Item key="chat-detail" onClick={() => openChatDetail()}>
				<UserOutlined />
				<span className="ml-1">{"Detail"}</span>
			</Menu.Item>
			{/*<Menu.Item key="1">
				<AudioMutedOutlined />
				<span>Mute Chat</span>
			</Menu.Item>
			<Menu.Divider />
			<Menu.Item key="3">
				<DeleteOutlined />
				<span>Delete Chat</span>
			</Menu.Item> */}			
			<Menu.Item key="close-chat" onClick={() => closeChat()}>
				<CloseCircleOutlined className="text-danger" />
				<span className="ml-1">{"Close"}</span>
			</Menu.Item>
		</Menu>
	);
	return (
		<>
			<div className="chat-content-header">
				<h4 className="mb-0">
					<AvatarStatus src={selectedConversation?.profile_picture ? env.FILE_ENDPOINT_URL + selectedConversation?.profile_picture : "/img/avatars/user.png"}
						name={selectedConversation.name} subTitle={whoIstypingUser ? whoIstypingUser + " is typing..." : "" } item={selectedConversation} />
				</h4>
				<div>
					<EllipsisDropdown menu={menu}/>
				</div>
			</div>
			{ chatDetailDrawer && <ChatDetail /> }
		</>
	)
}

export default ChatContentHeader

import React,{useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LEAVE_TYPE } from "constants/AppConstants";
import {
    LeaveTypeAddDrawerStatus,
    createLeaveType,
    getLeaveType,
    onCloseError
  } from "store/slices/LeaveType/manageLeaveTypeSlice.js";
import IntlMessage from "components/util-components/IntlMessage";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const Index = () => {
const [form] = Form.useForm();

const dispatch = useDispatch();
    const {
        LeaveTypeAddDrawer,
        LeaveTypeButtonAndModelLabel,
        sorting,
        filter,
        LeaveTypeErrors,
        LeaveTypeShowMessage,
        LeaveTypeButtonSpinner,
        LeaveTypeEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[LEAVE_TYPE]
    );
const onClose = () => {
    dispatch(LeaveTypeAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (LeaveTypeEditData && LeaveTypeEditData.id) {
    // If editing, include the id in the form values
    formValues.id = LeaveTypeEditData.id;
  }

  await dispatch(createLeaveType(formValues));
};

  useEffect(() => {
    if (Object.keys(LeaveTypeErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getLeaveType({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [LeaveTypeErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};


  return (
    <>

      <Drawer
        title={LeaveTypeButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={LeaveTypeAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: LeaveTypeEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={LeaveTypeShowMessage && LeaveTypeErrors.name ? "error" : ""}
                help={LeaveTypeShowMessage && LeaveTypeErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>
      

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={LeaveTypeButtonSpinner}
            >
              {LeaveTypeButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


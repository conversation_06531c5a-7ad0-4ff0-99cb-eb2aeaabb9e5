import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Row, Col, Table, Popconfirm, Pagination, Button, Input, Space, DatePicker, Breadcrumb, Card, Skeleton, Select, Tooltip, Form, Modal } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined, ArrowDownOutlined, AppstoreOutlined, TableOutlined, ArrowUpOutlined } from '@ant-design/icons';
import Chart from "react-apexcharts";
import { COLORS } from 'constants/ChartConstant';
import Highlighter from 'react-highlight-words';
import { INCIDENT_ACTION_CODE, INCIDENT_REPORT, INCIDENT_TYPE, LOCATIONS, MANAGE_CLASS, NAME_OF_COMMON_SLICE, SCHOOL_YEAR } from "constants/AppConstants";
import AddIncidentReportModal from "./Modals/Add";
import {
    IncidentReportAddDrawerStatus,
    getIncidentDashboardData,
    deleteIncidentReport,
    getIncidentReport,
    updateSortFilters,
    updateDashboardFilters,
    getIncidentsData, getLocationWise,
    getCategoryWise,
    getActionWise
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import { getOrganizationGradeLevel } from "store/slices/commonSlice";
import { getSchoolYear } from 'store/slices/SchoolYear/manageSchoolYearSlice';
import { useNavigate } from 'react-router-dom';
import { getManageClass, get_class_courses } from "store/slices/ManageClass/manageManageClassSlice";
import { getLocations } from "store/slices/Locations/manageLocationsSlice";
import { getIncidentType } from "store/slices/IncidentType/manageIncidentTypeSlice";
import { getIncidentActioncode } from "store/slices/IncidentActionCode/manageIncidentActionCodeSlice";
import { DATE_FORMAT_YYYY_MM_DD } from "constants/DateConstant";
import moment from 'moment';
import 'moment-timezone';
import { USER_INFORMATION } from 'constants/AuthConstant';
import OrganizationSelect from "components/Admin/OrganizationDropdown/OrganizationSelect";
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { Option } = Select;
const { RangePicker } = DatePicker;

function Index() {
    const [form] = Form.useForm();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [searchText, setSearchText] = useState('');
    const [gridLoader, setGridLoader] = useState(true);
    const [searchedColumn, setSearchedColumn] = useState('');
    const [view, setView] = useState('grid');
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(IncidentReportAddDrawerStatus(true));
    const [organization, setOrganization] = useState(selectedOrganization);

    const {
        incidentDashboardLoading,
        incidentDashboardData,
        IncidentReportAddDrawer,
        IncidentReportResult,
        tablePagination,
        sorting,
        filter,
        dashboardFilters,
        IncidentReportTableLoading,
        IncidentsData,
        permission,
        incidentDataLoading,
        countData,
        ActionWise,
        CategoryWise,
        IncidentsForecast,
        LocationWise,
        RequireAttention,
        TimeFrame
    } = useSelector(
        (state) => state[INCIDENT_REPORT]
    );


    const { SchoolYearResult } = useSelector(
        (state) => state[SCHOOL_YEAR]
    );
    const {
        ManageClassResult,
        ClassCourses
    } = useSelector((state) => state[MANAGE_CLASS])

    const { OrganizationGradeLevelResult } = useSelector((state) => state[NAME_OF_COMMON_SLICE]);
    const { LocationsResult } = useSelector((state) => state[LOCATIONS]);
    const { IncidentTypeResult } = useSelector((state) => state[INCIDENT_TYPE]);
    const { IncidentActioncodeResult } = useSelector((state) => state[INCIDENT_ACTION_CODE]);

    const switchToGridView = () => {
        setView('grid');
    };

    const switchToTableView = () => {
        setView('table');
    };
    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };
    const handleReset = (clearFilters) => {
        clearFilters();
        setSearchText('');
    };
    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder={`Search ${dataIndex}`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('Search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(clearFilters)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('Reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filtered ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{
                        backgroundColor: '#ffc069',
                        padding: 0,
                    }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const fetchClass = async (yearID) => {
        await dispatch(getManageClass({ schoolYear: yearID ? yearID : null, organization_id: organization }));
    }

    let yearID = null
    const fetchData = async () => {
        await dispatch(getLocations({ perPage: 1000 }));
        await dispatch(getIncidentType({ perPage: 1000 }));
        await dispatch(getIncidentActioncode({ perPage: 1000 }));
        await dispatch(getIncidentReport({ page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting }));
        await dispatch(getSchoolYear({ perPage: 1000 })).then((result) => {
            if (dashboardFilters.schoolYearId) {
                yearID = dashboardFilters?.schoolYearId;
            } else {

                yearID = userInformation.school_year_id;
                form.setFieldValue('schoolYearId', yearID);
                dispatch(updateDashboardFilters({
                    ...dashboardFilters,
                    schoolYearId: yearID ? yearID : null,
                }));
            }
            fetchClass(yearID);
        });
        await dispatch(getOrganizationGradeLevel({ perPage: 1000 }));
        setGridLoader(false);
    };

    useEffect(() => {
        fetchData();
    }, []);

    useEffect(() => {
        dispatch(getIncidentDashboardData(dashboardFilters));
    }, [dashboardFilters]);

    const handlePageChange = (page, pageSize) => {
        dispatch(getIncidentReport({ page: page, perPage: pageSize, filter: filter, sorting: sorting }));
    };

    const handleDelete = (record) => {
        dispatch(deleteIncidentReport(record.id)).then(() => {
            dispatch(getIncidentReport({ page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting }));
        })
    }
    const handleUpdate = (record) => {
        navigate(`/app/edit-incident-report/${record.enc_id}`)
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            await dispatch(
                getIncidentReport({
                    page: 1,
                    perPage: tablePagination.pageSize,
                    filter: filter,
                    sorting: sorting,
                })
            );
        } catch (error) {
            console.log(error);
        }

    };


    const columns = [
        {
            title: setLocale('name'),
            dataIndex: "title",
            key: "name",
            sorter: true,
            ...getColumnSearchProps('title'),
        },
        {
            title: setLocale('createdAt'),
            dataIndex: "created_at",
            key: "created_at",
            sorter: true,
            ...getColumnSearchProps('created_at'),
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sureToDelete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                    {permission.includes("View") && (
                        <Link to={`../../app/incident-report_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )}
                </>
            )
        },
    ];


    const barStateForTimeFrame = {
        series: [TimeFrame?.before ?? 0, TimeFrame?.during ?? 0, TimeFrame?.after ?? 0],
        options: {
            colors: ["#FFC72A", "#76CBF4", "#925EF8"],
            labels: ['Before School', 'During School', 'After School'],
            legend: {
                position: 'bottom'
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        }
    };

    const barStateForLocation = {
        series: [{
            data: LocationWise?.locationCount ?? []
        }],
        options: {
            plotOptions: {
                bar: {
                    horizontal: true,
                    barHeight: '50%', // Adjust the height of the bars
                }
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                categories: LocationWise?.locationName ?? [],
            },
            colors: ["#47A580"], // Adjust the color for the bars
            chart: {
                toolbar: {
                    show: false
                }
            },
            tooltip: {
                enabled: false
            }
        }
    };

    const barStateForCategory = {
        series: [{
            data: CategoryWise?.incident_type_count ?? []
        }],
        options: {
            plotOptions: {
                bar: {
                    horizontal: true,
                    barHeight: '50%', // Adjust the height of the bars
                }
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                categories: CategoryWise?.incident_type_name ?? [],
            },
            colors: ["#47A580"], // Adjust the color for the bars
            chart: {
                toolbar: {
                    show: false
                }
            },
            tooltip: {
                enabled: false
            }
        }
    };

    const barStateForAction = {
        series: [{
            data: ActionWise?.action_code_count ?? []
        }],
        options: {
            plotOptions: {
                bar: {
                    horizontal: true,
                    barHeight: '50%', // Adjust the height of the bars
                }
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                categories: ActionWise?.action_code ?? [],
            },
            colors: ["#47A580"], // Adjust the color for the bars
            chart: {
                toolbar: {
                    show: false
                }
            },
            tooltip: {
                enabled: false
            }
        }
    };

    const areaState = {
        series: [{
            data: IncidentsForecast?.series ?? []
        }],
        options: {
            dataLabels: {
                enabled: false
            },
            chart: {
                toolbar: {
                    show: false
                }
            },
            colors: COLORS,
            stroke: {
                curve: 'smooth'
            },
            xaxis: {
                categories: IncidentsForecast?.months_array ?? []
            },
        },
    };


    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState(null);

    const columnsStudent = [
        {
            title: 'Student',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Grade level',
            dataIndex: 'grade_level',
            key: 'grade_level',
        },
        {
            title: 'No. of Incidents',
            dataIndex: 'incident_count',
            key: 'incident_count',
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    <EyeOutlined style={{ fontSize: '15px' }} onClick={(e) => handleView(record)} className="text-primary" />
                </>
            )
        },

    ];


    const columnsStudentDetail = [
        {
            title: 'Incident',
            dataIndex: 'title',
            key: 'title',
        },
        {
            title: 'Incident Date',
            dataIndex: 'incident_date',
            key: 'incident_date',
        },
        {
            title: 'Location',
            dataIndex: 'location_name',
            key: 'location_name',
        },
        {
            title: 'Reported By',
            dataIndex: 'reported_by',
            key: 'reported_by',
        },
        {
            title: 'Prepared By',
            dataIndex: 'prepared_by',
            key: 'prepared_by',
        },
        {
            title: 'Time Frame',
            dataIndex: 'time_frame',
            key: 'time_frame',
        },

    ];

    const handleView = (record) => {
        setSelectedRecord(record);
        setIsModalVisible(true);
        const incidentIdsArray = record?.incident_ids.split(",").map(id => parseInt(id.trim()));
        dispatch(getIncidentsData({ 'student_id': record?.id, incident_ids: incidentIdsArray }));
    };

    const handleCancel = () => {
        setIsModalVisible(false);
    };

    const onSchoolYearHandel = async (year) => {
        await dispatch(updateDashboardFilters({
            ...dashboardFilters,
            schoolYearId: year ? year : null,
        }));
    }

    const onGradeLevelHandel = async (grade) => {
        await dispatch(updateDashboardFilters({
            ...dashboardFilters,
            gradeLevelId: grade ? grade : null,
        }));
        await dispatch(getManageClass({ schoolYear: yearID ? yearID : null, gradeLevel: grade ? grade : null, organization_id: organization }));
    }

    const onClassHandel = async (classId) => {
        await dispatch(updateDashboardFilters({
            ...dashboardFilters,
            classId: classId ? classId : null,
        }));
        await dispatch(get_class_courses({ class_id: classId ? classId : null }));
    }

    const handleLocationChange = (value) => {
        // dispatch(updateDashboardFilters({
        //     ...dashboardFilters,
        //     locationId: value ? value : null,
        // }));
        dispatch(getLocationWise({ ...dashboardFilters, requestFrom: 'location', locationId: value ? value : null }));
    }
    const handleActionChange = (value) => {
        // dispatch(updateDashboardFilters({
        //     ...dashboardFilters,
        //     actionId: value ? value : null,
        // }));
        dispatch(getActionWise({ ...dashboardFilters, requestFrom: 'action', actionId: value ? value : null }));
    }
    const handleTypeChange = (value) => {
        // dispatch(updateDashboardFilters({
        //     ...dashboardFilters,
        //     typeId: value ? value : null,
        // }));
        dispatch(getCategoryWise({ ...dashboardFilters, requestFrom: 'type', typeId: value ? value : null }));
    }

    const onRangeDateHandel = async (range) => {
        await dispatch(updateDashboardFilters({
            ...dashboardFilters,
            dateRange: range ? range : null,
        }));
    }

    const disabledDate = (current) => {
        const validSchoolYears = SchoolYearResult?.data?.filter(item => {
            if (item.id == userInformation.school_year_id) {
                const start = moment(item.start_date);
                const end = moment(item.end_date);
                return current >= start && current <= end;
            }
        });

        // Check if current date is within the valid school years
        return !validSchoolYears.some(item => {
            const start = moment(item.start_date);
            const end = moment(item.end_date);
            return current >= start && current <= end;
        });
    };


    const onChangeOrganization = (value) => {
        setOrganization(value);
        dispatch(getManageClass({ organization_id: value }));
        dispatch(getOrganizationGradeLevel({ perPage: 1000, organization_id: value }));

    }

    return (
        <>
            <div className="d-flex justify-content-between">
                <Breadcrumb className="mt-2 mx-2">
                    <Breadcrumb.Item>
                        <Link to="/app/default">{setLocale('home')}</Link>
                    </Breadcrumb.Item>
                    <Breadcrumb.Item>{setLocale('Incident Report')}</Breadcrumb.Item>
                </Breadcrumb>
                <div className="d-flex justify-content-end mb-1 mt-1 mr-3" style={{ marginTop: "-30px" }}>
                    <Tooltip title="Grid View">
                        <Button
                            icon={<AppstoreOutlined />}
                            onClick={switchToGridView}
                            style={{ marginRight: '8px' }}
                            type={view === 'grid' ? 'primary' : 'default'}
                        />
                    </Tooltip>

                    {/* Icon for table view */}
                    <Tooltip title="Table View">
                        <Button
                            icon={<TableOutlined />}
                            onClick={switchToTableView}
                            type={view === 'table' ? 'primary' : 'default'}
                        />
                    </Tooltip>
                </div>
            </div>
            <>
                {/* Dashboard Grid View */}
                {view === 'grid' && (
                    <>
                        <div className="code-box">
                            <section className="pl-3 pr-3">
                                <Row gutter={16} className='mt-3'>
                                    <Col xs={24} sm={24} md={12} lg={24} xl={24} className="d-flex justify-content-between">
                                        <div className='grade-book-selec' style={{ width: '100%' }}>
                                            <Form
                                                layout="horizontal"
                                                form={form}
                                                autoComplete="off"
                                                initialValues={{ ...dashboardFilters }}
                                            >

                                                <Row gutter={16}>
                                                    <h5 className='my-2 mx-2'>Filters: </h5>
                                                    {/* <Col xs={24} sm={24} md={2} lg={2} xl={2}>
                                                        <h5 className='my-2 mx-2'>Filters: </h5>
                                                    </Col> */}

                                                    {userOrganizations.length > 1 &&
                                                        <>
                                                            <Col xs={24} sm={24} md={5} lg={5} xl={5} >
                                                                <Form.Item
                                                                    name="organization_id"
                                                                    rules={[
                                                                        {
                                                                            required: false,
                                                                            message: setLocale('Please Select Class'),
                                                                        },
                                                                    ]}
                                                                >
                                                                    <OrganizationSelect
                                                                        updateSortFilters={updateDashboardFilters}
                                                                        onChange={onChangeOrganization}
                                                                        filter={dashboardFilters}
                                                                        sorting={sorting}
                                                                        tablePagination={tablePagination}
                                                                        getModuleData={getIncidentDashboardData}
                                                                        runUseEffect={false}
                                                                        dropStyle={true}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </>
                                                    }


                                                    <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                                                        <Form.Item
                                                            name="dateRange"
                                                            rules={[
                                                                {
                                                                    required: false,
                                                                    message: setLocale('school_year.dateRange_error'),
                                                                },
                                                            ]}
                                                        >

                                                            <RangePicker
                                                                onChange={(value) => onRangeDateHandel(value)}
                                                                disabledDate={disabledDate}
                                                                value={dashboardFilters.dateRange}
                                                                format={DATE_FORMAT_YYYY_MM_DD}
                                                                style={{ width: '100%' }}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col xs={24} sm={24} md={3} lg={3} xl={3}>
                                                        <Form.Item
                                                            name="gradeLevelId"
                                                            rules={[
                                                                {
                                                                    required: false,
                                                                    message: setLocale('school_year.school_year_error'),
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                className='rounded-0'
                                                                placeholder={setLocale('classes.school_grade')}
                                                                showSearch
                                                                optionLabelProp="label"
                                                                allowClear
                                                                value={dashboardFilters.gradeLevelId}
                                                                onChange={(value) => onGradeLevelHandel(value)}
                                                                optionFilterProp="children"
                                                                filterOption={(input, option) => (
                                                                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                                                                )}
                                                                // filterSort={(optionA, optionB) =>
                                                                //     (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                                                                // }
                                                                options={OrganizationGradeLevelResult ?? []}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col xs={24} sm={24} md={5} lg={5} xl={5}>
                                                        <Form.Item
                                                            name="classId"
                                                            rules={[
                                                                {
                                                                    required: false,
                                                                    message: setLocale('school_year.school_year_error'),
                                                                },
                                                            ]}
                                                        >

                                                            <Select
                                                                className='mx-2'
                                                                placeholder={setLocale('classes.class')}
                                                                showSearch
                                                                optionLabelProp="label"
                                                                allowClear
                                                                value={dashboardFilters.classId}
                                                                onChange={(value) => onClassHandel(value)}
                                                                optionFilterProp="children"
                                                                filterOption={(input, option) => (
                                                                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                                                                )}
                                                                filterSort={(optionA, optionB) =>
                                                                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                                                                }
                                                                options={ManageClassResult ?? []}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col xs={24} sm={24} md={3} lg={3} xl={3} className="ml-3">
                                                        {permission.includes("Create") && (
                                                            <>
                                                                <Link to={`../../app/add-incident-report`}>
                                                                    <Button
                                                                        className="ant-btn-round ant-btn-sm"
                                                                        type="primary"
                                                                        style={{ float: "right", margin: "5px" }}
                                                                    >
                                                                        {setLocale('incidentReport.add')}
                                                                    </Button>
                                                                </Link>
                                                            </>
                                                        )}
                                                    </Col>
                                                </Row>
                                            </Form>
                                        </div>
                                    </Col>
                                </Row>
                            </section>
                            {IncidentReportAddDrawer && <AddIncidentReportModal />}
                        </div>

                        <Row gutter={16}>
                            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                                <Card className="p-3">
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> :
                                        (
                                            <>
                                                <div className="d-flex justify-content-between">
                                                    <h3 className="mt-2"> Students </h3>
                                                    <h1> {countData?.student?.student_count ?? 0} </h1>
                                                </div>
                                                <div className="mt-4"><span className={countData?.student?.student_class}>
                                                    {
                                                        countData?.student?.student_icon_arrow === 'up' ?
                                                            <ArrowUpOutlined />
                                                            :
                                                            <ArrowDownOutlined />
                                                    }
                                                    {countData?.student?.student_percen_difference}%</span> vs. Last Year
                                                </div>
                                            </>
                                        )}

                                </Card>
                            </Col>
                            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                                <Card className="p-3">
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> :
                                        (
                                            <>
                                                <div className="d-flex justify-content-between">
                                                    <h3 className="mt-2"> Others </h3>
                                                    <h1> {countData?.other?.other_count ?? 0} </h1>
                                                </div>
                                                <div className="mt-4"><span className={countData?.other?.other_class ?? 0}>
                                                    {
                                                        countData?.other?.other_icon_arrow === 'up' ?
                                                            <ArrowUpOutlined />
                                                            :
                                                            <ArrowDownOutlined />
                                                    } {countData?.other?.other_count ?? 0}%</span> vs. Last Year
                                                </div>
                                            </>
                                        )}
                                </Card>
                            </Col>
                            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                                <Card className="p-3">
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> :
                                        (
                                            <>
                                                <div className="d-flex justify-content-between">
                                                    <h3 className="mt-2"> Staff </h3>
                                                    <h1> {countData?.staff?.staff_count ?? 0} </h1>
                                                </div>
                                                <div className="mt-4"><span className={countData?.staff?.staff_class ?? 0}>
                                                    {
                                                        countData?.staff?.staff_icon_arrow === 'up' ?
                                                            <ArrowUpOutlined />
                                                            :
                                                            <ArrowDownOutlined />
                                                    } {countData?.staff?.staff_count ?? 0}%</span> vs. Last Year
                                                </div>
                                            </>
                                        )}
                                </Card>
                            </Col>
                        </Row>

                        <Row gutter={16}>
                            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                                <Card className="p-3 incident-card">
                                    <h3>Time Frame</h3>
                                    <h5>Summary of incident by Time Frame</h5>
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> : (
                                        <Chart
                                            options={barStateForTimeFrame.options}
                                            series={barStateForTimeFrame.series}
                                            height={300}
                                            type="pie"
                                        />
                                    )}
                                </Card>
                            </Col>
                            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                                <Card className="p-3 incident-card">
                                    <div className="d-flex justify-content-between">
                                        <div>
                                            <h3>Location</h3>
                                            <h5>Summary of incident by Location</h5>
                                        </div>
                                        <div>
                                            <Select
                                                placeholder="Location"
                                                onChange={handleLocationChange}
                                                style={{ color: "#C54545", fontWeight: "900" }}
                                                showSearch
                                                optionLabelProp="label"
                                                allowClear
                                                optionFilterProp="children"
                                                filterOption={(input, option) => (
                                                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                                                )}
                                                filterSort={(optionA, optionB) =>
                                                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                                                }
                                                options={LocationsResult?.data ?? []}

                                            />
                                        </div>
                                    </div>
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> : (
                                        <Chart
                                            options={barStateForLocation.options}
                                            series={barStateForLocation.series}
                                            type="bar"
                                            height={300}
                                        />
                                    )}
                                </Card>
                            </Col>
                            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                                <Card className="p-3 incident-card">
                                    <div className="d-flex justify-content-between">
                                        <div>
                                            <h3>Frequent Actions</h3>
                                            <h5>Summary of incident by action</h5>
                                        </div>
                                        <div>
                                            <Select
                                                placeholder="Action Code"
                                                onChange={handleActionChange}
                                                style={{ color: "#C54545", fontWeight: "900" }}
                                                showSearch
                                                optionLabelProp="label"
                                                allowClear
                                                optionFilterProp="children"
                                                filterOption={(input, option) => (
                                                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                                                )}
                                                filterSort={(optionA, optionB) =>
                                                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                                                }
                                                options={IncidentActioncodeResult?.data ?? []}

                                            />
                                        </div>
                                    </div>
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> : (
                                        <Chart
                                            options={barStateForAction.options}
                                            series={barStateForAction.series}
                                            type="bar"
                                            height={300}
                                        />
                                    )}
                                </Card>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col xs={24} sm={24} md={16} lg={16} xl={16}>
                                <Card className="p-3 incident-card">
                                    <h3>Incident Forecast</h3>
                                    <h5>Summary of Incidents Forecast</h5>
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> : (
                                        <Chart
                                            options={areaState.options}
                                            series={areaState.series}
                                            type="area"
                                            height={300}
                                        />
                                    )}
                                </Card>
                            </Col>
                            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                                <Card className="p-3 incident-card">
                                    <div className="d-flex justify-content-between">
                                        <div>
                                            <h3>Incident by Category</h3>
                                            <h5>Summary of incident by category</h5>
                                        </div>
                                        <div>
                                            <Select
                                                placeholder="Category"
                                                onChange={handleTypeChange}
                                                style={{ color: "#C54545", fontWeight: "900" }}
                                                showSearch
                                                optionLabelProp="label"
                                                allowClear
                                                optionFilterProp="children"
                                                filterOption={(input, option) => (
                                                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                                                )}
                                                filterSort={(optionA, optionB) =>
                                                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                                                }
                                                options={IncidentTypeResult?.data ?? []}

                                            />
                                        </div>
                                    </div>
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> : (
                                        <Chart
                                            options={barStateForCategory.options}
                                            series={barStateForCategory.series}
                                            type="bar"
                                            height={300}
                                        />
                                    )}
                                </Card>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                <Modal
                                    title="Student Incident Details"
                                    visible={isModalVisible}
                                    width={'45%'}
                                    onCancel={handleCancel}
                                    footer={[
                                        <Button key="cancel" onClick={handleCancel}>
                                            Close
                                        </Button>,
                                    ]}
                                >
                                    {/* Render your modal content here */}
                                    {selectedRecord && (
                                        <>
                                            <p>Student Name: {selectedRecord.name}</p>
                                            <p>Grade Level: {selectedRecord.grade_level}</p>
                                            <p>No. of Incidents: {selectedRecord.incident_count}</p>

                                            <Table dataSource={IncidentsData?.data ?? []} loading={incidentDataLoading} columns={columnsStudentDetail} className="incident-table-head" />
                                        </>
                                    )}
                                </Modal>
                                <Card className="p-3">
                                    <div className="d-flex justify-content-between mb-3">
                                        <div>
                                            <h3>People who Require Attention</h3>
                                        </div>
                                    </div>
                                    {gridLoader ? <Skeleton active loading={gridLoader} paragraph /> : (
                                        <Table dataSource={RequireAttention ?? []} columns={columnsStudent} className="incident-table-head" />
                                    )}
                                </Card>
                            </Col>
                        </Row>
                    </>
                )}
                {/* End Dashboard Grid View */}

                {/* Table View */}
                {view === 'table' && (
                    <>
                        <div className="code-box">
                            <section className="code-box-demo">
                                {permission.includes("Create") && (
                                    <>
                                        <Link to={`../../app/add-incident-report`}>
                                            <Button
                                                className="ant-btn-round ant-btn-sm"
                                                type="primary"
                                                style={{ float: "right", margin: "5px" }}
                                            // onClick={handleOpenModal}
                                            >
                                                {setLocale('incidentReport.add')}
                                            </Button>
                                        </Link>
                                    </>
                                )}
                            </section>
                            {IncidentReportAddDrawer && <AddIncidentReportModal />}
                            <section className="code-box-description">
                                <Table
                                    onChange={handleTableChange}
                                    columns={columns}
                                    loading={IncidentReportTableLoading}
                                    rowKey={record => record.id}
                                    dataSource={IncidentReportResult.data ?? []}
                                    pagination={false}
                                />
                                <Pagination
                                    style={{ margin: '16px', float: 'right' }}
                                    current={tablePagination.current}
                                    pageSize={tablePagination.pageSize}
                                    total={tablePagination.total}
                                    showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                                    pageSizeOptions={['10', '20', '50', '100', '1000']}
                                    showQuickJumper
                                    onChange={handlePageChange}
                                />
                            </section>
                        </div>
                        {/* Table View */}
                    </>
                )}
            </>
        </>
    );
}

export default Index;

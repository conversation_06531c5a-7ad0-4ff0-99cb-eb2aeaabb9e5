import React, { useEffect } from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CONVERSATION_CHANNEL } from "constants/AppConstants";
import {
    ConversationChannelAddDrawerStatus,
    createConversationChannel,
    getConversationChannel,
    onCloseError
  } from "store/slices/ConversationChannel/manageConversationChannelSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {DrawerStatus,sorting, filter, ConversationChannelAddDrawer, ConversationChannelButtonAndModelLabel, ConversationChannelErrors, ConversationChannelShowMessage, ConversationChannelButtonSpinner, ConversationChannelEditData, tablePagination } = useSelector(
        (state) => state[CONVERSATION_CHANNEL]
      );
const onClose = () => {
    dispatch(ConversationChannelAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (ConversationChannelEditData && ConversationChannelEditData.id) {
    // If editing, include the id in the form values
    formValues.id = ConversationChannelEditData.id;
  }

  await dispatch(createConversationChannel(formValues))
};
useEffect(() => {
    if (Object.keys(ConversationChannelErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getConversationChannel({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ConversationChannelErrors]);
const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={ConversationChannelButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ConversationChannelAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            conversation_channel: ConversationChannelEditData?.conversation_channel,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="conversation_channel"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={ConversationChannelShowMessage && ConversationChannelErrors.conversation_channel ? "error" : ""}
                help={ConversationChannelShowMessage && ConversationChannelErrors.conversation_channel}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={ConversationChannelButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


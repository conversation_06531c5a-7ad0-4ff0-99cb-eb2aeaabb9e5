import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tabs } from 'antd';
import IntlMessage from "components/util-components/IntlMessage";
import { BUDGET_ALLOCATION } from "constants/AppConstants";
import { 
    BudgetAllocationAddDrawerStatus 
} from "store/slices/BudgetAllocation/manageBudgetAllocationSlice.js";
import { Link } from "react-router-dom";
import PurchaseRequests from "./PurchaseRequests";
import AddBudgetAllocation from "./Modals/index";
import AllocatedBudget from "./AllocatedBudget";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    // const dispatch = useDispatch()
    const [allocatedBudget, setAllocatedBudget] = useState(true);
    const [budgetRequests, setBudgetRequests] = useState(false);

    const onChangeTab = (key) => {
        setAllocatedBudget(false)
        setBudgetRequests(false)    
        /**check the clickedKey and open onlyThat one */
        if (key === 'allocated-budget') { setAllocatedBudget(true); }
        if (key === 'budget-requests') { setBudgetRequests(true); }
    }

    // const handleOpenModal = () => dispatch(BudgetAllocationAddDrawerStatus({ errorStatus: 1, status: true }));
    // const { 
    //     BudgetAllocationAddDrawer, 
    //     BudgetAllocationButtonSpinner,
    //     permission
    // } = useSelector((state) => state[BUDGET_ALLOCATION]);

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('budgetallocation.budgetallocation')}</Breadcrumb.Item>
            </Breadcrumb>
           
            <div className="code-box">                                                  
                <section className="code-box-description">
                <Tabs
                    defaultActiveKey="allocated-budget"
                    onChange={onChangeTab}
                    items={[
                        {
                          label: setLocale('budgetallocation.allocatedbudget'),
                          key: 'allocated-budget',
                          children: allocatedBudget ? <AllocatedBudget /> : null,
                        },
                        {
                          label: setLocale('budgetallocation.budgetrequests'),
                          key: 'budget-requests',
                          children: budgetRequests ? <PurchaseRequests /> : null,
                        }
                    ]}
                />                    
                </section>
            </div>            
        </>
    );
}

export default Index;

import React, { useEffect } from 'react';
import { Button, Col, Drawer, Form, Input, Row, Select, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { GRADE_BOOK_CATEGORY } from "constants/AppConstants";
import {
  GradeBookCategoryAddDrawerStatus,
  createGradeBookCategory,
  getGradeBookCategory,
  onCloseError
} from "store/slices/GradeBookCategory/manageGradeBookCategorySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { TextArea } = Input;
const { Option } = Select;

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { DrawerStatus, sorting, filter, GradeBookCategoryAddDrawer, GradeBookCategoryButtonAndModelLabel, GradeBookCategoryErrors, GradeBookCategoryShowMessage, GradeBookCategoryButtonSpinner, GradeBookCategoryEditData, tablePagination } = useSelector(
    (state) => state[GRADE_BOOK_CATEGORY]
  );
  const onClose = () => {
    dispatch(GradeBookCategoryAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {

    if (GradeBookCategoryEditData && GradeBookCategoryEditData.id) {
      // If editing, include the id in the form values
      formValues.id = GradeBookCategoryEditData.id;
    }

    await dispatch(createGradeBookCategory(formValues))
  };

  useEffect(() => {
    if (Object.keys(GradeBookCategoryErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getGradeBookCategory({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [GradeBookCategoryErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>

      <Drawer
        title={GradeBookCategoryButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={GradeBookCategoryAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            ...GradeBookCategoryEditData ?? [],
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={GradeBookCategoryShowMessage && GradeBookCategoryErrors.name ? "error" : ""}
                help={GradeBookCategoryShowMessage && GradeBookCategoryErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item
                name="is_final_grade"
                label={setLocale("grade_book_category.is_final")}
                rules={[
                  {
                    required: true,
                    message: setLocale('grade_book_category.is_final_error'),
                  },
                ]} initialValue={1}
                validateStatus={GradeBookCategoryShowMessage && GradeBookCategoryErrors.is_final_grade ? "error" : ""}
                extra={GradeBookCategoryShowMessage && GradeBookCategoryErrors.is_final_grade}
              >
                <Select className='rounded-0'>
                  <Option value={1} key={1} >{setLocale('grade_book_category.Yes')}</Option>
                  <Option value={0} key={0} >{setLocale('grade_book_category.No')}</Option>
                </Select>
              </Form.Item>
            </Col> */}

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={GradeBookCategoryButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React from "react";
import { Card, Col, Row, Tag, Tooltip } from "antd";
import PropTypes from "prop-types";
import { EditOutlined, CalendarOutlined } from "@ant-design/icons";
import moment from "moment";

const LeaveWidget = (props) => {
  const { leaveData, onEdit } = props;
  const {
    no_of_days: noOfDays,
    carry_forward,
    document_required,
    max_days_carry_forward,
    max_leaves_together,
    start_date,
    end_date,
    description,
    employment_length,
    designation,
    is_active, // Add is_active to destructuring
  } = leaveData || {};
  const { name: leaveTypeName } = leaveData.leave_type || {};

  return (
    <Card className="p-2">
      <Row justify="space-between" align="middle" className="mb-3">
        <Col>
          <Row align="middle" gutter={8}>
            <Col>
              <div className="d-flex">
                <h2 className="mb-0">{leaveTypeName}</h2>
                <Tag
                  className="rounded-xl ml-2"
                  color={is_active ? "green" : "red"}
                >
                  {is_active ? "Active" : "Inactive"}
                </Tag>
              </div>
            </Col>
          </Row>
        </Col>
        <Col>
          <EditOutlined
            className="cursor-pointer text-lg hover:text-primary"
            onClick={onEdit}
          />
        </Col>
      </Row>

      <div className="leave-details">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <div className="d-flex align-items-start">
              <h3 className="mb-2 text-muted font-weight-normal">
                {noOfDays} days
              </h3>
            </div>
            {description && <p className="text-muted mb-2">{description}</p>}
          </Col>

          <Col span={24}>
            <Tooltip title="Maximum leaves allowed at once">
              <div className="detail-item">
                <h4 className=" font-weight-light">
                  Max Together : {max_leaves_together || "N/A"}
                  <span className="ml-2">days</span>
                </h4>
              </div>
            </Tooltip>
          </Col>

          <Col span={24}>
            <div className="detail-item">
              <h4 className=" font-weight-light">
                Max Days Carry Forward: {max_days_carry_forward || "N/A"}
                <span className="ml-2">days</span>
              </h4>
            </div>
          </Col>
          <Col span={24}>
            <div className="detail-item">
              {designation && (
                <h4 className=" font-weight-light">
                  Designation:{" "}
                  <Tag className="rounded-xl font-weight-semibold">
                    {designation?.name || "N/A"}
                  </Tag>
                </h4>
              )}
              {employment_length && (
                <h4 className=" font-weight-light">
                  Employment Length:{" "}
                  <Tag className="rounded-xl font-weight-semibold">
                    {employment_length}{" years"}
                  </Tag>
                </h4>
              )}
            </div>
          </Col>

          <Col span={24}>
            <div className="d-flex align-items-center">
              <CalendarOutlined />
              <h4 className="ml-2 text-muted font-weight-light d-flex align-items-center mb-0">
                {start_date && end_date
                  ? `${moment(start_date).format("YYYY-MM-DD")} - ${moment(
                      end_date
                    ).format("YYYY-MM-DD")}`
                  : "No date range set"}
              </h4>
            </div>
          </Col>

          <Col span={24}>
            <div className="d-flex flex-wrap gap-2">
              <Tag className="rounded-xl" color={carry_forward ? "#22C55E" : "#EF4444"}>
                {carry_forward ? "Carry Forward Allowed" : "No Carry Forward"}
              </Tag>
              <Tag className="rounded-xl" color={document_required ? "#A855F7" : "#F97316"}>
                {document_required
                  ? "Document Required"
                  : "No Document Required"}
              </Tag>
            </div>
          </Col>
        </Row>
      </div>

      <style jsx>{`
        .leave-details {
          padding: 8px 0;
        }
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }
        .text-muted {
          color: rgba(0, 0, 0, 0.45);
        }
      `}</style>
    </Card>
  );
};

LeaveWidget.propTypes = {
  leaveData: PropTypes.shape({
    no_of_days: PropTypes.number,
    carry_forward: PropTypes.bool,
    document_required: PropTypes.bool,
    max_days_carry_forward: PropTypes.number,
    max_leaves_together: PropTypes.number,
    start_date: PropTypes.string,
    end_date: PropTypes.string,
    description: PropTypes.string,
    is_active: PropTypes.bool, // Add is_active to PropTypes
    leave_type: PropTypes.shape({
      name: PropTypes.string,
    }),
  }),
  onEdit: PropTypes.func,
};

export default LeaveWidget;

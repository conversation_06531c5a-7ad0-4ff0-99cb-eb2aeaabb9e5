import React from "react";
import { Card, Col, Row, Tag, Tooltip } from "antd";
import PropTypes from "prop-types";
import { EditOutlined, CalendarOutlined } from "@ant-design/icons";
import moment from 'moment';

const LeaveWidget = (props) => {
  const { leaveData, onEdit } = props;
  const { 
    no_of_days: noOfDays,
    carry_forward,
    document_required,
    max_days_carry_forward,
    max_leaves_together,
    start_date,
    end_date,
    description
  } = leaveData || {};
  const { name: leaveTypeName } = leaveData.leave_type || {};

  return (
    <Card>
      <Row justify="space-between" align="middle" className="mb-3">
        <Col>
          <Row align="middle" gutter={8}>
            <Col>
              <h4 className="mb-0">{leaveTypeName}</h4>
            </Col>
          </Row>
        </Col>
        <Col>
          <EditOutlined
            className="cursor-pointer text-lg hover:text-primary"
            onClick={onEdit}
          />
        </Col>
      </Row>

      <div className="leave-details">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <div className="d-flex align-items-start">
              <h2 className="mb-2 font-weight-semibold">{noOfDays} days</h2>
            </div>
            {description && (
              <p className="text-muted mb-2">{description}</p>
            )}
          </Col>
          
          <Col span={12}>
            <Tooltip title="Maximum leaves allowed at once">
              <div className="detail-item">
                <span className="text-muted">Max Together:</span>
                <span>{max_leaves_together || 'N/A'}</span>
              </div>
            </Tooltip>
          </Col>
          
          <Col span={12}>
            <div className="detail-item">
              <span className="text-muted">Max Days Carry Forward:</span>
              <span>{max_days_carry_forward || 'N/A'}</span>
            </div>
          </Col>

          <Col span={24}>
            <div className="detail-item">
              <CalendarOutlined className="mr-2" />
              <span>
                {start_date && end_date 
                  ? `${moment(start_date).format('YYYY-MM-DD')} - ${moment(end_date).format('YYYY-MM-DD')}`
                  : 'No date range set'}
              </span>
            </div>
          </Col>

          <Col span={24}>
            <div style={{ display: 'flex', gap: '8px' }}>
              <Tag color={carry_forward ? 'green' : 'red'}>
                {carry_forward ? 'Carry Forward Allowed' : 'No Carry Forward'}
              </Tag>
              <Tag color={document_required ? 'blue' : 'orange'}>
                {document_required ? 'Document Required' : 'No Document Required'}
              </Tag>
            </div>
          </Col>
        </Row>
      </div>

      <style jsx>{`
        .leave-details {
          padding: 8px 0;
        }
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }
        .text-muted {
          color: rgba(0, 0, 0, 0.45);
        }
      `}</style>
    </Card>
  );
};

LeaveWidget.propTypes = {
  leaveData: PropTypes.shape({
    no_of_days: PropTypes.number,
    carry_forward: PropTypes.bool,
    max_days: PropTypes.number,
    max_leaves_together: PropTypes.number,
    start_date: PropTypes.string,
    end_date: PropTypes.string,
    description: PropTypes.string,
    leave_type: PropTypes.shape({
      name: PropTypes.string
    })
  }),
  onEdit: PropTypes.func
};

export default LeaveWidget;

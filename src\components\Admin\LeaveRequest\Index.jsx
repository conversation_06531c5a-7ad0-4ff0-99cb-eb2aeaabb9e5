import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Tooltip, Table, Tag, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton, Row, Col } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { LEAVE_REQUEST } from "constants/AppConstants";
import AddLeaveRequestModal from "./Modals/index";
import {
    LeaveRequestEditWithDrawerStatus,
    // deleteLeaveRequest,
    getLeaveRequest,
    updateSortFilters,
    setColumnSearch,
    LeaveRequestAddDrawerStatus,
    setNewLeaveRequestDrawer
} from "store/slices/LeaveRequest/manageLeaveRequestSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import OrganizationSelect from "components/Admin/OrganizationDropdown/OrganizationSelect";
import AddNewLeaveRequest from "./Modals/AddNewLeaveRequest";



const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(LeaveRequestAddDrawerStatus(true));
    const { 
        LeaveRequestAddDrawer, 
        LeaveRequestResult, tablePagination, 
        sorting, filter, LeaveRequestTableLoading, 
        permission,
        addNewLeaveRequestDrawer
    } = useSelector((state) => state[LEAVE_REQUEST]);
     


    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getLeaveRequest({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };


    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) {
            console.log(error);
        }

    };

    const columns = [       
        {
            title: setLocale('leave_request.Student'),
            dataIndex: "student",
            key: "student",
            fixed: 'left',
            sorter: true,
            width: 220,
            ...getColumnSearchProps('student'),
            render: (text, record) => (
                <>
                    { record.students.map((student, index) =>                         
                       <> <Link to={`/app/student_view/${student.student_enc_id}`} key={index}>
                            {student.student}
                        </Link><br/></>
                    )}                    
                </>
            ),
        },
        {
            title: setLocale('leave_request.AbsentReason'),
            dataIndex: "absent_reason",
            key: "absent_reason",
            sorter: true,
            width: 200,
            ...getColumnSearchProps('absent_reason'),
        },

        {
            title: setLocale('leave_request.ApplyDate'),
            dataIndex: 'created_at',
            key: 'created_at',
            sorter: true,
            width: 150,
            ...getColumnSearchProps('created_at'),
        },
        {
            title: setLocale('leave_request.From'),
            dataIndex: "request_date_from",
            key: "request_date_from",
            sorter: true,
            width: 150,
            ...getColumnSearchProps('request_date_from'),
        },
        {
            title: setLocale('leave_request.To'),
            dataIndex: "request_date_to",
            key: "request_date_to",
            sorter: true,
            width: 150,
            ...getColumnSearchProps('request_date_to'),
        },
        {
            title: setLocale('leave_request.guardian_comments'),
            dataIndex: "guardian_comments",
            key: "guardian_comments", 
            ellipsis: {
                showTitle: false,
            },     
            width: 250,        
            render: (text, record) => (
                <>
                    <Tooltip placement="topLeft" title={text}>
                        {text}
                    </Tooltip>
                </>
            )            
        },
        {
            title: setLocale('leave_request.approver_comments'),
            dataIndex: "approver_comment",
            key: "approver_comment",  
            ellipsis: {
                showTitle: false,
            },  
            width: 250,             
            render: (text, record) => (
                <>
                    <Tooltip placement="topLeft" title={text}>
                        {text}
                    </Tooltip>
                </>
            )        
        },
        {
            title: setLocale('leave_request.TotalLeaveDays'),
            dataIndex: "leave_days",
            key: "leave_days",
            width: 150,
            // sorter: true,
            // ...getColumnSearchProps('leave_days'),
        },
        {
            title: setLocale('CreatedBy'),
            dataIndex: "created_by",
            key: "created_by",
            width: 150,
        },
        {
            title: setLocale('leave_request.Status'),                        
            key: "status",     
            width: 150,      
            render: (text, record, index) => {
                const statusText = record.status === 0 
                    ? <Tag color="blue">{setLocale('leave_request.Pending')}</Tag>
                    : record.status === 1 
                    ? <Tag color="green">{setLocale('leave_request.Approved')}</Tag>
                    : record.status === 2 
                    ? <Tag color="red">{setLocale('leave_request.Rejected')}</Tag> 
                    : '';
                return (
                    <span>
                        {statusText}
                    </span>
                )
            },
        },
        {
            title: setLocale('Action'),
            key: "action",
            fixed: 'right',
            width: 100,
            render: (text, record, index) => (
                <Space size="middle">
                    { record.status === 0 &&
                        <span onClick={() => handleRowClick(record, index)} style={{ cursor: 'pointer'}}>
                            <EditOutlined />
                        </span>
                    }
                </Space>
            ),
        }
    ];

    const handleRowClick = (record, index) => {
        dispatch(LeaveRequestEditWithDrawerStatus(record));
    };

    const newLeaveRequestDrawer = async(status) => {
        await dispatch(setNewLeaveRequestDrawer(status));
    };

    return (
        <>
            <Row gutter={16}>
                <Col xs={24} sm={24} md={12} lg={12}>
                    <Breadcrumb className="my-2 mx-2">
                        <Breadcrumb.Item>
                            <Link to="/app/default">{setLocale('home')}</Link>
                        </Breadcrumb.Item>
                        <Breadcrumb.Item>{setLocale('leave_request.title')}</Breadcrumb.Item>
                    </Breadcrumb>
                </Col>
                <Col xs={24} sm={24} md={12} lg={12} style={{ textAlign: 'right' }}>
                    <OrganizationSelect updateSortFilters={updateSortFilters} filter={filter} sorting={sorting} tablePagination={tablePagination} getModuleData={getModuleData} />
                </Col>
            </Row>

            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        <Button className="ant-btn-round ant-btn-sm" type="primary" style={{ float: "right", margin: "5px" }} onClick={() => newLeaveRequestDrawer(true)} >
                            Add Leaves
                        </Button>
                    </section>
                    {LeaveRequestAddDrawer && <AddLeaveRequestModal />}
                    { addNewLeaveRequestDrawer && <AddNewLeaveRequest />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}                
                            scroll={{ x: 1500 }}            
                            loading={LeaveRequestTableLoading}
                            rowKey={record => record.id}
                            dataSource={LeaveRequestResult.data ?? []}
                            pagination={false}
                        // onRow={(record, index) => ({
                        //     onClick: () => handleRowClick(record, index),
                        // })} rowClassName={() => 'ant-table-row-leave'}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

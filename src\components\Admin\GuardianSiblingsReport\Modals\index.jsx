import React,{useEffect} from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { GUARDIAN_SIBLINGS_REPORT } from "constants/AppConstants";
import {
    GuardianSiblingsReportAddDrawerStatus,
    createGuardianSiblingsReport,
    getGuardianSiblingsReport,
    onCloseError
  } from "store/slices/GuardianSiblingsReport/manageGuardianSiblingsReportSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        GuardianSiblingsReportAddDrawer,
        GuardianSiblingsReportButtonAndModelLabel,
        sorting,
        filter,
        GuardianSiblingsReportErrors,
        GuardianSiblingsReportShowMessage,
        GuardianSiblingsReportButtonSpinner,
        GuardianSiblingsReportEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[GUARDIAN_SIBLINGS_REPORT]
    );
const onClose = () => {
    dispatch(GuardianSiblingsReportAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (GuardianSiblingsReportEditData && GuardianSiblingsReportEditData.id) {
    // If editing, include the id in the form values
    formValues.id = GuardianSiblingsReportEditData.id;
  }

  await dispatch(createGuardianSiblingsReport(formValues));
};

  useEffect(() => {
    if (Object.keys(GuardianSiblingsReportErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getGuardianSiblingsReport({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [GuardianSiblingsReportErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={GuardianSiblingsReportButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={GuardianSiblingsReportAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: GuardianSiblingsReportEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={GuardianSiblingsReportShowMessage && GuardianSiblingsReportErrors.name ? "error" : ""}
                help={GuardianSiblingsReportShowMessage && GuardianSiblingsReportErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={GuardianSiblingsReportButtonSpinner}
            >
              {GuardianSiblingsReportButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


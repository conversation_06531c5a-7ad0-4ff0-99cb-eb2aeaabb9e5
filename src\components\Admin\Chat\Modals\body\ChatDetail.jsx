import React, { useRef, useState, useEffect } from 'react'
import moment from 'moment';
import { useSelector, useDispatch } from "react-redux";
import { CHAT } from "constants/chat/index";
import { List, Tag, Avatar, message, Drawer, Upload, Input, Form, Button, Menu } from 'antd';
// import {
// 	SendOutlined,
// 	UploadOutlined,
// 	PaperClipOutlined, 
// 	SmileOutlined, 
// 	InboxOutlined,
// 	PlusOutlined
// } from '@ant-design/icons';
import {
	setChatDetailDrawer
} from "store/slices/Chat/manageChatSlice.js";
import AvatarStatus from 'components/shared-components/AvatarStatus/chatAvatar';
import { USER_INFORMATION } from "constants/AuthConstant";
import { env } from "configs/EnvironmentConfig"
const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION)) || null;
// const { Dragger } = Upload;

const data = [
	{
	  title: 'Ant Design Title 1',
	},
	{
	  title: 'Ant Design Title 2',
	},
	{
	  title: 'Ant Design Title 3',
	},
	{
	  title: 'Ant Design Title 4',
	},
  ];

const ChatDetail = () => {
	const dispatch = useDispatch();
	const { 
		selectedConversation,
		chatDetailDrawer
	} = useSelector((state) => state[CHAT]);

	useEffect(() => {
			
	}, []);
	const onClose = () => {
		dispatch(setChatDetailDrawer(false));
	};

	return (
		<>
		<Drawer title={"Chat Detail"}  closable={true}  onClose={onClose}  open={chatDetailDrawer}  >											

			<List itemLayout="horizontal" 
				header={<div>{'Chat Participants'}</div>}
				size='small'
				dataSource={selectedConversation.participants}
				renderItem={(item) => (
				<List.Item>
					<List.Item.Meta
						avatar={
							<AvatarStatus name={item.name}  
								src={item?.profile_picture ? env.FILE_ENDPOINT_URL + item?.profile_picture : "/img/avatars/user.png"}
							/>
						}				
					/>
				
					<div>
						{ item.is_admin && <Tag color="success">{"Admin"}</Tag> }
					</div>
				</List.Item>
				)}
			/>
					
		</Drawer>
		</>
	)
}

export default ChatDetail

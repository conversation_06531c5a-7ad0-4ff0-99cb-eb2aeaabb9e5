import { Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { DESIGNATION } from 'constants/AppConstants';
import { useDispatch, useSelector } from 'react-redux';
import { getDesignation } from 'store/slices/Designation/manageDesignationSlice';

export const Designation = ({ value, onChange }) => {
  const { Option } = Select;
  const dispatch = useDispatch();
  const { DesignationResult, DesignationTableLoading } = useSelector((state) => state.Designation);
  const [dataFetched, setDataFetched] = useState(false);

  useEffect(() => {
    // Only fetch data if it hasn't been fetched yet and result is empty
    if (!dataFetched && (!DesignationResult || !DesignationResult.data)) {
      dispatch(getDesignation());
      setDataFetched(true);
    }
  }, [dispatch, DesignationResult, dataFetched]);

  if (DesignationResult.errors && DesignationResult.errors.length > 0) {
    return <div style={{ color: 'red' }}>Error loading designations</div>;
  }

  return (
    <Select
      placeholder="Select Designation"
      style={{ minWidth: 120, width: '100%' }}
      value={value}
      onChange={onChange}
      loading={DesignationTableLoading}
    >
      {DesignationResult?.data?.map(designation => (
        <Option 
          key={designation.id || designation.value} 
          value={designation.id || designation.value}
        >
          {designation.name || designation.label}
        </Option>
      ))}
    </Select>
  );
};

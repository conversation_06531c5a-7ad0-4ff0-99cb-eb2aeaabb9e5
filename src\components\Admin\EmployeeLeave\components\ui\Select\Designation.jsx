import { Select } from 'antd';
import React, { useEffect } from 'react';
import { DESIGNATION } from 'constants/AppConstants';
import { useDispatch, useSelector } from 'react-redux';
import { getDesignation } from 'store/slices/Designation/manageDesignationSlice';

export const Designation = ({ value, onChange }) => {
  const { Option } = Select;
  const dispatch = useDispatch();
  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(getDesignation({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
  };
  const { DesignationAddDrawer, DesignationErrors, DesignationResult, tablePagination, sorting, filter, DesignationTableLoading, permission } = useSelector(
    (state) => state[DESIGNATION]
  );
  useEffect(() => {
    getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
  }, []);

  if (DesignationErrors.length > 0) {
    return <div style={{ color: 'red' }}>Error loading designations</div>;
  }

  return (
    <Select
      placeholder="Select Designation"
      style={{ minWidth: 120, width: '100%' }}
      value={value}
      onChange={onChange}
      loading={DesignationTableLoading}
    >
      {DesignationResult?.data?.map(designation => (
        <Option 
          key={designation.id || designation.value} 
          value={designation.id || designation.value}
        >
          {designation.name || designation.label}
        </Option>
      ))}
    </Select>
  );
};

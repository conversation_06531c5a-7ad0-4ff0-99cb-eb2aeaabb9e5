import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { Empty, Skeleton, Row, Col, Button, Select, Collapse, Tag, Modal } from 'antd';
import { EyeOutlined, ExclamationCircleOutlined, PlusOutlined, EditFilled, DeleteOutlined } from '@ant-design/icons';
import {
    setOpenModal,
    getAllAssignments,
    changeAssignmentStatus,
    setEditData,
    getAllAssignmentTypes,
    setAssigneeOpenModal,
    getSingleAssignment,
    deleteAssignment
} from "store/slices/Assignments/assignmentSlice"
import { getAllTopics } from "store/slices/Topics/topicSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import { MANAGE_CLASS } from 'constants/AppConstants'
import AddAssignmentModal from "./Modals/AddAssignmentModal"
import AssignmentsDetails from './AssignmentsDetails'
import IntlMessage from "components/util-components/IntlMessage"
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Panel } = Collapse;
const { confirm } = Modal;

function Assignments() {
    const dispatch = useDispatch();
    const [selectedValue, setSelectedValue] = useState('Actions');
    const { showMessage, assignments, loading, openModal } = useSelector((state) => state[ASSIGNMENT_SLICE]);
    const { ClassCourseEditData, ViewManageClassData, permission, userRoles, teacherData, } = useSelector((state) => state[MANAGE_CLASS]);
    const [canEdit, setCanEdit] = useState(true);

    useEffect(() => {
        dispatch(getAllAssignments({ class_course_id: ClassCourseEditData?.id }))
    }, [])

    useEffect(() => {
        if (userRoles && teacherData?.to_date) {
            const today = new Date();
            const toDate = new Date(teacherData.to_date);
            const fromDate = new Date(teacherData.from_date);

            // If toDate is before today, editing is not allowed
            if (toDate < today || fromDate > today) {
                setCanEdit(false);
            } else {
                setCanEdit(true);
            }
        }
    }, [teacherData]);

    useEffect(() => {
        const values = {
            course_id: ClassCourseEditData.course_id,
            organization_grade_level_id: ViewManageClassData.organization_grade_level_id
        }
        dispatch(getAllAssignmentTypes({ class_course_id: ClassCourseEditData?.id }))
        dispatch(getAllTopics(values))
    }, [])

    const onChange = (key) => {
        // console.log(key);
    };
    const handleExtraClick = (assignment, e) => {
        // Prevent the panel from collapsing
        e.stopPropagation();
        dispatch(setEditData(assignment))
    };
    const setStopPropagation = (assignment, e) => {
        // Prevent the panel from collapsing
        e.stopPropagation();
    };

    const handleSelectChange = async (assignmentId, value) => {
        confirm({
            icon: <ExclamationCircleOutlined />,
            content: setLocale('assignment.are-you-sure-to-change-status'),
            async onOk() {
                await dispatch(changeAssignmentStatus({ id: assignmentId, assignment_status: value }))
                if (!showMessage) {
                    await dispatch(getAllAssignments({ class_course_id: ClassCourseEditData?.id }))
                }
            },
            onCancel() {
                setSelectedValue('Actions')
            },
            zIndex: 1100,
        });
    }
    const assignmentAssignees = async (assignment) => {
        await dispatch(setAssigneeOpenModal())
        await dispatch(getSingleAssignment(assignment.id))
    }
    const handleDelete = async (assignment, e) => {
        confirm({
            icon: <ExclamationCircleOutlined />,
            content: setLocale('assignment.are-you-sure-to-delete'),
            async onOk() {
                await dispatch(deleteAssignment({ id: assignment.id }))
                await dispatch(getAllAssignments({ class_course_id: ClassCourseEditData?.id }))

            },
            onCancel() {

            },
            zIndex: 1100,
        });
    }
    return (
        <>
            {canEdit && permission.includes('Add Class Assignment') &&
                <Row gutter={4} className='assignment'>
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                        {openModal && <AddAssignmentModal />}

                        <div className='assign-task d-flex' onClick={() => dispatch(setOpenModal())}>
                            <Button type="primary"><PlusOutlined /> {setLocale('assignment.new')}</Button>
                        </div>
                    </Col>
                </Row>}
            <Row gutter={4} className='assignment mt-2'>
                {loading ?
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                        <Skeleton active />
                    </Col>
                    :
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                        {(assignments && assignments.length) ?
                            <Collapse defaultActiveKey={[assignments[0]?.id]} onChange={onChange}>
                                {assignments.map(item => (
                                    <Panel
                                        header={
                                            <div className='d-flex'>
                                                {/* <FileTextFilled style={{ fontSize: '20px', marginRight:'5px' }} />  */}
                                                <Tag className='text-center tag'
                                                    color={item.assignment_status === 'pending'
                                                        ? 'gold'
                                                        : item.assignment_status === 'scheduled'
                                                            ? 'blue'
                                                            : item.assignment_status === 'assigned'
                                                                ? 'green'
                                                                : 'red'
                                                    }>
                                                    {item.assignment_status.toUpperCase()}
                                                    {item.assignment_status === 'scheduled' && (
                                                        <React.Fragment>
                                                            <br />
                                                            scheduled on:  {item.schedule_date}
                                                        </React.Fragment>
                                                    )}
                                                </Tag>
                                                {item.title}
                                            </div>}
                                        key={item.id}
                                        extra={
                                            <div >
                                                Due Date: {item.due_date}
                                                {permission.includes('Assignment Status')
                                                    // && (item.assignment_status === 'pending' || item.assignment_status === 'scheduled') 
                                                    ? (
                                                        <>
                                                            <Select style={{ width: 100, marginLeft: '5px' }}
                                                                value={selectedValue}
                                                                onClick={(e) => setStopPropagation(item, e)}
                                                                onChange={(value) => handleSelectChange(item.id, value)}>
                                                                <Select.Option value="cancelled">{setLocale('assignment.cancelled')}</Select.Option>
                                                                <Select.Option value="assigned">{setLocale('assignment.assigned')}</Select.Option>
                                                            </Select>

                                                            {canEdit && permission.includes('Update Class Assignment') ? <Button onClick={(e) => handleExtraClick(item, e)} className='ml-2 rounded-0' type="primary"><EditFilled /> {setLocale('assignment.edit')}</Button> : null}
                                                            <Button onClick={(e) => assignmentAssignees(item)} className='ml-2 rounded-0' type="primary"><EyeOutlined /> {setLocale('View')}</Button>
                                                            {canEdit && permission.includes('Delete Class Assignment') && <Button onClick={(e) => handleDelete(item, e)} className='ml-2 rounded-0' type="primary"><DeleteOutlined /> {setLocale('assignment.delete')}</Button>}
                                                        </>
                                                    ) : null}

                                            </div>
                                        }>
                                        <AssignmentsDetails data={item} />
                                    </Panel>
                                ))}
                            </Collapse>
                            : <Empty />}
                    </Col>
                }
            </Row>
        </>
    )
}

export default Assignments;

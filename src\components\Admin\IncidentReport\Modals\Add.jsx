import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Drawer, Form, Input, Row, Space, Steps, message } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { INCIDENT_REPORT } from "constants/AppConstants";
import {
  IncidentReportAddDrawerStatus,
  setIncidentFormStatus,
  setIncidentParticipantStatus,
  setIncidentFormData,
  setIncidentParticipantFormData,
  setIncidentParticipantActionFormStatus,
  viewIncidentReport,
  setStateClear
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import {
  ArrowLeftOutlined,
  LoadingOutlined, SmileOutlined, SolutionOutlined, UserOutlined
} from "@ant-design/icons";
import IncidentForm from './IncidentForm';
import IncidentParticipantForm from './IncidentParticipantForm';
import { Link, useNavigate, useParams } from "react-router-dom";
import IntlMessage from "components/util-components/IntlMessage";
import IncidentActionForm from './IncidentActionForm';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Step } = Steps;
const { TextArea } = Input;

const Index = () => {
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const params = useParams();
  const formRef = useRef(null); // Create a ref to access the form
  const {
    IncidentFormData,
    IncidentParticipantFormData,
    IncidentParticipantActionFormStatus,
    IncidentActionFormData,
    IncidentReportAddDrawer,
    IncidentReportButtonAndModelLabel,
    IncidentReportEditData,
    IncidentFormStatus,
    IncidentParticipantStatus
  } = useSelector(
    (state) => state[INCIDENT_REPORT]
  );

  const fetchData = async () => {
    if (params?.id) {
      await dispatch(viewIncidentReport(params?.id)).then(() => {
        // setIncidentParticipantDisabled(false);
        // setIncidentActionDisabled(false);
      });
    }
    setLoading(false);
  }

  useEffect(() => {
    fetchData();
  }, []);

  const [current, setCurrent] = useState(0);
  const onChange = (value) => {
    setCurrent(value);
  };


  const handleIncidentParticipantFormSubmit = (formValues) => {
    dispatch(setIncidentParticipantFormData(formValues));
    // setFormSubmit(0)
  }

  const handleIncidentActionFormSubmit = (formValues) => {
    dispatch(setIncidentParticipantFormData(formValues));
    // setFormSubmit(0)
  }

  const handleIncidentFormSubmit = async (formValues) => {
    await dispatch(setIncidentFormData(formValues));
    // setFormSubmit(0)
  }


  const steps = [
    {
      title: 'Incident',
      content: IncidentFormStatus && <IncidentForm submitForm={handleIncidentFormSubmit} />,
      disabled: true,
    },
    {
      title: 'Participants',
      content: IncidentParticipantStatus && <IncidentParticipantForm submitForm={handleIncidentParticipantFormSubmit} />,
      disabled: true,
    },
    {
      title: 'Action',
      content: IncidentParticipantActionFormStatus && <IncidentActionForm submitForm={handleIncidentActionFormSubmit} />,
      disabled: true,
    },

  ];

  const next = () => {
    if (params?.id) {
      dispatch(setIncidentFormStatus(true));
      dispatch(setIncidentParticipantStatus(true));
      dispatch(setIncidentParticipantActionFormStatus(true));
    }
    setCurrent(current + 1);
  };

  const prev = () => {
    setCurrent(current - 1);
  };

  const contentStyle = {
    textAlign: 'center',
    marginTop: 16,
  };

  const navigateBack = () => {
    navigate('../../app/incident-report');
    dispatch(setStateClear());
  }

  return (
    <>
      <Breadcrumb className="my-2 mx-2">
        <Breadcrumb.Item>
          <Link to="/app/default">{setLocale('home')}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{setLocale('Incident Report')}</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <h5>
            <span style={{ cursor: "pointer", float: "left" }}
              onClick={navigateBack}
            >
              <ArrowLeftOutlined /> Back
            </span>{" "}
          </h5>
        }
        loading={loading}
        className="profile-card"
      >
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: "center" }}>
            <h1>Report An Incident</h1>
            <p>Help us maintain a safe and secure learning environment by providing details <br></br> of any incidents that occur on campus. </p>
          </Col>
          <Col span={24}>
            <Steps
              current={current}
              onChange={onChange} labelPlacement="vertical">
              {steps.map(item => (
                <Step key={item.title} title={item.title} icon={item.icon} disabled={item.disabled} />
              ))}
            </Steps>
            <div style={contentStyle}>{steps[current].content}</div>
            <div
              style={{
                marginTop: 24,
                textAlign: "center"
              }}
            >
              {current > 0 && (
                <Button
                  style={{
                    margin: '0 8px',
                  }}
                  onClick={() => prev()}
                >
                  Previous
                </Button>
              )}
              {current < steps.length - 1 && params?.id && (
                <Button type="primary" onClick={() => next()}>
                  Next
                </Button>
              )}
              {/* {!params?.id && (
                <Button type="primary" onClick={() => next()}>
                  Save
                </Button>
              )} */}
            </div>
          </Col>
        </Row>
      </Card>
    </>
  );
};
export default Index;

import React,{useEffect, useState} from 'react';
import { Button, Col, Drawer, Form, Input, Row, Space, Switch } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { LEGAL_STATUS } from "constants/AppConstants";
import {
    LegalStatusAddDrawerStatus,
    createLegalStatus,
    getLegalStatus,
    onCloseError
  } from "store/slices/LegalStatus/manageLegalStatusSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        LegalStatusAddDrawer,
        LegalStatusButtonAndModelLabel,
        sorting,
        filter,
        LegalStatusErrors,
        LegalStatusShowMessage,
        LegalStatusButtonSpinner,
        LegalStatusEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[LEGAL_STATUS]
    );
const [switchValue, setSwitchValue] = useState('checked');
const onClose = () => {
    dispatch(LegalStatusAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (LegalStatusEditData && LegalStatusEditData.id) {
    // If editing, include the id in the form values
    formValues.id = LegalStatusEditData.id;
    formValues.is_default = LegalStatusEditData.is_default == 1 ? true : false;
  }

  await dispatch(createLegalStatus(formValues));
};

  useEffect(() => {
    if (Object.keys(LegalStatusErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getLegalStatus({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [LegalStatusErrors]);

  useEffect(() => {
    LegalStatusEditData.is_default === 1 ? setSwitchValue('checked') : setSwitchValue();
  }, [dispatch]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

const getSwitchValue = (checked) => {
  setSwitchValue(checked);
};

  return (
    <>

      <Drawer
        title={LegalStatusButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={LegalStatusAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: LegalStatusEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={LegalStatusShowMessage && LegalStatusErrors.name ? "error" : ""}
                help={LegalStatusShowMessage && LegalStatusErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>
            {!(LegalStatusEditData && LegalStatusEditData.id) &&
              <Col span={12}>
                <Form.Item
                  name="is_default"
                  // valuePropName={switchValue}
                  label="Is Default"
                  rules={[
                    {
                      required: false,
                      message: "Status Error",
                    },
                  ]}
                  validateStatus={LegalStatusShowMessage && LegalStatusErrors.is_default ? "error" : ""}
                      extra={LegalStatusShowMessage && LegalStatusErrors.is_default}


                >
                  <Switch
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    onChange={getSwitchValue}
                    checked={switchValue}
                    name="is_default"
                  />
                </Form.Item>
              </Col>
            }
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={LegalStatusButtonSpinner}
            >
              {LegalStatusButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


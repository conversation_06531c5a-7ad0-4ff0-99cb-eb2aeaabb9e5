import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { INCIDENT_TYPE } from "constants/AppConstants";
import {
  IncidentTypeAddDrawerStatus,
  createIncidentType,
  getIncidentType
} from "store/slices/IncidentType/manageIncidentTypeSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { IncidentTypeAddDrawer, IncidentTypeButtonAndModelLabel, sorting, filter, IncidentTypeErrors, IncidentTypeShowMessage, IncidentTypeButtonSpinner, IncidentTypeEditData, tablePagination } = useSelector(
    (state) => state[INCIDENT_TYPE]
  );
  const onClose = () => dispatch(IncidentTypeAddDrawerStatus(false));

  const onSubmit = async (formValues) => {

    if (IncidentTypeEditData && IncidentTypeEditData.id) {
      // If editing, include the id in the form values
      formValues.id = IncidentTypeEditData.id;
    }

    await dispatch(createIncidentType(formValues))
      .then(() => {
        if (Object.keys(IncidentTypeErrors).length === 0) {
          dispatch(getIncidentType({
            page: tablePagination.current,
            perPage: tablePagination.pageSize,
            filter: filter, sorting: sorting
          }));
          // form.resetFields();
        }
      })
      .catch((error) => {
        // Handle delete error
        console.error("Error deleting module:", error);
      });
  };

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>

      <Drawer
        title={IncidentTypeButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={IncidentTypeAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            incident_type: IncidentTypeEditData?.incident_type,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="incident_type"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={IncidentTypeShowMessage && IncidentTypeErrors.incident_type ? "error" : ""}
                help={IncidentTypeShowMessage && IncidentTypeErrors.incident_type}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={IncidentTypeButtonSpinner}
            >
              {IncidentTypeButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


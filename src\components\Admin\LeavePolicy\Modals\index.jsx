import React,{useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LEAVE_POLICY } from "constants/AppConstants";
import {
    LeavePolicyAddDrawerStatus,
    createLeavePolicy,
    getLeavePolicy,
    onCloseError
  } from "store/slices/LeavePolicy/manageLeavePolicySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        LeavePolicyAddDrawer,
        LeavePolicyButtonAndModelLabel,
        sorting,
        filter,
        LeavePolicyErrors,
        LeavePolicyShowMessage,
        LeavePolicyButtonSpinner,
        LeavePolicyEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[LEAVE_POLICY]
    );
const onClose = () => {
    dispatch(LeavePolicyAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (LeavePolicyEditData && LeavePolicyEditData.id) {
    // If editing, include the id in the form values
    formValues.id = LeavePolicyEditData.id;
  }

  await dispatch(createLeavePolicy(formValues));
};

  useEffect(() => {
    if (Object.keys(LeavePolicyErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getLeavePolicy({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [LeavePolicyErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={LeavePolicyButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={LeavePolicyAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: LeavePolicyEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={LeavePolicyShowMessage && LeavePolicyErrors.name ? "error" : ""}
                help={LeavePolicyShowMessage && LeavePolicyErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={LeavePolicyButtonSpinner}
            >
              {LeavePolicyButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Upload, Modal, Button, message } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import axios from "axios";
import {
    uploadSecureFileUploadInS3, downloadSecureFileFromS3
} from "store/slices/IdentificationType/manageIdentificationTypeSlice.js";

function UploadAndDownloadSecureFile() {
    const dispatch = useDispatch();
    const [fileList, setFileList] = useState([]);
    const [uploading, setUploading] = useState(false);
    const [uploadedFilename, setUploadedFilename] = useState('68375b027d7df_original-logo-sm.png');

    const uploadProps = {
        beforeUpload: file => {
            setFileList([file]);
            return false; // prevent automatic upload
        },
        fileList,
        onRemove: () => {
            setFileList([]);
            setUploadedFilename(null);
        }
    };

    const handleUpload = async () => {
        if (fileList.length === 0) return;
        const formData = new FormData();
        formData.append('file', fileList[0]);
        setUploading(true);

        try {
            dispatch(uploadSecureFileUploadInS3(formData)).then((res) => {
                message.success(res.payload.message);
                setUploadedFilename(res.payload.filename);
            });
        } catch (error) {
            console.error(error);
            message.error('Upload failed');
        } finally {
            setUploading(false);
        }
    };

    const handleDownload = async () => {

        try {
            await dispatch(downloadSecureFileFromS3({ filename: uploadedFilename })).then((res) => {
                const response = res.payload;
                if (response.download_url) {
            // window.open(response.download_url, '_blank', 'download');

                    const link = document.createElement("a");
                    link.href = response.download_url;
                    link.setAttribute("download", response.filename || "downloaded-file");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            });
        } catch (error) {
            console.error("Download failed:", error);
            message.error("Download failed");
        }

    };

    return (
        <>
            <Modal title="Secure File Upload/Download" open={true} footer={null}>
                <Upload {...uploadProps}>
                    <Button icon={<UploadOutlined />}>Select File</Button>
                </Upload>
                <br />
                <Button
                    type="primary"
                    onClick={handleUpload}
                    disabled={fileList.length === 0}
                    loading={uploading}
                    style={{ marginTop: 16 }}
                >
                    {uploading ? 'Uploading...' : 'Upload'}
                </Button>

                <Button
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                    // disabled={!uploadedFilename}
                    style={{ marginLeft: 10 }}
                >
                    Download
                </Button>
            </Modal>
        </>
    );
}

export default UploadAndDownloadSecureFile;

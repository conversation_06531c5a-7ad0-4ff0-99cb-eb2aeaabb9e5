import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    Table,
    Popconfirm,
    Pagination,
    Button,
    Input,
    Space,
    Breadcrumb,
    Skeleton,
    Tabs,
    TabsProps,
} from "antd";
import {
    DeleteOutlined,
    EditOutlined,
    SearchOutlined,
    EyeOutlined,
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { COMMENT } from "constants/AppConstants";
import AddCommentModal from "./Modals/index";
import {
    CommentAddDrawerStatus,
    CommentEditWithDrawerStatus,
    deleteComment,
    getComment,
    updateSortFilters,
    setColumnSearch,
} from "store/slices/Comment/manageCommentSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () =>
        dispatch(CommentAddDrawerStatus({ errorStatus: 1, status: true }));
    const {
        CommentAddDrawer,
        CommentResult,
        tablePagination,
        sorting,
        filter,
        CommentTableLoading,
        permission,
    } = useSelector((state) => state[COMMENT]);

    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(
            getComment({
                page: page,
                perPage: perPage,
                filter: filterData,
                sorting: sortingData,
                type: "my_comment",
            })
        );
    };

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(
            tablePagination.current,
            tablePagination.pageSize,
            newObject,
            sorting
        );
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === "") {
            confirm();
            getModuleData(
                tablePagination.current,
                tablePagination.pageSize,
                { ...filter, [dataIndex]: value },
                sorting
            );
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
            close,
        }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) =>
                        handleOnChange(
                            dataIndex,
                            e.target.value ? e.target.value : "",
                            confirm
                        )
                    }
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: "block",
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale("search")}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex);
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale("reset")}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color:
                        filter[dataIndex] && filter[dataIndex] !== ""
                            ? "#1677ff"
                            : undefined,
                }}
            />
        ),
        onFilter: (value, record) =>
            record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex] ? (
                <Highlighter
                    highlightStyle={{
                        backgroundColor: "#ffc069",
                        padding: 0,
                    }}
                    searchWords={[filter[dataIndex]]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            ),
    });

    useEffect(() => {
        getModuleData(
            tablePagination.current,
            tablePagination.pageSize,
            filter,
            sorting
        );
    }, []);

    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };

    const handleDelete = (record) => {
        dispatch(deleteComment(record.id)).then(() => {
            getModuleData(
                tablePagination.current,
                tablePagination.pageSize,
                filter,
                sorting
            );
        });
    };
    const handleUpdate = (record) => {
        dispatch(CommentEditWithDrawerStatus({ errorStatus: 1, data: record }));
    };

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sortingTable = {
            [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sortingTable }));
            getModuleData(1, tablePagination.pageSize, filter, sortingTable);
        } catch (error) {
            console.log(error);
        }
    };

    const columns = [
        {
            title: "Teacher ID",
            dataIndex: "teacher_id",
            key: "teacher_id",
            sorter: true,
            ...getColumnSearchProps("teacher_id"),
        },
        {
            title: "Teacher Name",
            dataIndex: "teacher_name",
            key: "teacher_name",
            sorter: true,
            ...getColumnSearchProps("teacher_name"),
        },
        {
            title: "Student ID",
            dataIndex: "class_student_id",
            key: "class_student_id",
            sorter: true,
            ...getColumnSearchProps("class_student_id"),
        },
        {
            title: "Student Name",
            dataIndex: "student_name",
            key: "student_name",
            sorter: true,
            ...getColumnSearchProps("student_name"),
        },
        {
            title: "Grade",
            dataIndex: "grade",
            key: "grade",
            sorter: true,
            ...getColumnSearchProps("grade"),
        },
        {
            title: "Comment Category",
            dataIndex: "comment_category",
            key: "comment_category",
            sorter: true,
            ...getColumnSearchProps("comment_category"),
        },
        {
            title: "Comments",
            dataIndex: "comments",
            key: "comments",
            sorter: true,
            ...getColumnSearchProps("comments"),
        },
        {
            title: setLocale("operation"),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm
                            title={setLocale("sure_to_delete")}
                            onConfirm={(e) => handleDelete(record)}
                        >
                            <DeleteOutlined
                                style={{ fontSize: "15px" }}
                                className="text-danger"
                            />{" "}
                            &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined
                            style={{ fontSize: "15px", marginRight: "9px" }}
                            className="text-success"
                            onClick={(e) => handleUpdate(record)}
                        />
                    )}
                    {/* {permission.includes("View") && (
                        <Link to={`../../app/comment_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
                </>
            ),
        },
    ];
    const onChange = (key: string) => {
        console.log(key);
    };

    const items: TabsProps["items"] = [
        {
            key: "1",
            label: "All",
            children: "Content of All Comments",
        },
        {
            key: "2",
            label: "My Comment",
            children: "Content of My Comments",
        },
    ];
    return (
        <>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale("comment.add")}
                            </Button>
                        )}
                    </section>
                    {CommentAddDrawer && <AddCommentModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={CommentTableLoading}
                            rowKey={(record) => record.id}
                            dataSource={CommentResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: "16px", float: "right" }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={["10", "20", "50", "100", "1000"]}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

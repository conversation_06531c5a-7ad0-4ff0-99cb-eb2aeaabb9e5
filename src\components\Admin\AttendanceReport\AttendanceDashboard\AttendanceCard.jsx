import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { Form, Row, Col, Card } from 'antd';

// import { 
//   ArrowDownOutlined 
// } from '@ant-design/icons';
import IntlMessage from "components/util-components/IntlMessage";
// import {
//   getAttendanceDashboardReport,
//   getClassStudents
// } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import { 
//     MANAGE_CLASS, 
//     SCHOOL_YEAR 
// } from "constants/AppConstants";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AttendanceCard() {
    // const [form] = Form.useForm();
    // const dispatch = useDispatch();
    const {
        //   AttendanceReportButtonSpinner,
        attendanceDashboardReport,
        //   classStudents 
    } = useSelector((state) => state[ATTENDANCE_REPORT]);
    // const { ManageClassResult } = useSelector((state) => state[MANAGE_CLASS])  
    return (
        <>
            <Row gutter={16}>
                <Col span={24} className="d-flex justify-content-between">
                    {attendanceDashboardReport?.cardData?.map((item, index) => (
                        <Card className="p-2 mr-2 w-100 detail-attendance-card" key={index}>
                            <div className="d-flex justify-content-between mb-2">
                                <h5 className="semiHead-3 mt-2">{item?.name}</h5>
                            </div>
                            <h5 className="semiHead-1">{item?.total}</h5>
                        </Card>
                    ))}
                </Col>
            </Row>
        </>
    );
}

export default AttendanceCard;

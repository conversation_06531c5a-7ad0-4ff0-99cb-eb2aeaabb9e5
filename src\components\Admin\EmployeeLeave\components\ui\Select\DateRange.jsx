import React from "react";
import { DatePicker} from "antd";
import moment from "moment";

const { RangePicker } = DatePicker;

export const DateRange = ({ onChange }) => {
  
  const handleDateChange = (dates, dateStrings) => {
    onChange(dates, dateStrings);
  };
 
  return (
      <RangePicker
        autoFocus
        onChange={handleDateChange}
        ranges={{
          Today: [moment(), moment()],
          "This Month": [
            moment().startOf("month"),
            moment().endOf("month"),
          ],
        }}
      />

  );
};

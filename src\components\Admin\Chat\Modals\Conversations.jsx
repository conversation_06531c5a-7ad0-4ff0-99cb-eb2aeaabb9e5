import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from "react-redux";
import { Skeleton, Badge, Input } from 'antd';
import AvatarStatus from 'components/shared-components/AvatarStatus/chatAvatar';
import { COLOR_1 } from 'constants/ChartConstant';
import { SearchOutlined } from '@ant-design/icons';
import { CHAT } from "constants/chat/index";
import { env } from "configs/EnvironmentConfig"

import {
	setSelectedConversation,
	setUserConversations
} from "store/slices/Chat/manageChatSlice.js";
import Echo from 'broadcasting/laravelEcho'
import { USER_INFORMATION } from 'constants/AuthConstant';
import { humanizeDate } from "components/Helper/Index";

const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION)) || null;


const ChatMenu = () => {
	const dispatch = useDispatch();
	const {
		conversationLoader,
		userConversations,
		selectedConversation
	} = useSelector((state) => state[CHAT]);
	const [conversations, setConversations] = useState([]);

	useEffect(() => {
		const channel = Echo.private(`chat-message-read-count`);
		channel.listen('ChatMessageReadCountEvent', async (data) => {
			if (data.conversation_id !== selectedConversation?.conversation_id && data.user_id !== currentUser.id) {
				await dispatch(setUserConversations(data));
			}
			return () => {
				channel.stopListening('ChatMessageReadCountEvent');
			}
		})
	}, [])

	useEffect(() => {
		const sortedConversations = [...userConversations].sort((a, b) => new Date(b.last_message_at) - new Date(a.last_message_at));
		setConversations(sortedConversations);
	}, [userConversations]);

	const openChat = async (conversation) => {
		await dispatch(setSelectedConversation(conversation))
	}

	const searchOnChange = e => {
		const query = e.target.value;
		if (!query) {
			setConversations(userConversations);
			return;
		}
		const data = userConversations.filter(item => {
			return item.name.toLowerCase().includes(query)
		})
		setConversations(data)
	}

	return (
		<div className="chat-menu chat-menu-parent">
			<div className="chat-menu-toolbar">
				<Input placeholder="Search" onChange={searchOnChange}
					prefix={
						<SearchOutlined className="font-size-sm mr-2" />
					}
				/>
			</div>
			<div className="chat-menu-list">
				<Skeleton loading={conversationLoader} active avatar />
				<Skeleton loading={conversationLoader} active avatar />
				<Skeleton loading={conversationLoader} active avatar />
				{!conversationLoader &&
					conversations.map((item, i) => (
						<div key={`chat-item-${item.conversation_id}`} onClick={() => openChat(item)} className={`chat-menu-list-item`} >							
							<AvatarStatus src={item?.profile_picture ? env.FILE_ENDPOINT_URL + item?.profile_picture : "/img/avatars/user.png"}
								name={item.name} subTitle={item.last_message} subString={true} item={item} />							
							<div className="text-right">
								<div className="chat-menu-list-item-time">{humanizeDate(item.last_message_at)}</div>
								{item.unread_messages > 0 && <Badge count={item.unread_messages} style={{ backgroundColor: COLOR_1 }} />}
							</div>
						</div>
					))
				}
			</div>
		</div>
	)
}

export default ChatMenu

import React,{useEffect} from 'react';
import { Button, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CHARGES_SUMMARY } from "constants/AppConstants";
import {
    ChargesSummaryAddDrawerStatus,
    createChargesSummary,
    getChargesSummary,
    onCloseError
  } from "store/slices/ChargesSummary/manageChargesSummarySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        ChargesSummaryAddDrawer,
        ChargesSummaryButtonAndModelLabel,
        sorting,
        filter,
        ChargesSummaryErrors,
        ChargesSummaryShowMessage,
        ChargesSummaryButtonSpinner,
        ChargesSummaryEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[CHARGES_SUMMARY]
    );
const onClose = () => {
    dispatch(ChargesSummaryAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (ChargesSummaryEditData && ChargesSummaryEditData.id) {
    // If editing, include the id in the form values
    formValues.id = ChargesSummaryEditData.id;
  }

  await dispatch(createChargesSummary(formValues));
};

  useEffect(() => {
    if (Object.keys(ChargesSummaryErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getChargesSummary({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ChargesSummaryErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={ChargesSummaryButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ChargesSummaryAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: ChargesSummaryEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={ChargesSummaryShowMessage && ChargesSummaryErrors.name ? "error" : ""}
                help={ChargesSummaryShowMessage && ChargesSummaryErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={ChargesSummaryButtonSpinner}
            >
              {ChargesSummaryButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


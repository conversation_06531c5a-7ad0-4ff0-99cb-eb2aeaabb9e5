import React, { useEffect } from 'react';
import { Button, Col, Drawer, Form, Input, Row, Select, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { GRADE_LEVEL } from "constants/AppConstants";
import {
  GradeLevelAddDrawerStatus,
  createGradeLevel,
  getGradeLevel,
  onCloseError
} from "store/slices/GradeLevel/manageGradeLevelSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { DrawerStatus, sorting, filter, GradeLevelAddDrawer, GradeLevelButtonAndModelLabel, GradeLevelErrors, GradeLevelShowMessage, GradeLevelButtonSpinner, GradeLevelEditData, tablePagination } = useSelector(
    (state) => state[GRADE_LEVEL]
  );
  const onClose = () => {
    dispatch(GradeLevelAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {

    if (GradeLevelEditData && GradeLevelEditData.id) {
      // If editing, include the id in the form values
      formValues.id = GradeLevelEditData.id;
    }

    await dispatch(createGradeLevel(formValues))
  };
  useEffect(() => {
    if (Object.keys(GradeLevelErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getGradeLevel({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [GradeLevelErrors]);
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };
  const typeOption = [
    {
      value: 'term',
      label: 'Term',
    },
    {
      value: 'semester',
      label: 'Semester',
    }
  ];
  return (
    <>

      <Drawer
        title={GradeLevelButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={GradeLevelAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            grade_level: GradeLevelEditData?.grade_level,
            type: GradeLevelEditData?.type ? GradeLevelEditData?.type : null,

          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="grade_level"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={GradeLevelShowMessage && GradeLevelErrors.grade_level ? "error" : ""}
                extra={GradeLevelShowMessage && GradeLevelErrors.grade_level}
              >
                <Input />
              </Form.Item>
            </Col>

            <Col span={12}>

              <Form.Item
                name="type"
                label={setLocale('grade_level.type')}
                rules={[
                  {
                    required: true,
                    message: setLocale('grade_level.type_error'),
                  },
                ]}
                validateStatus={GradeLevelShowMessage && GradeLevelErrors.type ? "error" : ""}
                extra={GradeLevelShowMessage && GradeLevelErrors.type}>
                <Select
                  className='rounded-0'
                  showSearch
                  optionLabelProp="label"
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) => (
                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                  )}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                  }
                  options={typeOption ?? []}
                />
              </Form.Item>
            </Col>
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={GradeLevelButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


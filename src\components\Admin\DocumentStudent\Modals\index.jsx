import React, { useEffect, useState, useRef } from "react";
import {
  Upload,
  But<PERSON>,
  Col,
  Drawer,
  Form,
  Input,
  Row,
  Space,
  Select,
  message,
  Table,
  Popconfirm,
} from "antd";
import Highlighter from "react-highlight-words";
import {
  DeleteOutlined, DownloadOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { DOCUMENT_STUDENT, DOCUMENT_TYPE } from "constants/AppConstants";
import { STUDENT } from "constants/student/index";
import {
  DocumentStudentAddDrawerStatus,
  createDocumentStudent,
  getDocumentStudent,
  onCloseError,
  getStudentDocuments,
  setColumnSearch,
  deleteDocumentStudent,
} from "store/slices/DocumentStudent/manageDocumentStudentSlice.js";
import { getDocumentType } from "store/slices/DocumentType/manageDocumentTypeSlice.js";
import {
  getStudents,
  deleteStudentDocument,
} from "store/slices/Student/manageStudentSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { env } from "configs/EnvironmentConfig"

import { USER_INFORMATION } from "constants/AuthConstant";
import { isJSON } from "components/composeable";
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;
const { Option } = Select;
const allowedFileTypes = ["application/pdf", "image/jpeg", "image/png", "image/jpg",];

const Index = () => {
  const [form] = Form.useForm();
  const searchInput = useRef(null);
  const dispatch = useDispatch();
  const {
    DocumentStudentAddDrawer,
    DocumentStudentButtonAndModelLabel,
    sorting,
    filter,
    DocumentStudentErrors,
    DocumentStudentShowMessage,
    DocumentStudentButtonSpinner,
    DocumentStudentEditData,
    getStudentDocumentsData,
    getStudentDocumentsLoader,
    tablePagination,
    DrawerStatus,
    permission,
  } = useSelector((state) => state[DOCUMENT_STUDENT]);
  const {
    // StudentAddDrawer,
    StudentResult,
  } = useSelector((state) => state[STUDENT]);
  const { DocumentTypeResult } = useSelector((state) => state[DOCUMENT_TYPE]);
  const [fileLists, setFileLists] = useState([[]]);
  const [fileError, setFileError] = useState(null);
  const [selectedDocumentTypes, setSelectedDocumentTypes] = useState(
    [DocumentStudentEditData?.document_type_id] || []
  );
  const [uploadType, setUploadType] = useState("m");

  const onClose = () => {
    dispatch(DocumentStudentAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  };

  const onSubmit = async (formValues) => {
    const formData = new FormData();

    // If editing, include the id in the form values
    if (DocumentStudentEditData && DocumentStudentEditData.id) {
      formValues.document_student_id = DocumentStudentEditData.id;
    }

    /** Save student document type file */
    selectedDocumentTypes.forEach((docTypeId, index) => {
      const fileList = fileLists[index];
      if (fileList && fileList.length > 0) {
        formData.append(`document_file_${index}`, fileList[0]);
      } else if (DocumentStudentEditData && DocumentStudentEditData.file_path) {
        formData.append(`document_file_${index}`, DocumentStudentEditData.file_path);
      }
    });

    Object.entries(formValues).forEach(([key, value]) => {
      formData.append(key, value || "");
    });

    await dispatch(createDocumentStudent(formData));
    form.resetFields();
    setFileLists([[]]); // Reset file lists after submission
  };

  useEffect(() => {
    if (Object.keys(DocumentStudentErrors).length === 0 && DrawerStatus === 0) {
      dispatch(
        getDocumentStudent({
          page: tablePagination.current,
          perPage: tablePagination.pageSize,
          filter: filter,
          sorting: sorting,
        })
      );
    }
    dispatch(getDocumentType());
    dispatch(getStudents({ filter: isJSON({ organization_id: selectedOrganization }), perPage: 10000 }));
    dispatch(getStudentDocuments({ student_id: DocumentStudentEditData?.student_id }));
  }, [DocumentStudentErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const handleDocumentTypeChange = (selectedItems) => {
    setSelectedDocumentTypes(selectedItems);
    setFileLists(Array(selectedItems.length).fill([]));
  };

  const handleTypeChange = (value) => {
    setUploadType(value);
    if (value === "s") {
      setFileLists([[]]); // Reset file list for single upload
    } else {
      setFileLists(Array(selectedDocumentTypes.length).fill([])); // Reset file lists for multiple upload
    }
  };
  const handleStudentChange = (value) => {
    if (value !== undefined) {
      dispatch(getStudentDocuments({ student_id: value }));
    }
  }
  const handleRemove = (fileIndex) => (file) => {
    setFileLists((prevLists) => {
      const newLists = [...prevLists];
      newLists[fileIndex] = newLists[fileIndex].filter(
        (item) => item.uid !== file.uid
      );
      return newLists;
    });
  };

  const beforeUpload = (file) => {
    const isPdf = allowedFileTypes.includes(file.type);
    if (!isPdf) {
      setFileError("Only PDF files are allowed!");
      message.error("Only PDF files are allowed!");
    }

    const isLt6M = file.size / 1024 / 1024 < 6;
    if (!isLt6M) {
      setFileError("File must be smaller than 6MB!");
      message.error("File must be smaller than 6MB!");
    }

    return isPdf && isLt6M;
  };

  const handleCustomRequest = (fileIndex) => (info) => {
    if (beforeUpload(info.file)) {
      setFileLists((prevLists) => {
        const newLists = [...prevLists];
        newLists[fileIndex] = [info.file];
        return newLists;
      });
      setFileError(null); // Clear previous error when a new file is selected
      info.onSuccess("ok");
    } else {
      info.onError(new Error("File upload error"));
    }
  };
  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getDocumentStudent({
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
      })
    );
  };
  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, tablePagination.pageSize, filter, sorting);
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      newObject,
      sorting
    );
  };

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === "") {
      confirm();
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        { ...filter, [dataIndex]: value },
        sorting
      );
    }
  };

  const handleDelete = (record, data) => {
    dispatch(deleteDocumentStudent(record.id)).then(() => {
      dispatch(getStudentDocuments({ student_id: data?.student_id }))
    });
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });
  const columns = [
    {
      title: "Document Type",
      dataIndex: "document_type",
      key: "document_type",
      sorter: true,
      ...getColumnSearchProps("document_type"),
    }, {
      title: "Comments",
      dataIndex: "comments",
      key: "comments",
      sorter: true,
      ...getColumnSearchProps("comments"),
    }, {
      title: "Operation",
      key: "action",
      render: (data, record) => (
        <>
          {permission.includes("Delete") && (
            <Popconfirm
              title={setLocale("sure_to_delete")}
              onConfirm={(e) => handleDelete(record, data)}
            >
              <DeleteOutlined
                style={{ fontSize: "15px" }}
                className="text-danger"
              />{" "}
              &nbsp;
            </Popconfirm>
          )}
          <Button
            type="link"
            href={env.FILE_ENDPOINT_URL + record?.file_path}
            target="_blank"
            rel="noopener noreferrer"
            icon={<DownloadOutlined />}
          />
        </>
      ),

    },
  ];

  const options = [
    ...userOrganizations?.map(organization => ({
      value: organization.id,
      label: organization.org_name,
    })),
  ];

  const handleOrganizationChange = (value) => {
    dispatch(getStudents({ filter: isJSON({ organization_id: value }), perPage: 10000 }));
  }
  return (
    <>
      <Drawer
        title={DocumentStudentButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={DocumentStudentAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            student_id: DocumentStudentEditData?.student_id,
            organization_id: DocumentStudentEditData?.organization_id ? DocumentStudentEditData?.organization_id : filter.organization_id ? filter.organization_id : selectedOrganization
            // document_type_id: DocumentStudentEditData?.document_type_id,
            // comments: DocumentStudentEditData?.comments,
            // upload_type: "m",
            // file_path: DocumentStudentEditData?.file_path,
          }}
        >
          <Row gutter={16}>

            {userOrganizations.length > 1 &&
              <>
                <Col xs={24} sm={24} md={12} lg={12}>
                  <Form.Item
                    name="organization_id"
                    label={setLocale('users.organization_id')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('users.organization_id_error'),
                      },
                    ]}
                    validateStatus={DocumentStudentShowMessage && DocumentStudentErrors.organization_id ? "error" : ""}
                    extra={DocumentStudentShowMessage && DocumentStudentErrors.organization_id} >
                    <Select
                      placeholder={setLocale('organizations.label')}
                      optionLabelProp="label"
                      onChange={handleOrganizationChange}
                      disabled={Object.keys(DocumentStudentEditData).length === 0 ? false : true}
                      defaultValue={filter.organization_id ? filter.organization_id : selectedOrganization}
                      options={options ?? []}
                    />
                  </Form.Item>
                </Col>
              </>
            }

            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
              <Form.Item
                name="student_id"
                label="Student"
                rules={[
                  {
                    required: true,
                    message: "Student is required",
                  },
                ]}
                validateStatus={
                  DocumentStudentShowMessage && DocumentStudentErrors.student_id
                    ? "error"
                    : ""
                }
                extra={
                  DocumentStudentShowMessage && DocumentStudentErrors.student_id
                }
              >
                <Select className="rounded-0"
                  showSearch
                  optionLabelProp="label"
                  disabled={!!DocumentStudentEditData?.student_id}
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  onChange={handleStudentChange}>
                  {StudentResult
                    ? StudentResult.map((Student, index) => (
                      <Option
                        value={Student.id}
                        key={index}
                        label={Student.name}
                      >
                        {Student.name + ' (' + Student.student_id + ')'}
                      </Option>
                    ))
                    : null}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
              <Form.Item
                name="upload_type"
                label="Upload Documents"
                rules={[
                  {
                    required: true,
                    message: setLocale("select upload Type"),
                  },
                ]}
                validateStatus={
                  DocumentStudentShowMessage &&
                    DocumentStudentErrors.document_type_id
                    ? "error"
                    : ""
                }
                extra={
                  DocumentStudentShowMessage &&
                  DocumentStudentErrors.document_type_id
                }
              >
                <Select
                  allowClear
                  showSearch
                  className="rounded-0"
                  optionLabelProp="label"
                  onChange={handleTypeChange}
                  filterOption={(inputValue, option) =>
                    option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                  }
                  options={[
                    {
                      value: "m",
                      label: "Multiple files",
                    },
                    {
                      value: "s",
                      label: "Single file",
                    },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
              <Form.Item
                name="document_type_id"
                label={setLocale("document.document_type")}
                rules={[
                  {
                    required: true,
                    message: setLocale("document.document_type_error"),
                  },
                ]}
                validateStatus={
                  DocumentStudentShowMessage &&
                    DocumentStudentErrors.document_type_id
                    ? "error"
                    : ""
                }
                extra={
                  DocumentStudentShowMessage &&
                  DocumentStudentErrors.document_type_id
                }
              >
                <Select
                  mode="multiple"
                  allowClear
                  className="rounded-0"
                  optionLabelProp="label"
                  onChange={handleDocumentTypeChange}
                  maxTagCount={1}
                >
                  {DocumentTypeResult.data
                    ? DocumentTypeResult.data.map((DocumentType, index) => (
                      <Option
                        value={DocumentType.id}
                        key={index}
                        label={DocumentType.document_type}
                      >
                        {DocumentType.document_type}
                      </Option>
                    ))
                    : null}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
              <Form.Item name="comments" label={setLocale("document.comments")}>
                <Input />
              </Form.Item>
            </Col>

            {uploadType === "s" ? (
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Form.Item
                  name="single_document_file"
                  label="Document file"
                  rules={[
                    {
                      required: true,
                      message: setLocale("document.attach_file_error"),
                    },
                  ]}
                  validateStatus={fileError ? "error" : ""}
                  help={fileError}
                >
                  <Upload
                    fileList={fileLists[0]}
                    beforeUpload={beforeUpload}
                    customRequest={handleCustomRequest(0)}
                    showUploadList={{ showRemoveIcon: true }}
                    onRemove={handleRemove(0)}
                  >
                    <Button>{setLocale("document.file")}</Button>
                  </Upload>
                </Form.Item>
              </Col>
            ) : (
              selectedDocumentTypes?.map((docTypeId, index) => {
                const docTypeName = DocumentTypeResult?.data?.find(
                  (docType) => docType.id === docTypeId
                )?.document_type;
                return docTypeName ? (
                  <Col xs={24} sm={24} md={12} lg={12} xl={12} key={index}>
                    <Form.Item
                      name={`document_file_${index}`}
                      label={`${docTypeName} File`}
                      rules={[
                        {
                          required: !(DocumentStudentEditData && DocumentStudentEditData.id),
                          message: setLocale("document.attach_file_error"),
                        },
                      ]}
                      validateStatus={fileError ? "error" : ""}
                      help={fileError}
                    >
                      <Upload
                        fileList={fileLists[index]}
                        beforeUpload={beforeUpload}
                        customRequest={handleCustomRequest(index)}
                        showUploadList={{ showRemoveIcon: true }}
                        onRemove={handleRemove(index)}
                      >
                        <Button>{setLocale("document.file")}</Button>
                      </Upload>
                    </Form.Item>
                  </Col>
                ) : null;
              })
            )}

            {/* {DocumentStudentEditData?.file_path && (
              <Col
                xs={24}
                sm={24}
                md={6}
                lg={6}
                xl={6}
                style={{ marginTop: "30px" }}
              >
                Previous File
                <Button
                  type="link"
                  href={env.FILE_ENDPOINT_URL + DocumentStudentEditData?.file_path}
                  target="_blank"
                  rel="noopener noreferrer"
                  icon={<DownloadOutlined />}
                />
              </Col>
            )} */}
          </Row>

          <Space className="mb-3">
            <Button
              type="primary"
              htmlType="submit"
              loading={DocumentStudentButtonSpinner}
            >
              {DocumentStudentButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale("Cancel")}</Button>
          </Space>
        </Form>
        {getStudentDocumentsData?.length > 0 && (
          <Table
            columns={columns}
            loading={getStudentDocumentsLoader}
            rowKey={(record) => record.id}
            dataSource={getStudentDocumentsData ?? []}
            pagination={false}
          />
        )}
      </Drawer>
    </>
  );
};

export default Index;

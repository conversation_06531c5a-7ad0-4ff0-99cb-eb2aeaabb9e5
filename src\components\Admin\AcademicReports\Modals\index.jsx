import React,{useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ACADEMIC_REPORTS } from "constants/AppConstants";
import {
    AcademicReportsAddDrawerStatus,
    createAcademicReports,
    getAcademicReports,
    onCloseError
  } from "store/slices/AcademicReports/manageAcademicReportsSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        AcademicReportsAddDrawer,
        AcademicReportsButtonAndModelLabel,
        sorting,
        filter,
        AcademicReportsErrors,
        AcademicReportsShowMessage,
        AcademicReportsButtonSpinner,
        AcademicReportsEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[ACADEMIC_REPORTS]
    );
const onClose = () => {
    dispatch(AcademicReportsAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (AcademicReportsEditData && AcademicReportsEditData.id) {
    // If editing, include the id in the form values
    formValues.id = AcademicReportsEditData.id;
  }

  await dispatch(createAcademicReports(formValues));
};

  useEffect(() => {
    if (Object.keys(AcademicReportsErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getAcademicReports({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [AcademicReportsErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={AcademicReportsButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={AcademicReportsAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: AcademicReportsEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={AcademicReportsShowMessage && AcademicReportsErrors.name ? "error" : ""}
                help={AcademicReportsShowMessage && AcademicReportsErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AcademicReportsButtonSpinner}
            >
              {AcademicReportsButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


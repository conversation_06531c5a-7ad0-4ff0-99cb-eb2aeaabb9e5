import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { Input, Tooltip, Modal, Select, Typography, Row, Col, Tag, Card, Alert, Button } from 'antd';
import { env } from "configs/EnvironmentConfig"
import { FileOutlined, LeftCircleOutlined, ExclamationCircleOutlined, CloseOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { removeSingleAssignee, reAssignAssignment } from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import { getColor, getFileType } from "components/composeable"
import AvatarStatus from 'components/composeable/AvatarStatus'
import SaveMarks from './SaveMarks'

//test data
//end test data

import IntlMessage from "components/util-components/IntlMessage"
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Title } = Typography;
const { confirm } = Modal;

function SingleAssignee() {
  const { singleAssignee, singleAssignment } = useSelector((state) => state[ASSIGNMENT_SLICE]);
  const dispatch = useDispatch();
  const [selectedValue, setSelectedValue] = useState(setLocale('assignment.select-re-assigne'));

  useEffect(() => {

  }, [])

  const handleReAssignAssignment = async (value) => {
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: setLocale('assignment.are-you-sure-re-assigned'),
      onOk() {
        const values = {
          submission_status: value,
          id: singleAssignee?.id
        }
        dispatch(reAssignAssignment(values))
      },
      onCancel() {
        setSelectedValue(setLocale('assignment.select-re-assigne'))
      },
      zIndex: 1100,
    });
    // Show confirmation dialog here
  }
  return (
    <>

      <div className="mail-detail">
        <div className="d-lg-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center mb-3">
            <div className="font-size-md mr-3" onClick={() => { dispatch(removeSingleAssignee()) }}>
              <Tooltip title={setLocale('back')}>
                <LeftCircleOutlined className="mail-detail-action-icon font-size-md ml-0" />
              </Tooltip>
            </div>
            <AvatarStatus
              src={env.FILE_ENDPOINT_URL + singleAssignee?.student?.profile_picture}
              name={singleAssignee?.student?.full_name}
              subTitle={singleAssignee?.submission_status == 'submitted' ? `submited: ${singleAssignee?.updated_at}` : null} />
          </div>
          <div className="mail-detail-action mb-3">
            {/* <span className="mr-2 font-size-md">10-10-2023</span> */}
            <Tooltip title={setLocale('assignment.status')}>
              <Tag color={getColor(singleAssignee?.submission_status)}>{singleAssignee?.submission_status}</Tag>
            </Tooltip>
            {singleAssignee?.submission_status == 'submitted' ?
              <Select style={{ width: 150, }} value={selectedValue} className='mr-2' size="small"
                onChange={handleReAssignAssignment}
                placeholder={setLocale('assignment.select-re-assigne')}
                options={[
                  {
                    value: 're-assigned',
                    label: setLocale('assignment.re-assigne'),
                  },
                ]}
              /> : null}
          </div>
        </div>
        <div className="mail-detail-content ml-5">
          <h4 className="mb-2">{singleAssignment?.title}</h4>
          <div dangerouslySetInnerHTML={{ __html: singleAssignment?.instructions }} />

          <Row gutter={4}>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              {singleAssignee?.assignee_attachments?.length ?
                <>
                  <h4 className="mt-4">{setLocale('student.files')}</h4>
                  <div className="mail-detail-attactment">
                    {singleAssignee?.assignee_attachments.map((attachment, index) => (
                      <Alert className='mb-2' key={index}
                        message={<FileOutlined />}
                        type="success"
                        action={attachment.attachment_type_code === 'file' ? (
                          <Button key={index} size="small" type="link" target="_blank"
                            href={env.FILE_ENDPOINT_URL + attachment.attachment_path} icon={<DownloadOutlined />} > Download
                          </Button>
                        ) : (
                          <Button key={index} className='mb-2' size="small" type="link" target="_blank"
                            href={attachment.attachment_path} icon={<EyeOutlined />} > Preview
                          </Button>
                        )
                        }
                      />
                    ))}
                  </div>
                </> : null
              }
            </Col>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              {singleAssignee?.submission_status === 'submitted' && <SaveMarks></SaveMarks>}
            </Col>
          </Row>

          {/* <Input placeholder="Type your message..."
            onChange={e => console.log(e.target.value)}
            onPressEnter={(e) => singleAssigneeMessage(e.target.value)} // Trigger sending on Enter key press
            addonAfter={<SendOutlined onClick={(e) => singleAssigneeMessage(e.target.value)} />} /> */}

        </div>


      </div>

    </>
  )
}

export default SingleAssignee;
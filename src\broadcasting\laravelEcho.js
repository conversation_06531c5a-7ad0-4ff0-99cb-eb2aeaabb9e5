import Echo from 'laravel-echo';
import Pusher from 'pusher-js';
// import { REACT_APP_PUSHER_APP_KEY, REACT_APP_PUSHER_CLUSTER } from '../constants/AppConstants';
import { AUTH_TOKEN } from 'constants/AuthConstant';
import { env } from '../configs/EnvironmentConfig'
const jwtToken = localStorage.getItem(AUTH_TOKEN) || null;

window.Pusher = Pusher;
const echo = new Echo({
  broadcaster: 'pusher',
  key: env.PUSHER_APP_KEY,
  cluster: env.PUSHER_CLUSTER,
  //wsHost: "ec2-13-58-194-234.us-east-2.compute.amazonaws.com",//window.location.hostname, // Use the hostname of your Laravel application
  wsHost: env.WS_HOST, // Use the hostname of your Laravel application
  wsPort: env.WS_PORT, // Use the WebSocket port of your Laravel application
  disableStats: true,
  forceTLS: false,
  //encrypted: true, // Set to false if not using HTTPS
  enabledTransports: ['ws', 'wss'], // Enable WebSocket transports
  authEndpoint: `${env.SOCKET_ENDPOINT_URL}broadcasting/auth`,
  auth: {
    headers: {
      Authorization: `Bearer ${jwtToken}`, // Pass the user's authentication token here
    },
  },
});

export default echo;

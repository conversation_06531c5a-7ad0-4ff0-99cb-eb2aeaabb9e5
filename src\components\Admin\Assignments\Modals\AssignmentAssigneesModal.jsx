import React, { useEffect } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { Skeleton, Drawer, Row, Col } from 'antd';
import {
  setAssigneeCloseModal,
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import IntlMessage from "components/util-components/IntlMessage"
import StudentAssignees from './Partials/StudentAssignees'
import AssignmentDetail from './Partials/AssignmentDetail'
import SingleAssignee from './Partials/SingleAssignee'
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AssignmentAssigneesModal() {
  const { 
    singleAssignee, 
    openAssigneeModal, 
    assigneeModuleLabel,    
    singleAssignment
  } = useSelector((state) => state[ASSIGNMENT_SLICE]);
  const dispatch = useDispatch();

  useEffect(() => {
    
  }, [])
  return (
    <>
      <Drawer title={setLocale(`${assigneeModuleLabel}`)}
        width={'100%'}
        onClose={() => dispatch(setAssigneeCloseModal())} open={openAssigneeModal}
        maskClosable={false} bodyStyle={{ paddingBottom: 80 }} zIndex={1002}>
          
            <Row gutter={8}>
              <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                <StudentAssignees></StudentAssignees>
              </Col>
              <Col xs={24} sm={24} md={18} lg={18} xl={18}>
                {singleAssignee ?
                  <SingleAssignee></SingleAssignee> 
                  : 
                  <AssignmentDetail></AssignmentDetail>
                  }
              </Col>
            </Row>
          
      </Drawer>
    </>
  )
}

export default AssignmentAssigneesModal;
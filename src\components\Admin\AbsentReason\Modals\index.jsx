import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ABSENT_REASON } from "constants/AppConstants";
import {
  AbsentReasonAddDrawerStatus,
  createAbsentReason,
  getAbsentReason,
  onCloseError
} from "store/slices/AbsentReason/manageAbsentReasonSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {DrawerStatus, sorting, filter, AbsentReasonAddDrawer, AbsentReasonButtonAndModelLabel, AbsentReasonErrors, AbsentReasonShowMessage, AbsentReasonButtonSpinner, AbsentReasonEditData, tablePagination } = useSelector(
    (state) => state[ABSENT_REASON]
  );
  const onClose = () => {
    dispatch(AbsentReasonAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {

    if (AbsentReasonEditData && AbsentReasonEditData.id) {
      // If editing, include the id in the form values
      formValues.id = AbsentReasonEditData.id;
    }

    await dispatch(createAbsentReason(formValues))
  };
useEffect(() => {
    if (Object.keys(AbsentReasonErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getAbsentReason({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [AbsentReasonErrors]);
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>

      <Drawer
        title={AbsentReasonButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={AbsentReasonAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            absent_reason: AbsentReasonEditData?.absent_reason,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="absent_reason"
                label={setLocale('absentreason.label')}
                rules={[
                  {
                    required: true,
                    message: setLocale('absentreason.label_error'),
                  },
                ]}
                validateStatus={AbsentReasonShowMessage && AbsentReasonErrors.absent_reason ? "error" : ""}
                help={AbsentReasonShowMessage && AbsentReasonErrors.absent_reason}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AbsentReasonButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


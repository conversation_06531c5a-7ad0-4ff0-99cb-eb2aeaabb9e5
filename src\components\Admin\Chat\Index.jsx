import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Collapse, Row, Col, Tabs,Card, Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { CHAT } from "constants/chat/index";
import {
    getUserConversations
} from "store/slices/Chat/manageChatSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import Conversations from "./Modals/Conversations";
import ChatBody from "./Modals/ChatBody";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const { ChatButtonSpinner } = useSelector((state) => state[CHAT]);

    useEffect(() => {
        pageLand()
    }, []);
    const pageLand = async () => {
        await dispatch(getUserConversations())
    }
    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item> <Link to="/app/default">{setLocale('home')}</Link> </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Chat')}</Breadcrumb.Item>
            </Breadcrumb>
            <div className="code-box">                   
                <section className="code-box-description">
                   <Row gutter={16} className="chat-row-left">
                       <Col xs={24} sm={24} md={8} lg={8} xl={8} className="chat-tab">
                        <Conversations /> 
                       </Col>
                       <Col xs={24} sm={24} md={16} lg={16} xl={16}>
                         <ChatBody />
                       </Col>
                   </Row>
                </section>
            </div>
        </>    
    );
}

export default Index;

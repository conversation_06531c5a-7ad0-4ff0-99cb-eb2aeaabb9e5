import React, { useEffect, useState } from 'react';
import { Button, Col, Drawer, Form, Input, Row, Select, Skeleton, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LEAVE_REQUEST } from "constants/AppConstants";
import {
  LeaveRequestAddDrawerStatus,
  createLeaveRequest,
  // getAttendanceCode,
  getLeaveRequest
} from "store/slices/LeaveRequest/manageLeaveRequestSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import TextArea from 'antd/lib/input/TextArea';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  // const [loading, setLoading] = useState(true);
  const {
    sorting, filter,
    // AttendanceCode,
    LeaveRequestAddDrawer,
    LeaveRequestButtonAndModelLabel,
    LeaveRequestErrors,
    LeaveRequestShowMessage,
    LeaveRequestButtonSpinner,
    LeaveRequestEditData,
    tablePagination,
    approverStatuses
  } = useSelector((state) => state[LEAVE_REQUEST]);
  const onClose = () => dispatch(LeaveRequestAddDrawerStatus(false));
  // let status_value = '';
  const getModuleData = async (page, perPage, filterData, sortingData) => {
      await dispatch(getLeaveRequest({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
  }
  const onSubmit = async (formValues) => {

    if (LeaveRequestEditData && LeaveRequestEditData.id) {
      // If editing, include the id in the form values
      formValues.id = LeaveRequestEditData.id;
    }
    // formValues.absent_reason_id = LeaveRequestEditData?.absent_reason_id;
    // formValues.guardian_id = LeaveRequestEditData?.guardian_id;
    // formValues.is_full_day = LeaveRequestEditData?.is_full_day;
    // formValues.leave_days = LeaveRequestEditData?.leave_days;
    // formValues.student_id = LeaveRequestEditData?.student_id;
    // formValues.guardian_comments = LeaveRequestEditData?.guardian_comments;
    // formValues.absent_reason_id = LeaveRequestEditData?.absent_reason_id;
    // formValues.request_date_from = LeaveRequestEditData?.request_date_from;
    // formValues.request_date_to = LeaveRequestEditData?.request_date_to;
    await dispatch(createLeaveRequest(formValues)).then((result) => {        
      if (result?.payload) {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        form.resetFields();
        onClose();
      }
    })
      // .then(() => {
        // if (Object.keys(LeaveRequestErrors).length == 0) {
        //   dispatch(getLeaveRequest({
        //     page: tablePagination.current,
        //     perPage: tablePagination.pageSize,
        //     filter: filter, sorting: sorting
        //   }));          
        // }
      // })
      // .catch((error) => {        
      //   console.error("Error deleting module:", error);
      // });
  };

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };


  useEffect(() => {

    // if (LeaveRequestEditData?.status === 1) {
    //   status_value = 'Approve';
    // }
    // if (LeaveRequestEditData?.status === 2) {
    //   status_value = 'Reject';
    // }
    // dispatch(getAttendanceCode()).then((result) => {
    //   const attendanceCode = result.payload.filter(item => item.id === LeaveRequestEditData?.attendance_code_id);
    //   form.setFieldsValue({
    //     approver_comment: LeaveRequestEditData?.approver_comment,
    //     attendance_code: attendanceCode[0]?.id ?? '',
    //     status: status_value
    //   });
    //   setLoading(false);
    // });

  }, []);

  return (
    <>

      <Drawer title={LeaveRequestButtonAndModelLabel} width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose} open={LeaveRequestAddDrawer} maskClosable={false} zIndex={1002}
        bodyStyle={{ paddingBottom: 80 }} >
        <Skeleton active loading={false}>
          <Form layout="vertical" onFinish={onSubmit} form={form} onFinishFailed={onFinishFailed} autoComplete="off"
          // initialValues={{
          //   ...LeaveRequestEditData
          // }}
          >

            <Row gutter={16}>
              {/* <Col span={24}>
                <div className="mail-detail">
                  <div className="mail-detail-content">
                    <h3 className="mb-2">{LeaveRequestEditData?.absent_reason}</h3>
                    <h5 dangerouslySetInnerHTML={{ __html: LeaveRequestEditData?.guardian_comments }} className="mb-2" />
                  </div>
                </div>
              </Col> */}
              <Col span={12}>
                <Form.Item name="status" label={'Status'}
                  rules={[
                    {
                      required: true,
                      message: setLocale('conversation_template.status_error'),
                    },
                  ]}
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.status ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.status}>
                  <Select className='rounded-0' optionLabelProp="label" options={approverStatuses ? approverStatuses : null}/>
                </Form.Item>
              </Col>
              {/* <Col span={12}>
                <Form.Item name="attendance_code" label={setLocale("leave_request.attendance_code")}
                  rules={[
                    {
                      required: true,
                      message: setLocale('leave_request.attendance_code_error'),
                    },
                  ]}
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.attendance_code ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.attendance_code} >
                  <Select className='rounded-0' showSearch optionLabelProp="label" allowClear optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    } options={AttendanceCode ?? []} />
                </Form.Item>
              </Col> */}
              <Col span={24}>
                <Form.Item name="approver_comment" label={'Comments'}
                  rules={[
                    {
                      required: false,
                      message: 'Comments is required',
                    },
                  ]}
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.approver_comment ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.approver_comment} >
                  <TextArea style={{ height: 120, marginBottom: 24, }} className='rounded-0' />
                </Form.Item>
              </Col>
            </Row>

            <Space>
              <Button type="primary" htmlType="submit" loading={LeaveRequestButtonSpinner} >
                {setLocale("save")}
              </Button>
              <Button onClick={onClose}>{setLocale('cancel')}</Button>
            </Space>
          </Form>
        </Skeleton>
      </Drawer>
    </>
  );
};
export default Index;


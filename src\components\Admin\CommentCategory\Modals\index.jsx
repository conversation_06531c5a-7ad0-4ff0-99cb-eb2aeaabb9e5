import React,{useEffect} from 'react';
import { Button, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { COMMENT_CATEGORY } from "constants/AppConstants";
import {
    CommentCategoryAddDrawerStatus,
    createCommentCategory,
    getCommentCategory,
    onCloseError
  } from "store/slices/CommentCategory/manageCommentCategorySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        CommentCategoryAddDrawer,
        CommentCategoryButtonAndModelLabel,
        sorting,
        filter,
        CommentCategoryErrors,
        CommentCategoryShowMessage,
        CommentCategoryButtonSpinner,
        CommentCategoryEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[COMMENT_CATEGORY]
    );
const onClose = () => {
    dispatch(CommentCategoryAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (CommentCategoryEditData && CommentCategoryEditData.id) {
    // If editing, include the id in the form values
    formValues.id = CommentCategoryEditData.id;
  }

  await dispatch(createCommentCategory(formValues));
};

  useEffect(() => {
    if (Object.keys(CommentCategoryErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getCommentCategory({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [CommentCategoryErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={CommentCategoryButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CommentCategoryAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: CommentCategoryEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={CommentCategoryShowMessage && CommentCategoryErrors.name ? "error" : ""}
                help={CommentCategoryShowMessage && CommentCategoryErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CommentCategoryButtonSpinner}
            >
              {CommentCategoryButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


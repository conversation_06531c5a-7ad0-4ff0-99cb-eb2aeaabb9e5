import React, { useState, useEffect, useRef } from "react";
import { Col, Row, Card, Avatar, Skeleton, Breadcrumb, Select, DatePicker, Badge, Modal, Form, Input,Table } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { Icon } from "components/util-components/Icon";
import { EyeOutlined, CalendarOutlined, DeleteOutlined} from '@ant-design/icons';
import CalendarData from './CalenderData';

import { useParams, useNavigate, Link, json } from "react-router-dom";
import {
    ArrowLeftOutlined,
    ContainerOutlined
} from "@ant-design/icons";
import {
    viewCalendarEvent
} from "store/slices/CalendarEvent/manageCalendarEventSlice.js";
import Flex from "components/shared-components/Flex";
import IntlMessage from "components/util-components/IntlMessage"
import { DASHBOARD } from "constants/AppConstants";
import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import moment from 'moment';

import {
    getCalendarActivity
} from "store/slices/dashboardManagement/dashboardSlice";

const { Option } = Select;

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const AgendaList = props => {
	const { list, onShow } = props
	return (
        <div className="calendar-list">
            {
                list.map((eventItem, i) => (
                    <div key={`${eventItem.category}-${i}`} className="calendar-list-item" onClick={() => onShow(eventItem, i)}>
                        <div className="d-flex">
                            <Badge className="mr-2"color="cyan" />
                            <div>
                                <h5 className="mb-1">{eventItem.category}</h5>
                                <span className="text-muted">{eventItem.formatted_dates} </span>
                            </div>
                        </div>
                        
                    </div>
                ))
            }
        </div>
		
	)
}
function View() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const calendarRef = useRef(null);
    const {
        calendarEvents,
        calendarEventLoading,
    } = useSelector((state) => state[DASHBOARD]);
    // const initialDate = new Date();
    const [initialDate, setInitialDate] = useState(null);
    const [calendarKey, setCalendarKey] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
     const [selectedEvent, setSelectedEvent] = useState(null);
    useEffect(() => {
        dispatch(getCalendarActivity());
    }, []);

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (calendarRef.current) {
                calendarRef.current.getApi().updateSize();
            }
        }, 0);

        return () => clearTimeout(timeoutId);
    }, [initialDate]);

    const onShowCalendarEvent = (event) => {

        setSelectedEvent(event);
        setModalVisible(true);
    }
const columns = [
  {
    title: 'Name',
    dataIndex: 'formatted_days',
    key: 'formatted_days',
    render: (data, record) => (
                <>
                    <span>{record.formatted_days} {record.formatted_dates} </span>
                </>
            )
  },
  {
    title: 'Age',
    dataIndex: 'category',
    key: 'category',
  },
];
  const onShowEvent = (date, index) => {
		setInitialDate(date.start_date);
        setCalendarKey(prevKey => prevKey + 1);
	}

    const eventObjects = calendarEvents?.events ? calendarEvents?.events.map((event, index) => ({
        id: index,
        title: event.description,
        start: event.date,
        end: event.date,
        color: event.color,
        detail: event.details,
    })) : [];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Calendar Event')}</Breadcrumb.Item>
            </Breadcrumb>
            {calendarEventLoading ? <><Skeleton active loading={calendarEventLoading} ></Skeleton></> :
                <>
                <Card title={
                                <h5>
                                    <span style={{ cursor: "pointer", float: "right" }}
                                        onClick={() => navigate(-1)}
                                    >
                                        <ArrowLeftOutlined /> Back
                                    </span>{" "}
                                </h5>
                            }
                            className="calendar mb-0">
                    <Row>
                        <Col xs={24} sm={24} md={9} lg={6}>
                            {/* <h2 className="mb-4">Agenda</h2> */}
                            <AgendaList 
                                list={calendarEvents?.importantDays} 
                                onShow={onShowEvent}
                            />
                        </Col>
                        <Col xs={24} sm={24} md={15} lg={18}>
                            <FullCalendar
                                 key={calendarKey}
                                    ref={calendarRef}
                                    plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                                    initialView="dayGridMonth"
                                    initialDate={initialDate} 
                                    headerToolbar={{
                                        left: 'prev,next today',
                                        center: 'title',
                                        // right: 'dayGridMonth'
                                    }}
                                    editable={false}
                                    selectable={false}
                                    selectMirror={false}
                                    dayMaxEvents={true}
                                    weekends={true}
                                    events={eventObjects}
                                // select={handleEventAdd}
                                    eventClick={(info) => onShowCalendarEvent(info.event)}
                                // eventDrop={handleEventDrop}
                                />
                        </Col>
                        
                    </Row>
                    <Row style={{ marginTop: 40 }}>
                        <Col xs={24} sm={24} md={24} lg={24}>
                            {/* <Table dataSource={calendarEvents?.importantDays} columns={columns} /> */}
                            <Table dataSource={calendarEvents?.importantDays} columns={columns} showHeader={false} pagination={false} />
                        </Col>
                    </Row>
                    
                </Card>
                     <Modal
                        title={`${selectedEvent ? selectedEvent.title.toUpperCase() : ''} ( ${selectedEvent ? moment(selectedEvent.start).format('MMMM Do YYYY') : ''} )`}
                        open={modalVisible}
                        onCancel={() => setModalVisible(false)}
                        footer={null}
                    >
                        {selectedEvent && (
                            <div>
                                {selectedEvent.extendedProps && selectedEvent.extendedProps.detail && (
                                    <p>{setLocale('calendarEvent.description')}: {selectedEvent.extendedProps.detail}</p>
                                )}
                                {/* Add more event details here */}
                            </div>
                        )}
                    </Modal>
                </>
            }
        </>
    );
}
export default View;

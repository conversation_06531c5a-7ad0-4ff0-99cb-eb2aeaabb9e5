import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, InputNumber, Row, Select, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { BATCHES, CHARGES_RATES, CHARGES_TYPES } from "constants/AppConstants";
import {
  BatchesAddDrawerStatus,
  createBatches,
  getBatches,
  viewBatches,
  setSingleBatchStudentData,
  addStudentChargeInInvoice,
  onCloseError
} from "store/slices/Batches/manageBatchesSlice.js";
import { getChargesTypes } from "store/slices/ChargesTypes/manageChargesTypesSlice";
import { getChargesRates } from "store/slices/ChargesRates/manageChargesRatesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { useParams } from 'react-router-dom';
import moment from 'moment';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const EditInvoice = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { ViewBatchesData, DrawerStatus, BatchesAddDrawer, BatchesButtonAndModelLabel, singleBatchStudentData, BatchesErrors, BatchesShowMessage, BatchesButtonSpinner, BatchesEditData } = useSelector(
    (state) => state[BATCHES]
  );
  const { ChargesRatesResult } = useSelector((state) => state[CHARGES_RATES]);
  const [disable, setDisabled] = useState(true);
  const { ChargesTypesResult } = useSelector((state) => state[CHARGES_TYPES]);

  const params = useParams();

  const onClose = () => {
    dispatch(BatchesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {

    if (singleBatchStudentData && singleBatchStudentData.id) {
      // If editing, include the id in the form values
      formValues.id = singleBatchStudentData.id;
      formValues.school_year_id = singleBatchStudentData.student_school_year_id;
      formValues.charge_date = singleBatchStudentData.invoice_date;
    }

    await dispatch(addStudentChargeInInvoice(formValues))
  };
  useEffect(() => {
    if (Object.keys(BatchesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(viewBatches({ id: params?.id }));
    }
  }, [BatchesErrors]);

  useEffect(() => {
    dispatch(viewBatches({ id: params?.id }));
    dispatch(getChargesTypes({
      filter: {
        "is_recurring": ["no"]
      }
    }));
    dispatch(getChargesRates({
      filter: {
        "is_active": [2]
      }
    }))
  }, [])

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const [filteredChargeTypes, setFilteredChargeTypes] = useState([]);

  const onChangeHandel = (e) => {
    if (e) {
      const student = ViewBatchesData?.data?.find(
        (item) => item.value === e
      );
      if (student) {
        dispatch(setSingleBatchStudentData(student));
        const { invoice_detail } = student;
        const usedChargeTypeIds = invoice_detail.map((item) => item.charge_type_id);
        const availableChargeTypes = ChargesTypesResult?.data?.filter(
          (type) => !usedChargeTypeIds.includes(type.id)
        );
        setFilteredChargeTypes(availableChargeTypes);
      } else {
        setFilteredChargeTypes(ChargesTypesResult?.data || []);
      }
    } else {
      setFilteredChargeTypes(ChargesTypesResult?.data || []);
    }
  }

  const onHandelChargeType = (chargeType) => {
    const yearId = singleBatchStudentData.student_school_year_id;
    const organizationGradeLevelId = singleBatchStudentData.organization_grade_level_id;
    const chargeTypeId = chargeType;
    const charge_date = singleBatchStudentData.invoice_date;
    if (charge_date && chargeTypeId && yearId) {
      dispatch(getChargesRates({
        filter: {
          organization_grade_level_id: organizationGradeLevelId ?? null,
          // school_year_id: [yearId],
          charge_type_id: chargeTypeId,
          charge_date: moment.utc(charge_date).format('YYYY-MM-DD'),
          // "is_active": [1]
        }
      })).then((result) => {
        const rateID = result?.payload?.data[0]?.id;
        form.setFieldValue('charge_rate_id', rateID);
        (result?.payload?.data[0]?.plain_amount === 0) ? setDisabled(false) : setDisabled(true);
        form.setFieldsValue({ charge_amount: result?.payload?.data[0]?.plain_amount ?? 0 });
      });
    }

  }

  return (
    <>

      <Drawer
        title={BatchesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={BatchesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: BatchesEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="studentIds"
                label={setLocale('Students')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={BatchesShowMessage && BatchesErrors.name ? "error" : ""}
                help={BatchesShowMessage && BatchesErrors.name}
              >
                <Select
                  showSearch
                  // mode="multiple"
                  allowClear
                  onChange={(e) => {
                    onChangeHandel(e);
                  }}
                  optionFilterProp="children"
                  filterOption={(input, option) => (
                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                  )}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                  }
                  options={ViewBatchesData?.data ?? []}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="charge_type_id"
                label={setLocale("charges_and_invoices.charge_type")}
                rules={[
                  {
                    required: true,
                    message: setLocale("charges_and_invoices.charge_type_error"),
                  },
                ]}
                validateStatus={BatchesShowMessage && BatchesErrors.charge_type_id ? "error" : ""}
                help={BatchesShowMessage && BatchesErrors.charge_type_id}
              >
                <Select
                  onChange={(e) => {
                    onHandelChargeType(e);
                  }}
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={filteredChargeTypes ?? []}
                />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                name="charge_rate_id"
                label={setLocale("charges_and_invoices.charge_rate")}
                rules={[
                  {
                    required: true,
                    message: setLocale("charges_and_invoices.charge_rate_error"),
                  },
                ]}
                validateStatus={BatchesShowMessage && BatchesErrors.charge_rate_id ? "error" : ""}
                help={BatchesShowMessage && BatchesErrors.charge_rate_id}
              >
                <Select
                  showSearch
                  // onChange={getChargeRate}
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) => (
                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                  )}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                  }
                  options={ChargesRatesResult.data ?? []}
                />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                name="charge_amount"
                label={setLocale("charges_and_invoices.amount")}
                rules={[
                  {
                    required: true,
                    message: setLocale("charges_and_invoices.amount_error"),
                  },
                ]}
                validateStatus={BatchesShowMessage && BatchesErrors.charge_amount ? "error" : ""}
                help={BatchesShowMessage && BatchesErrors.charge_amount}
              >

                <InputNumber
                  disabled={disable}
                  className="rounded-0 w-100" min={0}
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value.replace(/\$\s?|(,*)/g, '')} />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                name="description"
                label={setLocale("calendarEvent.description")}
                rules={[
                  {
                    required: true,
                    message: setLocale("calendarEvent.description_error"),
                  },
                ]}
                validateStatus={BatchesShowMessage && BatchesErrors.description ? "error" : ""}
                help={BatchesShowMessage && BatchesErrors.description}
              >

                <TextArea rows={4} />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={BatchesButtonSpinner}
            >
              {BatchesButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default EditInvoice;


import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {LEAVE_ALLOCATION, LEAVE_ALLOCATION_API_URL, GET_ALL_LEAVE_ALLOCATION_API_URL} from 'constants/AppConstants';
import { USER_ALL_INFORMATION_ORGANIZATION_WISE } from "constants/AuthConstant";
import CommonService from 'services/CommonService/CommonService';
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const permission = JSON.parse(localStorage.getItem(USER_ALL_INFORMATION_ORGANIZATION_WISE));

export const initialState = {
  LeaveAllocationErrors       : {},
  permission: permission?.leave_allocation ?? [],
  LeaveAllocationShowMessage  : null,
  LeaveAllocationResult       : [],
  LeaveAllocationButtonSpinner : false,
  LeaveAllocationTableLoading : true,
  LeaveAllocationAddDrawer    : false,
  selectedLeaveType: null, // Add this to store the selected leave type record
  LeaveAllocationEditData     : [],
  LeaveAllocationButtonAndModelLabel  : setLocale('leaveallocation.add'),
  tablePagination: {
        current: 1,
        pageSize: 10,
        total: 0,
    },
    sorting: {},
    filter: {},
    ViewLeaveAllocationData: [],
    ViewLeaveAllocationLoader: true,
    DrawerStatus: 0,
};

export const createLeaveAllocation = createAsyncThunk(
    "createLeaveAllocation",
  async (data) => {
    try {
      const response = await CommonService.createAndUpdate(data, LEAVE_ALLOCATION_API_URL);
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors)); // Throw an error with the server response errors
    }
  }
);

export const getLeaveAllocation = createAsyncThunk(
    "getLeaveAllocation",
  async (data) => {
    try {
      const response = await CommonService.getAllPost(data, GET_ALL_LEAVE_ALLOCATION_API_URL);
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors)); // Throw an error with the server response errors
    }
  }
);

export const viewLeaveAllocation = createAsyncThunk(
  "viewLeaveAllocation",
  async (id) => {
    try {
      const response = await CommonService.showOne(id, LEAVE_ALLOCATION_API_URL);
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors)); // Throw an error with the server response errors
    }
  }
);

export const deleteLeaveAllocation = createAsyncThunk(
  "deleteLeaveAllocation",
  async (data) => {
    try {
      const response = await CommonService.deleteOne(data, LEAVE_ALLOCATION_API_URL);
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors)); // Throw an error with the server response errors
    }
  }
);

export const manageLeaveAllocationSlice = createSlice({
  name: LEAVE_ALLOCATION,
  initialState,
  reducers: {
    onCloseError: (state, action) => {
      state.LeaveAllocationErrors = {};
    },
    LeaveAllocationAddDrawerStatus: (state, action) => {
      state.LeaveAllocationAddDrawer = action.payload.status;
      state.selectedLeaveType = action.payload.record || null; // Store the record
      if (action.payload.status === false) {
            state.LeaveAllocationButtonSpinner = false;
      }
      state.DrawerStatus = action.payload.errorStatus;
      state.LeaveAllocationEditData    = [];
      state.LeaveAllocationButtonAndModelLabel = setLocale('leaveallocation.add');
    },
    LeaveAllocationEditWithDrawerStatus: (state, action) => {
      state.LeaveAllocationAddDrawer = true;
      state.DrawerStatus = action.payload.errorStatus;
      state.LeaveAllocationEditData = action.payload.data;
      state.LeaveAllocationButtonAndModelLabel = setLocale('leaveallocation.edit');
    },
    updateSortFilters: (state, action) => {
        state.filter = action.payload.filter;
        state.sorting = action.payload.sorting;
    },
    setColumnSearch: (state, action) => {
      state.filter = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createLeaveAllocation.pending, (state, action) => {
        state.DrawerStatus = 0
        state.LeaveAllocationButtonSpinner = true;
      }).addCase(createLeaveAllocation.fulfilled, (state, action) => {
        state.DrawerStatus = 0
        state.LeaveAllocationButtonSpinner = false;
        state.LeaveAllocationErrors = {}
      }).addCase(createLeaveAllocation.rejected, (state, action) => {
        state.DrawerStatus = 1
        state.LeaveAllocationShowMessage = true;
        state.LeaveAllocationButtonSpinner = false;
        state.LeaveAllocationErrors = JSON.parse(action.error.message); // Parse the error messages and store them in the state
      }).addCase(getLeaveAllocation.pending, (state, action) => {
        state.LeaveAllocationButtonSpinner = true;
        state.LeaveAllocationTableLoading = true;
      }).addCase(getLeaveAllocation.fulfilled, (state, action) => {
        state.LeaveAllocationButtonSpinner = false;
        state.LeaveAllocationTableLoading = false;
        state.LeaveAllocationResult = action.payload;
        state.tablePagination = {
            ...state.tablePagination,
            total: action.payload.pagination?.total,
            current: action.payload.pagination?.current_page,
            pageSize: action.payload.pagination?.per_page
        };
      }).addCase(getLeaveAllocation.rejected, (state, action) => {
        state.LeaveAllocationShowMessage = true; // Set the showMessage flag to display the errors
        state.LeaveAllocationButtonSpinner = false;
        state.LeaveAllocationTableLoading = false;
        state.LeaveAllocationErrors = JSON.parse(action.error.message); // Parse the error messages and store them in the state
      })
      .addCase(viewLeaveAllocation.pending, (state, action) => {
        state.ViewLeaveAllocationLoader = true;
        state.ViewLeaveAllocationData = [];
      }).addCase(viewLeaveAllocation.fulfilled, (state, action) => {
        state.ViewLeaveAllocationLoader = false;
        state.ViewLeaveAllocationData = action.payload;
      }).addCase(viewLeaveAllocation.rejected, (state, action) => {
        state.ViewLeaveAllocationLoader = false;
        state.ViewLeaveAllocationData = [];
      })
      .addCase(deleteLeaveAllocation.pending, (state, action) => {
        state.LeaveAllocationTableLoading = true;
      }).addCase(deleteLeaveAllocation.fulfilled, (state, action) => {
        state.LeaveAllocationTableLoading = false;
      }).addCase(deleteLeaveAllocation.rejected, (state, action) => {
        state.LeaveAllocationTableLoading = false;
      });
  },
});

export const { onCloseError, setColumnSearch, LeaveAllocationAddDrawerStatus, LeaveAllocationEditWithDrawerStatus, updateSortFilters } = manageLeaveAllocationSlice.actions;

export default manageLeaveAllocationSlice.reducer;

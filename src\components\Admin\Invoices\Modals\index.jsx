import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, Col, DatePicker, Drawer, Form, Radio, Input, Row, Select, Space, Table, Divider, List, Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
  InvoicesAddDrawerStatus,
  createInvoices,
  setFutureInvoicesStatus,
} from "store/slices/Invoices/manageInvoicesSlice.js";
import {
  getBatches,
  getGradeClasses
} from "store/slices/Batches/manageBatchesSlice.js";
import {
  getGradeStudents,
  setSelectedGuardianList,
  setGradeStudentGuardians,
  setSelectedRowKeys,
  setGuardianSelectedRowKeys,
} from "store/slices/ManageClass/manageManageClassSlice";
import { getChargesTypes } from "store/slices/ChargesTypes/manageChargesTypesSlice";
import { SearchOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { getGradeLevel } from "store/slices/GradeLevel/manageGradeLevelSlice";
import moment from 'moment';
import 'moment-timezone';
import IntlMessage from "components/util-components/IntlMessage";
import { DATE_FORMAT_YYYY_MM, DATE_FORMAT_YYYY_MM_DD } from "constants/DateConstant";
import { BATCHES, CHARGES_TYPES, GRADE_LEVEL, INVOICES, MANAGE_CLASS, SCHOOL_YEAR } from "constants/AppConstants";
import { USER_INFORMATION } from 'constants/AuthConstant';
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const today = new Date();
  const [dueDateCheck, setDueDateCheck] = useState(today);
  const [StudentButtonSpinner, setStudentButtonSpinner] = useState(false);
  const [StudentButtonDisable, setStudentButtonDisable] = useState(true);
  const { ChargesTypesResult } = useSelector((state) => state[CHARGES_TYPES]);
  const [showChargeType, setShowChargeType] = useState(false);

  const handleRadioChange = (e) => {
    setShowChargeType(e.target.value === "1");
  };

  const {
    gradeStudents,
    gradeStudentGuardians,
    selectedRowKeys,
    guardianSelectedRowKeys,
    selectedGuardianList,
  } = useSelector((state) => state[MANAGE_CLASS]);

  const {
    InvoicesAddDrawer,
    InvoicesButtonAndModelLabel,
    InvoicesErrors,
    InvoicesShowMessage,
    InvoicesButtonSpinner,
    InvoicesEditData,
    futureInvoicesArray,
    futureInvoicesStatus
  } = useSelector((state) => state[INVOICES]);
  const { GradeLevelResult } = useSelector((state) => state[GRADE_LEVEL]);
  const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);
  const { tablePagination, sorting, filter, gradeClassesResult } = useSelector(
    (state) => state[BATCHES]
  );
  const [stdLoading, setStdLoading] = useState(false);
  const [guardianTableStatus, setGuardianTableStatus] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const searchInput = useRef(null);

  const disabledDate = (current) => {
    // Get the current date
    const currentDate = moment.utc();

    const validSchoolYears = SchoolYearResult?.data?.filter(item => {
      const start = moment.utc(item.start_date);
      const end = moment.utc(item.end_date);
      return currentDate >= start && currentDate <= end;
    });

    // Check if current date is within the valid school years
    return !validSchoolYears.some(item => {
      const start = moment.utc(item.start_date);
      // const end = moment.utc(item.end_date);
      const end = currentDate;
      return current >= start && current <= end;
    });
  };

  const onClose = () => dispatch(InvoicesAddDrawerStatus(false));

  // const filteredArray = gradeStudents?.filter(
  //   (obj) => !classStudents?.includes(obj.key)
  // );

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters, selectedKeys, confirm, dataIndex) => {
    clearFilters();  // Clear the filter state
    setSearchText('');  // Reset the search text
    setSearchedColumn('');  // Reset the searched column
    handleSearch(selectedKeys, confirm, dataIndex)
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button onClick={() => handleReset(clearFilters, selectedKeys, confirm, dataIndex)} size="small" style={{ width: 90 }}>
            Reset
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : 'black', fontSize: 'large' }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : '',
    onFilterDropdownVisibleChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
    render: (text) =>
      searchedColumn === dataIndex ? (
        <Highlighter
          highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
          searchWords={[searchText]}
          autoEscape
          textToHighlight={text ? text.toString() : ''}
        />
      ) : (
        text
      ),
  });

  const fetchRecord = async () => {
    await dispatch(getBatches({ page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting }));
  };

  const onSubmit = async (formValues) => {

    if (InvoicesEditData && InvoicesEditData.id) {
      formValues.id = InvoicesEditData.id;
    }
    formValues.studentIds = selectedRowKeys

    if (formValues.class_id === undefined) {
      if (Array.isArray(gradeClassesResult) && gradeClassesResult.length > 0) {
        const classIds = gradeClassesResult.map(item => item.id);
        formValues.class_id = classIds;
      }
    }
    console.log('class_id was undefined, set to:', formValues.class_id);

    await dispatch(createInvoices(formValues))
      .then(() => {
        if (Object.keys(InvoicesErrors).length == 0) {
          fetchRecord();
        }
      })
      .catch((error) => {
        // Handle delete error
        console.error("Error deleting module:", error);
      });
  };

  const onMonthChange = (current) => {
    const dueDateSet = moment.utc(current).startOf('month').format('YYYY-MM-DD');
    setDueDateCheck(dueDateSet);
    form.setFieldValue('due_date', moment.utc(current).add(15, 'days'));
  };

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const dueDateDisabledCheck = (current) => {
    // Can not select days before today and today
    const due = moment.utc(dueDateCheck, 'YYYY-MM-DD');
    return current && current < due;
  };


  useEffect(() => {
    const fetchData = async () => {
      try {
        form.setFieldsValue({
          invoice_generation_type: 'Student',
        });
        await dispatch(
          getChargesTypes({
            perPage: 1000,
            filter: {
              is_recurring: ["no"],
            },
          })
        );
        await dispatch(getGradeLevel({
          perPage: 1000,
          requestFrom: 'invoices',
          // filter: { 'organization_id': filter.organization_id ?? selectedOrganization }
        }
        ));
        dispatch(setSelectedRowKeys([]));
        dispatch(setGuardianSelectedRowKeys([]));
      } catch (error) {
        console.error('Error fetching school year data:', error);
      }
    };
    if (Object.keys(InvoicesEditData).length === 0) {
      fetchData();
    }
  }, []);

  useEffect(() => {
    if (InvoicesEditData && InvoicesEditData.id) {

      const classIds = new Set();
      const organizationGradeLevelIds = new Set();
      const studentIds = new Set();

      InvoicesEditData.invoice_batch_class.forEach((item) => {
        classIds.add(item.manage_class_id);
        organizationGradeLevelIds.add(item.organization_grade_level_id);
      });

      InvoicesEditData.invoices.forEach((item) => {
        studentIds.add(item.student_id);
      });

      // Convert back to an array if needed
      const uniqueStudentIds = [...studentIds];
      const uniqueClassIds = [...classIds];
      const uniqueOrganizationGradeLevelIds = [...organizationGradeLevelIds];
      dispatch(getGradeLevel({ perPage: 1000, requestFrom: 'invoices', organization_grade_level_id: uniqueOrganizationGradeLevelIds })).then(() => {
        const organization_id = form.getFieldValue('organization_id');
        dispatch(getGradeClasses({
          organization_grade_level_id: uniqueOrganizationGradeLevelIds,
          school_year_id: userInformation.school_year_id,
          organization_id: organization_id ?? selectedOrganization
        }));
      });

      form.setFieldsValue({
        class_id: uniqueClassIds,
        organization_grade_level_id: uniqueOrganizationGradeLevelIds,
        invoice_generation_type: InvoicesEditData.generate_by,
        invoice_month: moment.utc(InvoicesEditData.invoices[0].invoice_date, 'YYYY-MM-DD'),
        due_date: moment.utc(InvoicesEditData.invoices[0].due_date, 'YYYY-MM-DD'),
      });
      setStudentButtonDisable(false);
      const organization_id = form.getFieldValue('organization_id');
      dispatch(getGradeStudents({
        class_ids: uniqueClassIds,
        requestFrom: 'invoices',
        invoice_month: moment.utc(InvoicesEditData.invoices[0].invoice_date, 'YYYY-MM-DD'),
        organization_grade_level_id: uniqueOrganizationGradeLevelIds,
        school_year_id: userInformation.school_year_id,
        organization_id: organization_id ?? selectedOrganization
      }))

    }
  }, [InvoicesEditData])

  const options = [
    ...userOrganizations?.map(organization => ({
      value: organization.id,
      label: organization.org_name,
    })),
    // {
    //   value: 'all',
    //   label: 'All',
    // },
  ];

  const handleOrganizationChange = (value) => {
    dispatch(getGradeLevel({ perPage: 1000, filter: { organization_id: value } }));
  };

  const handleChangeGrade = (value) => {
    const organization_id = form.getFieldValue('organization_id');
    dispatch(getGradeClasses({
      organization_grade_level_id: value,
      school_year_id: userInformation.school_year_id,
      organization_id: organization_id ?? selectedOrganization
    }));
  }

  const onSelectChange = (newSelectedRowKeys) => {
    dispatch(setSelectedRowKeys(newSelectedRowKeys));
  };



  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
      {
        key: "odd",
        text: "Select Odd Row",
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => index % 2 === 0);
          dispatch(setSelectedRowKeys(newSelectedRowKeys));
        },
      },
      {
        key: "even",
        text: "Select Even Row",
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => index % 2 !== 0);
          dispatch(setSelectedRowKeys(newSelectedRowKeys));
        },
      },
    ],
  };

  const columns = [
    {
      title: setLocale("name"),
      key: "title",
      dataIndex: 'title',
      ...getColumnSearchProps('title'),  // Apply search props
      render: (text) => (
        <>
          {text}
        </>
      ),
    },
  ];


  const onSelectGuardianChange = (newSelectedRowKeys) => {
    dispatch(setGuardianSelectedRowKeys(newSelectedRowKeys));
    const selectedGuardian = gradeStudentGuardians.filter((item) =>
      newSelectedRowKeys.includes(item.id)
    );

    dispatch(setSelectedGuardianList(selectedGuardian));
  };

  useEffect(() => {
    const uniqueKeys = new Set();

    selectedGuardianList.forEach((item) => {
      item.get_sibling_students.forEach((student) => {
        uniqueKeys.add(student.id);
      });
    });

    dispatch(setSelectedRowKeys([...uniqueKeys]));
  }, [selectedGuardianList]);

  const guardianRowSelection = {
    selectedRowKeys: guardianSelectedRowKeys,
    onChange: onSelectGuardianChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
      {
        key: "odd",
        text: "Select Odd Row",
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => index % 2 === 0);
          dispatch(setGuardianSelectedRowKeys(newSelectedRowKeys));
        },
      },
      {
        key: "even",
        text: "Select Even Row",
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => index % 2 !== 0);
          dispatch(setGuardianSelectedRowKeys(newSelectedRowKeys));
        },
      },
    ],
  };

  const guardianColumns = [
    {
      title: setLocale("name"),
      key: "guardians.id",
      ...getColumnSearchProps('guardians'),  // Apply search props
      render: (record) => (
        <>{record?.full_name}</>
      ),
    },
  ];


  const getClassStudent = () => {
    setStudentButtonSpinner(true);
    const class_id = form.getFieldValue('class_id');
    const organization_id = form.getFieldValue('organization_id');
    const organization_grade_level_id = form.getFieldValue('organization_grade_level_id');
    const invoice_month = form.getFieldValue('invoice_month');
    const invoice_generation_charge_type = form.getFieldValue('invoice_generation_charge_type');
    const charge_type_id = form.getFieldValue('charge_type_id');

    dispatch(getGradeStudents({
      class_ids: class_id,
      requestFrom: 'invoices',
      invoice_month: invoice_month.format('YYYY-MM-DD'),
      organization_grade_level_id: organization_grade_level_id,
      school_year_id: userInformation.school_year_id,
      organization_id: organization_id ?? selectedOrganization,
      invoice_generation_charge_type: invoice_generation_charge_type,
      charge_type_id: charge_type_id
    })).then(() => {
      setStudentButtonSpinner(false);
      dispatch(setSelectedRowKeys([]));
    });

  };


  const handleChangeButtonState = (value) => {
    const invoice_month = form.getFieldValue('invoice_month');
    const due_date = form.getFieldValue('due_date');
    const organization_grade_level_id = form.getFieldValue('organization_grade_level_id');
    // const class_id = form.getFieldValue('class_id');
    // if (invoice_month && due_date && (organization_grade_level_id && organization_grade_level_id.length > 0) && (class_id && class_id.length > 0)) {
    if (invoice_month && due_date && (organization_grade_level_id && organization_grade_level_id.length > 0)) {
      setStudentButtonDisable(false);
    } else {
      setStudentButtonDisable(true);
    }
  }

  const getClassStudentGuardians = (value) => {
    if (value === 'Family') {
      setGuardianTableStatus(true);
    } else {
      setGuardianTableStatus(false);
    }
    dispatch(setSelectedRowKeys([]));
    dispatch(setGuardianSelectedRowKeys([]));
    dispatch(setGradeStudentGuardians([]));

    // const organization_id = form.getFieldValue('organization_id');
  }

  const futureInvoicesColumns = [
    {
      title: 'Batch Name',
      dataIndex: 'batch_name',
      key: 'batch_name',
    },
    {
      title: 'Batch Status',
      dataIndex: 'batch_status',
      key: 'batch_status',
    },
    {
      title: 'Student Name',
      dataIndex: 'student_name',
      key: 'student_name',
    },
    {
      title: 'Student ID',
      dataIndex: 'student_id',
      key: 'student_id',
    },
    {
      title: 'Invoice Date',
      dataIndex: 'invoice_date',
      key: 'invoice_date',
    },
    {
      title: 'Invoice No',
      dataIndex: 'invoice_no',
      key: 'invoice_no',
    },
  ];

  return (
    <>

      <Drawer
        title={InvoicesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "60%" : window.innerWidth - 100}
        onClose={onClose}
        open={InvoicesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Modal
          open={futureInvoicesStatus}
          title="Invoice creation failed: Future dated invoices exist"
          onCancel={() => dispatch(setFutureInvoicesStatus(false))}
          footer={null}
          width={window.innerWidth > 800 ? "60%" : window.innerWidth - 100}
          zIndex={1050} // Adjust the z-index value as needed
        >
          <Table
            columns={futureInvoicesColumns}
            dataSource={futureInvoicesArray}
            rowKey="invoice_no"
            pagination={{ pageSize: 5 }}
          />
        </Modal>
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            ...InvoicesEditData,
            invoice_month: InvoicesEditData?.invoice_month ? moment.utc(InvoicesEditData?.invoice_month, 'YYYY-MM-DD') : moment.utc(new Date(), 'YYYY-MM-DD'),
            organization_id: InvoicesEditData?.organization_id ? InvoicesEditData?.organization_id : filter.organization_id && filter.organization_id !== 'all' ? filter.organization_id : selectedOrganization
          }}
        >
          <Row gutter={16}>

            {userOrganizations.length > 1 &&
              <>
                <Col xs={24} sm={24} md={12} lg={12}>
                  <Form.Item
                    name="organization_id"
                    label={setLocale('users.organization_id')}
                    rules={[
                      {
                        required: true,
                        message: setLocale('users.organization_id_error'),
                      },
                    ]}
                    validateStatus={InvoicesShowMessage && InvoicesErrors.organization_id ? "error" : ""}
                    extra={InvoicesShowMessage && InvoicesErrors.organization_id} >
                    <Select
                      placeholder={setLocale('organizations.label')}
                      optionLabelProp="label"
                      onChange={handleOrganizationChange}
                      disabled={Object.keys(InvoicesEditData).length === 0 ? false : true}
                      defaultValue={filter.organization_id ? filter.organization_id : selectedOrganization}
                      options={options ?? []}
                    />
                  </Form.Item>
                </Col>
              </>
            }

            <Col span={12}>
              <Form.Item
                name="batch_name"
                label={setLocale("invoices.batch_name")}
                rules={[
                  {
                    required: true,
                    message: setLocale("invoices.batch_name_error"),
                  },
                ]}
                validateStatus={InvoicesShowMessage && InvoicesErrors.batch_name ? "error" : ""}
                help={InvoicesShowMessage && InvoicesErrors.batch_name}
              >
                <Input className="rounded-0 w-100" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="invoice_month"
                label={setLocale("invoices.month")}
                rules={[
                  {
                    required: true,
                    message: setLocale("invoices.month_error"),
                  },
                ]}
                validateStatus={InvoicesShowMessage && InvoicesErrors.invoice_month ? "error" : ""}
                help={InvoicesShowMessage && InvoicesErrors.invoice_month}
              >
                <DatePicker
                  disabledDate={disabledDate}
                  onChange={(e) => {
                    onMonthChange(e);
                    handleChangeButtonState(e);
                  }}
                  format={DATE_FORMAT_YYYY_MM_DD} className="rounded-0 w-100" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="due_date"
                label={setLocale("invoices.due_date")}
                rules={[
                  {
                    required: true,
                    message: setLocale("invoices.due_date_error"),
                  },
                ]}
                validateStatus={InvoicesShowMessage && InvoicesErrors.due_date ? "error" : ""}
                help={InvoicesShowMessage && InvoicesErrors.due_date}
              >
                <DatePicker disabledDate={dueDateDisabledCheck} onChange={(e) => handleChangeButtonState(e)} format={DATE_FORMAT_YYYY_MM_DD} className="rounded-0 w-100" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="invoice_generation_charge_type"
                label={setLocale("invoices.generated_by_charge_type")}
                rules={[
                  {
                    required: true,
                    message: setLocale("invoices.generated_by_charge_type_error"),
                  },
                ]}
                validateStatus={
                  InvoicesShowMessage && InvoicesErrors.invoice_generation_type
                    ? "error"
                    : ""
                }
                help={InvoicesShowMessage && InvoicesErrors.invoice_generation_type}
              >
                <Radio.Group onChange={handleRadioChange}>
                  <Radio value="1">Yes</Radio>
                  <Radio value="0">No</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>

            {showChargeType && (
              <Col span={12}>
                <Form.Item
                  name="charge_type_id"
                  label={setLocale("charges_and_invoices.charge_type")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("charges_and_invoices.charge_type_error"),
                    },
                  ]}
                  validateStatus={
                    InvoicesShowMessage && InvoicesErrors.charge_type_id
                      ? "error"
                      : ""
                  }
                  help={InvoicesShowMessage && InvoicesErrors.charge_type_id}
                >
                  <Select
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                    }
                    options={ChargesTypesResult?.data ?? []}
                  />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item
                name="invoice_generation_type"
                label={setLocale('invoices.generated_by')}
                rules={[
                  {
                    required: true,
                    message: setLocale('invoices.generated_by_error'),
                  },
                ]}
                validateStatus={InvoicesShowMessage && InvoicesErrors.invoice_generation_type ? "error" : ""}
                help={InvoicesShowMessage && InvoicesErrors.invoice_generation_type}
              >
                <Select className='rounded-0' showSearch optionLabelProp="label"
                  allowClear
                  onChange={getClassStudentGuardians}
                  optionFilterProp="children"
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                  // filterSort={(optionA, optionB) =>
                  //   (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                  // }
                  options={[{ value: 'Student', label: 'Student' },
                    // { value: 'Family', label: 'Family' }
                  ]}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="organization_grade_level_id"
                label={setLocale('classes.school_grade')}
                rules={[
                  {
                    required: true,
                    message: setLocale('classes.school_grade_error'),
                  },
                ]}
                validateStatus={InvoicesShowMessage && InvoicesErrors.organization_grade_level_id ? "error" : ""}
                help={InvoicesShowMessage && InvoicesErrors.organization_grade_level_id}
              >
                <Select className='rounded-0' showSearch optionLabelProp="label"
                  allowClear
                  onChange={(e) => {
                    handleChangeGrade(e);
                    handleChangeButtonState(e);
                  }}
                  mode="multiple"
                  optionFilterProp="children"
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                  // filterSort={(optionA, optionB) =>
                  //   (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                  // }
                  options={GradeLevelResult.data ?? []}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="class_id"
                label={setLocale("classes.class")}
                rules={[
                  {
                    required: false,
                    message: setLocale("classes.classError"),
                  },
                ]}
                validateStatus={InvoicesShowMessage && InvoicesErrors.class_id ? "error" : ""}
                help={InvoicesShowMessage && InvoicesErrors.class_id}
              >
                <Select
                  className="rounded-0"
                  mode="multiple"
                  showSearch
                  optionLabelProp="label"
                  allowClear
                  optionFilterProp="children"
                  // onChange={(e) => handleChangeButtonState(e)}
                  filterOption={(input, option) => (
                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                  )}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                  }
                  options={gradeClassesResult ?? []}
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Button
                loading={StudentButtonSpinner}
                disabled={StudentButtonDisable}
                type="primary" style={{ marginTop: '31px', width: "100%" }} size="small"
                onClick={getClassStudent}
                className="">
                {setLocale("charges_and_invoices.load_students")}
              </Button>
            </Col>

            {
              guardianTableStatus ? <>
                <Col span={12}>
                  <Divider orientation="left" >
                    {setLocale("student.guardians")}
                  </Divider>
                  <Table
                    rowSelection={guardianRowSelection}
                    columns={guardianColumns}
                    dataSource={gradeStudentGuardians ?? []}
                    rowKey={(record) => record.id}  // Ensure each row has a unique key
                    pagination={{
                      pageSize: 200,
                    }}
                    scroll={{
                      y: 270,
                    }}
                  />
                </Col>
                <Col span={12}>
                  <Divider orientation="left" >
                    {setLocale("classes.students")}
                  </Divider>
                  <List
                    style={{ height: '380px', overflow: 'auto' }}
                    itemLayout="vertical"
                    size="small"
                    dataSource={selectedGuardianList ?? []}
                    renderItem={(item) => (
                      <List.Item
                        key={item.full_name}
                      >
                        <List.Item.Meta
                          title={item.full_name}
                          description={item.get_sibling_students.map((student) => (
                            <div key={student.id}>
                              {student.full_name}
                            </div>
                          ))}
                        />
                      </List.Item>
                    )}
                  />
                </Col>
              </> :
                <>
                  <Col span={24}>
                    <Divider orientation="left" >
                      {setLocale("classes.students")}
                    </Divider>
                    <Table
                      rowSelection={rowSelection}
                      columns={columns}
                      dataSource={gradeStudents ?? []}
                      pagination={{
                        pageSize: 200,
                      }}
                      scroll={{
                        y: 270,
                      }}
                    />
                  </Col>
                </>
            }


          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={InvoicesButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


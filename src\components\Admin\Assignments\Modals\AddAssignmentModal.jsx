import moment from 'moment';
import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs'
import { useSelector, useDispatch } from "react-redux";
import { Alert, Modal, Space, message, Upload, Card, Drawer, Row, Col, InputNumber, Button, Select, Form, DatePicker, Collapse, Input } from 'antd';
import { CloseCircleOutlined, ExclamationCircleOutlined, UploadOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
    getAllAssignments,
    setCloseModal,
    addAssignment,
    removeAssignmentFile,
    getAllAssignmentTypes
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import { MANAGE_CLASS } from 'constants/AppConstants'
import { TOPIC_SLICE } from 'constants/topics/index'
import { env } from "configs/EnvironmentConfig"
import { getAllTopics, setOpenModal as setTopicModal } from "store/slices/Topics/topicSlice"
import AddTopicModal from './AddTopicModal';
import IntlMessage from "components/util-components/IntlMessage"

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


const { TextArea } = Input;
const { Option } = Select;
const { confirm } = Modal;


const disabledDate = (current) => {
    // Can not select days before today and today
    return current && current < dayjs().startOf('day + 1');
};

function AddAssignmentModal() {
    const [aFile, setFile] = useState();
    const [customeWeightageDisable, setCustomWeightageDisable] = useState(true);

    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const { assignmentTypes, editData, showMessage, errors, loading, openModal, moduleLabel } = useSelector((state) => state[ASSIGNMENT_SLICE]);
    const { topics, openTopicModal, topicLabel } = useSelector((state) => state[TOPIC_SLICE]);
    const { ClassCourseEditData, ViewManageClassData, ClassStudentsData } = useSelector(state => state[MANAGE_CLASS]);
    const [isRubric, setIsRubric] = useState(false);
    useEffect(() => {
        const assignmentRubrics = editData?.assignment_rubrics.length ? editData?.assignment_rubrics : null;
        form.setFieldsValue({
            assignment_rubrics: assignmentRubrics
        })
        setIsRubric(editData?.assignment_rubrics.length ? true : false)
    }, [])
    // useEffect(() => {
    //     const values  = {
    //       course_id: ClassCourseEditData.course_id,
    //       organization_grade_level_id: ViewManageClassData.organization_grade_level_id
    //     }
    //     dispatch(getAllAssignmentTypes(null))
    //     dispatch(getAllTopics(values))
    // }, [])

    //    getClassStudentIds the student already in assignment to update
    const classStudentIds = editData?.assignees.map((classStudent) => {
        return classStudent.class_student_id
    })

    const onFinish = async (values) => {
        if (editData) { values.id = editData?.id; }
        // values.course = ClassCourseEditData.course.course
        values.class_course_id = ClassCourseEditData.id
        values.due_date = moment(values.due_date).format("YYYY-MM-DD HH:mm:ss")
        values.schedule_date = values.schedule_date ? moment(values.schedule_date).format("YYYY-MM-DD HH:mm:ss") : null
        const { ...otherFields } = values;

        const formData = new FormData();
        Object.entries(otherFields).forEach(([key, value]) => {
            if (key === "class_student_ids" && value !== null) {
                formData.append(key, JSON.stringify(value)); // Convert array to JSON string
            } else if (key === "assignmentFile") {
                // Skip appending "assignmentFile"
            } else if (key === "assignment_rubrics") {
                formData.append(key, JSON.stringify(value));
            }
            else if (value !== null) {
                formData.append(key, value);
            }
        })

        if (aFile) {
            formData.append("file", aFile);
        }
        //   for (const [key, value] of formData.entries()) {
        //     console.log(`Key: ${key}, Value: ${value}`);
        //   }

        await dispatch(addAssignment(formData))
        await dispatch(getAllAssignments({ class_course_id: ClassCourseEditData?.id }))

    }
    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };
    const beforeUpload = (file) => {
        /** check file size here */
        const isLt5M = file.size / 1024 / 1024 < 5; // Changed to < 5MB
        if (!isLt5M) {
            message.error("File must be smaller than 5MB!");
        }
        return isLt5M;
    };
    const removeAssignmentFileFun = async (assignmentAttachId) => {
        confirm({
            icon: <ExclamationCircleOutlined />,
            content: setLocale('assignment.are-you-sure-to-delete-attachment'),
            onOk() {
                dispatch(removeAssignmentFile(assignmentAttachId))
            },
            onCancel() {
                //   console.log('Cancel');
            },
            zIndex: 1100,
        });
    }
    const getRubricMarks = () => {
        let total_marks = 0
        form.getFieldValue("assignment_rubrics")?.map((rubric) => {
            total_marks += rubric?.mark ? parseFloat(rubric.mark) : 0;
        })
        form.setFieldValue("total_marks", total_marks)
        setIsRubric(total_marks > 0 ? true : false)
    }
    const customizeWeightageHandler = () => {
        setCustomWeightageDisable(!customeWeightageDisable)
        if (!customeWeightageDisable) {
            form.setFieldValue("weightage", null)
        }
    }

    return (
        <>
            <Drawer title={setLocale(`${moduleLabel}`)}
                width={window.innerWidth > 800 ? '81%' : window.innerWidth}
                onClose={() => dispatch(setCloseModal())} open={openModal}
                maskClosable={false} bodyStyle={{ paddingBottom: 80 }} zIndex={1002}>
                <Form layout="vertical" onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    form={form} encType="multipart/form-data" >
                    <Row gutter={16}>
                        <Col xs={24} sm={24} md={24} lg={13} xl={13}>
                            <Row gutter={16}>
                                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                    <Form.Item name="assignment_type_id" label={setLocale('assignment.assignmentType')}
                                        initialValue={editData?.assignment_type_id}
                                        rules={[{
                                            required: true,
                                            message: setLocale('assignment.assignmentTypeRequired'),
                                        }]}
                                        validateStatus={showMessage && errors.assignment_type_id ? "error" : ""}
                                        extra={showMessage && errors.assignment_type_id}>
                                        <Select className='rounded-0' optionLabelProp="label"
                                            filterOption={(inputValue, option) =>
                                                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                                            } showSearch>
                                            {assignmentTypes ? assignmentTypes.map((assignmentType, index) =>
                                                <Option value={assignmentType.id} key={index} label={assignmentType.name}>{assignmentType.name}</Option>
                                            ) : null}
                                        </Select>
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Row gutter={16}>
                                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                    <Form.Item name="title" label={setLocale('assignment.title')}
                                        initialValue={editData?.title}
                                        rules={[{
                                            required: true,
                                            message: setLocale('assignment.titleRequired'),
                                        }]}
                                        validateStatus={showMessage && errors.title ? "error" : ""}
                                        extra={showMessage && errors.title}>
                                        <Input />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                    <Form.List name="assignment_rubrics">
                                        {(fields, { add, remove }) => (
                                            <>
                                                {fields.map(({ key, name, ...restField }) => (
                                                    <Space key={key}
                                                        style={{ display: 'flex', marginBottom: 8 }} align="baseline" >
                                                        <Form.Item
                                                            {...restField}
                                                            name={[name, 'rubric']}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: 'Rubric is required',
                                                                },
                                                            ]}>
                                                            <Input disabled={editData} placeholder="Rubric" />
                                                        </Form.Item>
                                                        <Form.Item
                                                            {...restField}
                                                            name={[name, 'mark']}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: 'Marks is required',
                                                                },
                                                            ]} >
                                                            <Input disabled={editData} placeholder="marks" onKeyUp={() => getRubricMarks()} />
                                                        </Form.Item>
                                                        {!editData && <MinusCircleOutlined onClick={() => { remove(name); getRubricMarks() }} />}
                                                    </Space>
                                                ))}
                                                <Form.Item>
                                                    {!editData && <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                                                        {setLocale('Add Rubrics')}
                                                    </Button>}
                                                </Form.Item>
                                            </>
                                        )}
                                    </Form.List>
                                </Col>
                                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                    <Form.Item name="instructions" label={setLocale('assignment.instructions')}
                                        initialValue={editData?.instructions}
                                        rules={[{ required: false }]}>
                                        <TextArea rows={4} />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                    <Form.Item name="assignmentFile" label={setLocale('assignment.file')}
                                        rules={[{
                                            required: false,
                                            message: setLocale('assignment.fileRequired'),
                                        }
                                        ]}>
                                        <Upload
                                            fileList={aFile ? [aFile] : []}
                                            maxCount={1}
                                            listType="picture"
                                            beforeUpload={(file) => {
                                                return beforeUpload(file);
                                            }}
                                            customRequest={(info) => {
                                                setFile(info.file)
                                            }}
                                            showUploadList={{
                                                showRemoveIcon: true,
                                                showPreviewIcon: true,
                                            }}
                                            onRemove={(file) => {
                                                setFile(null)
                                            }}>
                                            <Button icon={<UploadOutlined />}>{setLocale("import.file")}</Button>
                                        </Upload>
                                    </Form.Item>

                                    {editData?.assignmentAttachments.map((attachment, index) => (
                                        <Alert className='mb-2'
                                            key={index}
                                            message={attachment.attachment_name}
                                            type="info"
                                            action={
                                                <Space>
                                                    <Button size="small"
                                                        type="link"
                                                        target="_blank"
                                                        href={attachment.attachment_type_code === 'file' ? env.FILE_ENDPOINT_URL + attachment.attachment_path : attachment.attachment_path}
                                                        ghost> Preview </Button>
                                                    <Button size="small" loading={loading} danger ghost onClick={() => removeAssignmentFileFun(attachment.id)}> Delete </Button>
                                                </Space>}
                                        />
                                    ))}

                                </Col>
                            </Row>
                        </Col>
                        <Col xs={24} sm={24} md={24} lg={11} xl={11}>
                            <Card bordered={false} style={{ borderLeft: '1px solid rgb(230, 235, 241)', borderRadius: '0px' }}>
                                <Row gutter={16}>
                                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                        <Form.Item name="class_student_ids"
                                            initialValue={classStudentIds}
                                            label={setLocale('assignment.selectStudent')}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: setLocale('assignment.studentRequired'),
                                                },
                                            ]} validateStatus={showMessage && errors.class_student_ids ? "error" : ""}
                                            extra={showMessage && errors.class_student_ids}>
                                            <Select mode='multiple' className='rounded-0'
                                                disabled={classStudentIds?.length > 0}
                                                maxTagCount={1}
                                                optionLabelProp="label"
                                                filterOption={(inputValue, option) =>
                                                    option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                                                }
                                                showSearch
                                                onChange={(values) => {
                                                    // Handle the "Select All" logic here
                                                    if (values.includes('select-all')) {
                                                        if (ClassStudentsData && ClassStudentsData.length < form.getFieldValue('class_student_ids').length) {
                                                            form.setFieldsValue({
                                                                class_student_ids: []
                                                            });
                                                        } else {
                                                            form.setFieldsValue({
                                                                class_student_ids: ClassStudentsData.map(student => student.id),
                                                            });
                                                        }
                                                    }
                                                }}>
                                                <Option value="select-all" key="select-all" label="Select All"> Select All</Option>
                                                {ClassStudentsData ? ClassStudentsData.map((classStudent, index) => (
                                                    <Option value={classStudent.id} key={classStudent.id} label={classStudent.full_name}>
                                                        {classStudent.full_name}
                                                    </Option>
                                                )) : null}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                        <Form.Item name="topic_id"
                                            initialValue={editData?.topic_id}
                                            label={<span> {setLocale('assignment.topic')}
                                                <Button onClick={() => dispatch(setTopicModal())} type="link" style={{ marginLeft: '4px', padding: '0px', height: '0px', lineHeight: '0px' }} >
                                                    {setLocale(`Add Topic`)}
                                                </Button>
                                            </span>}
                                            rules={[{
                                                required: true,
                                                message: setLocale('assignment.topicRequired'),
                                            }]}
                                            validateStatus={showMessage && errors.topic_id ? "error" : ""}
                                            extra={showMessage && errors.topic_id}>
                                            <Select className='rounded-0' optionLabelProp="label"
                                                filterOption={(inputValue, option) =>
                                                    option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                                                } showSearch>
                                                {topics ? topics.map((topic, index) =>
                                                    <Option value={topic.id} key={index} label={topic.name}>{topic.name}</Option>
                                                ) : null}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                        <Form.Item name="weightage"
                                            initialValue={editData?.weightage}
                                            label={<>
                                                {setLocale('assignment.weightage')}
                                                <Button onClick={() => customizeWeightageHandler()} type="link" style={{ marginLeft: '4px', padding: '0px', height: '0px', lineHeight: '0px' }} >
                                                    {customeWeightageDisable ? setLocale(`Click for Customize Weightage`) : setLocale(`Click for Default Weightage`)}
                                                </Button>
                                            </>}
                                            rules={[{
                                                required: false,
                                                message: setLocale('assignment.weightageRequired'),
                                            }]}
                                            validateStatus={showMessage && errors.weightage ? "error" : ""}
                                            extra={showMessage && errors.weightage}>
                                            <InputNumber min={0} disabled={customeWeightageDisable} className='rounded-0' style={{ 'width': '100%' }} />
                                            <Alert message={setLocale('If you want to use default weightage then leave this field empty, don\'t enter Zero')} type="warning" />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                                        <Form.Item name="assignment_status"
                                            initialValue={editData?.assignment_status}
                                            label={setLocale('assignment.assignmentStatus')}
                                            rules={[{
                                                required: true,
                                                message: setLocale('assignment.assignmentStatusRequired'),
                                            }]}
                                            validateStatus={showMessage && errors.assignment_status ? "error" : ""}
                                            extra={showMessage && errors.assignment_status}>
                                            <Select>
                                                <Option value={'pending'}> {setLocale("assignment.pending")} </Option>
                                                <Option value={'scheduled'}> {setLocale("assignment.scheduled")} </Option>
                                                <Option value={'assigned'}> {setLocale("assignment.assigned")} </Option>
                                                <Option value={'cancelled'}> {setLocale("assignment.cancelled")} </Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                                        <Form.Item name="total_marks"
                                            initialValue={editData?.total_marks}
                                            label={setLocale('assignment.totalMarks')}
                                            rules={[{
                                                required: true,
                                                message: setLocale('assignment.totalMarksRequired'),
                                            }, {
                                                validator: (_, value) => {
                                                    if (value > 0) {
                                                        return Promise.resolve();
                                                    }
                                                    return Promise.reject(new Error('Total must be greater than zero!'));
                                                }
                                            }]}
                                            validateStatus={showMessage && errors.total_marks ? "error" : ""}
                                            extra={showMessage && errors.total_marks}>
                                            <InputNumber disabled={isRubric} className='rounded-0' style={{ 'width': '100%' }} />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                                        <Form.Item name="due_date"
                                            initialValue={editData?.due_date ? moment(editData?.due_date) : null}
                                            label={setLocale('assignment.dueDate')}
                                            rules={[{
                                                required: true,
                                                message: setLocale('assignment.dueDateRequired'),
                                            }]}
                                            validateStatus={showMessage && errors.due_date ? "error" : ""}
                                            extra={showMessage && errors.due_date}>
                                            <DatePicker
                                                minuteStep={5}
                                                style={{ width: "100%" }}
                                                format="YYYY-MM-DD HH:mm"
                                                // disabledDate={disabledDate}
                                                showTime={{
                                                    defaultValue: dayjs('23:59:59', 'HH:mm'),
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                                        <Form.Item name="schedule_date"
                                            initialValue={editData?.schedule_date ? moment(editData?.schedule_date) : null}
                                            label={setLocale('assignment.scheduleDate')}
                                            rules={[{
                                                required: false,
                                                message: setLocale('assignment.scheduleDateRequired'),
                                            }]}
                                            validateStatus={showMessage && errors.schedule_date ? "error" : ""}
                                            extra={showMessage && errors.schedule_date}>
                                            <DatePicker
                                                minuteStep={5}
                                                style={{ width: "100%" }}
                                                format="YYYY-MM-DD HH:mm"
                                                disabledDate={disabledDate}
                                                showTime={{
                                                    defaultValue: dayjs('23:59:59', 'HH:mm'),
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>

                                    {/* <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                                <Form.Item name="add_to_gradebook" label={setLocale('assignment.addToGradebook')}
                                    rules={[ { 
                                        required: true,
                                        message: setLocale('assignment.addToGradebookRequired!'), } ]}
                                        initialValue={1} validateStatus={showMessage && errors.name ? "error" : ""}
                		    extra={showMessage && errors.name}>
                                    <Select>
                                        <Option value={1}> Yes </Option>
                                        <Option value={0}> No </Option>
                                    </Select>
                                </Form.Item>
                            </Col> */}
                                </Row>
                            </Card>
                        </Col>
                    </Row>

                    <Row>
                        <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                            <Form.Item label="">
                                <Button className='rounded-0' loading={loading} type="primary" htmlType="submit">
                                    {setLocale('save')}
                                </Button>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
                {openTopicModal ? <AddTopicModal /> : null}
            </Drawer>
        </>
    )
}

export default AddAssignmentModal;

import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CLASS_WISE } from "constants/AppConstants";
import {
  ClasswiseAddDrawerStatus,
  createClasswise,
  getClasswise,
  onCloseError
} from "store/slices/ClassWise/manageClassWiseSlice";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {
    ClasswiseAddDrawer,
    ClasswiseButtonAndModelLabel,
    sorting,
    filter,
    ClasswiseErrors,
    ClasswiseShowMessage,
    ClasswiseButtonSpinner,
    ClasswiseEditData,
    tablePagination,
    DrawerStatus
  } = useSelector(
    (state) => state[CLASS_WISE]
  );
  const onClose = () => {
    dispatch(ClasswiseAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {
    if (ClasswiseEditData && ClasswiseEditData.id) {
      // If editing, include the id in the form values
      formValues.id = ClasswiseEditData.id;
    }

    await dispatch(createClasswise(formValues));
  };

  useEffect(() => {
    if (Object.keys(ClasswiseErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getClasswise({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ClasswiseErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>

      <Drawer
        title={ClasswiseButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ClasswiseAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: ClasswiseEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={ClasswiseShowMessage && ClasswiseErrors.name ? "error" : ""}
                help={ClasswiseShowMessage && ClasswiseErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={ClasswiseButtonSpinner}
            >
              {ClasswiseButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


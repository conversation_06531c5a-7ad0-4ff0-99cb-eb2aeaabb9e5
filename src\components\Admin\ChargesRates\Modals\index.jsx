import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, DatePicker, Drawer, Form, Input, InputNumber, Row, Select, Skeleton, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CHARGES_RATES, CHARGES_TYPES, GRADE_LEVEL, SCHOOL_YEAR } from "constants/AppConstants";
import {
  ChargesRatesAddDrawerStatus,
  createChargesRates,
  getChargesRates,
  getChargesRateExist,
  onCloseError
} from "store/slices/ChargesRates/manageChargesRatesSlice.js";
import { getSchoolYear } from "store/slices/SchoolYear/manageSchoolYearSlice";
import { getChargesTypes } from "store/slices/ChargesTypes/manageChargesTypesSlice";
import { getGradeLevel } from "store/slices/GradeLevel/manageGradeLevelSlice";
import IntlMessage from "components/util-components/IntlMessage";
import moment from "moment";
import 'moment-timezone';
import { DATE_FORMAT_YYYY_MM } from "constants/DateConstant";
import { DEFAULT_YEAR } from 'constants/AuthConstant';
const defaultYear = JSON.parse(localStorage.getItem(DEFAULT_YEAR));
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { TextArea } = Input;

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [disableField, setDisableField] = useState(false);
  const {
    ChargeRateExistData,
    ChargesRatesAddDrawer,
    ChargesRatesButtonAndModelLabel,
    ChargesRatesErrors,
    ChargesRatesShowMessage,
    ChargesRatesButtonSpinner,
    ChargesRatesEditData,
    tablePagination,
    sorting, filter,
    DrawerStatus,
  } = useSelector((state) => state[CHARGES_RATES]);
  const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);
  const { ChargesTypesResult } = useSelector((state) => state[CHARGES_TYPES]);
  const { GradeLevelResult } = useSelector((state) => state[GRADE_LEVEL]);

  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [chargeBy, setChargeBy] = useState(true);
  const [disableDatePicker, setDisableDatePicker] = useState(true);
  const [amountState, setAmountState] = useState(true);
  const [showGradeLevel, setShowGradeLevel] = useState(true);
  const [previousChargeRateId, setPreviousChargeRateId] = useState(null);
  const previousYearId = ChargesRatesEditData?.school_year_id ?? null;
  const previousGradeId = ChargesRatesEditData?.organization_grade_level_id ?? null;
  const previousChargeTypeId = ChargesRatesEditData?.charge_type_id ?? null;

  const onClose = () => {
    dispatch(ChargesRatesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        await dispatch(getSchoolYear({ perPage: 1000 }));
        await dispatch(getChargesTypes({ perPage: 1000 }));
        await dispatch(getGradeLevel({ perPage: 1000 })).then(() => {
          setLoading(false);
        });
        const desiredObject = defaultYear;
        if (Object.keys(ChargesRatesEditData).length > 0 && (ChargesRatesEditData?.student_charge_count !== 0 || ChargesRatesEditData?.recurring_charge_count !== 0)) {
          setDisableField(true);
        }

        Object.keys(ChargesRatesEditData).length > 0 ? setStartDate(moment(ChargesRatesEditData?.effective_from, 'YYYY-MM')) : setStartDate(moment(desiredObject.start_date, 'YYYY-MM'));
        Object.keys(ChargesRatesEditData).length > 0 ? setEndDate(moment(ChargesRatesEditData?.effective_to, 'YYYY-MM-DD')) : setEndDate(moment('2099-12-31', 'YYYY-MM-DD'));
        form.setFieldsValue({
          effective_from: Object.keys(ChargesRatesEditData).length > 0 ? moment(ChargesRatesEditData?.effective_from, 'YYYY-MM') : moment(desiredObject.start_date, 'YYYY-MM'),
          effective_to: Object.keys(ChargesRatesEditData).length > 0 ? moment(ChargesRatesEditData?.effective_to, 'YYYY-MM-DD') : moment('2099-12-31', 'YYYY-MM-DD'),
        });

        let amountString = ChargesRatesEditData.plain_amount.replace('$', ''); // Remove the dollar sign
        let amount = Math.round(parseFloat(amountString));

        form.setFieldsValue({ amount: amount ?? 0 });

      } catch (error) {
        console.error('Error fetching school year data:', error);
      }
    };

    fetchData();
  }, []);


  const onSubmit = async (formValues) => {
    await dispatch(getChargesRateExist({ rateId: ChargesRatesEditData?.id, chargeTypeId: formValues.charge_type_id, organizationGradeLevelId: formValues.organization_grade_level_id }))
      .then((result) => {
        const checkData = result.payload;
        if (Object.keys(checkData).length > 0) {
          formValues.previousChargeRateId = checkData.id;
        } else {
          formValues.previousChargeRateId = null;
        }
      });

    if (ChargesRatesEditData && ChargesRatesEditData.id) {
      formValues.id = ChargesRatesEditData.id;
    }
    if (previousChargeTypeId != formValues.charge_type_id) {
      formValues.prev_charge_type_id = previousChargeTypeId;
    }
    if (previousYearId != formValues.school_year_id) {
      formValues.prev_school_year_id = previousYearId;
    }
    if (previousGradeId != formValues.organization_grade_level_id) {
      formValues.prev_organization_grade_level_id = previousGradeId;
    }

    // console.log(formValues);
    await dispatch(createChargesRates(formValues))
  };

  useEffect(() => {
    if (Object.keys(ChargesRatesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getChargesRates({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ChargesRatesErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const onSchoolYearHandel = (year) => {

    if (year !== undefined) {
      const desiredObject = SchoolYearResult?.data?.find(item => item.id === year);
      setStartDate(moment(desiredObject.start_date, 'YYYY-MM'));
      setEndDate(moment('2099-12-31', 'YYYY-MM-DD'));
      form.setFieldsValue({
        effective_from: moment(desiredObject.start_date, 'YYYY-MM'),
        effective_to: moment('2099-12-31', 'YYYY-MM-DD'),
      });
    } else {
      setStartDate('');
      setEndDate('');
      form.setFieldsValue({
        effective_from: '',
        effective_to: '',
      });
    }
    // checkChargeRateExist(year);
  }

  const disabledDate = (current) => {
    // Can not select days after today and before start Date
    const start = startDate;
    const end = endDate;
    return current >= end;
  };

  useEffect(() => {

  }, [startDate]);


  const checkChargeRateExist = async (data) => {
    const chargeTypeId = form.getFieldValue('charge_type_id');
    // const schoolYearId = form.getFieldValue('school_year_id');
    const organizationGradeLevelId = form.getFieldValue('organization_grade_level_id');

    // dispatch(getChargesRateExist({ rateId: ChargesRatesEditData?.id, chargeTypeId, schoolYearId, organizationGradeLevelId }))
    await dispatch(getChargesRateExist({ rateId: ChargesRatesEditData?.id, chargeTypeId, organizationGradeLevelId }))
      .then((result) => {
        const checkData = result.payload;

        if (Object.keys(checkData).length > 0) {
          setPreviousChargeRateId(checkData.id);
          setDisableDatePicker(false);

          // Use destructuring for cleaner code
          const { effective_from } = checkData;

          // if (typeof data === 'number' && effective_from) {
          // Use moment.js startOf to set the date to the first day of the month
          const start_Date = moment(effective_from, 'YYYY-MM').startOf('month');
          setStartDate(start_Date);
          form.setFieldsValue({ effective_from: start_Date });
          // } else {
          //   setStartDate(null);
          //   form.setFieldsValue({ effective_from: '' });
          // }
        } else {
          setPreviousChargeRateId(null);
          // const schoolYearId = form.getFieldValue('school_year_id');
          // onSchoolYearHandel(schoolYearId);
          setDisableDatePicker(true);
        }
      });
  };

  const onHandelChargeType = (chargeType) => {
    if (chargeType) {
      const desiredObject = ChargesTypesResult?.data?.find(item => (item.id === chargeType));
      setChargeBy(true);
      form.setFieldsValue({ rate_apply_by: 'By Charge Amount' });
      if (desiredObject?.is_discount === 'Yes') {
        setAmountState(false);
      } else {
        setAmountState(true);
      }

      if (desiredObject?.all_grades === 'Yes') {
        setShowGradeLevel(false);
      } else {
        setShowGradeLevel(true);
      }
    }
  }

  const checkChargeApply = async (data) => {
    if (data === 'By Charge Amount') {
      setChargeBy(true);
    } else {
      setChargeBy(false);
    }
  }


  return (
    <>
      <Drawer
        title={ChargesRatesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ChargesRatesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Skeleton loading={loading} active>
          <Form
            layout="vertical"
            onFinish={onSubmit}
            form={form}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            initialValues={{
              ...ChargesRatesEditData,
              amount: ChargesRatesEditData ? ChargesRatesEditData?.plain_amount : 0,
              rate_apply_by: ChargesRatesEditData && Object.keys(ChargesRatesEditData).length > 0 ? ChargesRatesEditData?.plain_amount && ChargesRatesEditData?.plain_amount > 0 ? 'By Charge Amount' : 'By Charge Percentage' : 'By Charge Amount',
              effective_from: ChargesRatesEditData ? ChargesRatesEditData?.effective_from ? moment(ChargesRatesEditData?.effective_from, 'YYYY-MM') : startDate : startDate,
              effective_to: ChargesRatesEditData ? ChargesRatesEditData?.effective_to ? moment(ChargesRatesEditData?.effective_to, 'YYYY-MM-DD') : endDate : endDate,
            }}
          >
            <Row gutter={16}>
              {/* <Col span={12}>
                <Form.Item
                  name="school_year_id"
                  label={setLocale("charges_and_invoices.school_year")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("charges_and_invoices.school_year_error"),
                    },
                  ]}
                  validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.school_year_id ? "error" : ""}
                  help={ChargesRatesShowMessage && ChargesRatesErrors.school_year_id}
                >
                  <Select
                    showSearch
                    disabled={disableField}
                    onChange={(e) => {
                      onSchoolYearHandel(e);
                      checkChargeRateExist(e);
                    }
                    }
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    }
                    options={SchoolYearResult?.data ?? []}
                  />
                </Form.Item>
              </Col> */}

              <Col span={12}>
                <Form.Item
                  name="charge_type_id"
                  label={setLocale("charges_and_invoices.charge_type")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("charges_and_invoices.charge_type_error"),
                    },
                  ]}
                  validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.charge_type_id ? "error" : ""}
                  help={ChargesRatesShowMessage && ChargesRatesErrors.charge_type_id}
                >
                  <Select
                    showSearch
                    allowClear
                    onChange={(e) => {
                      checkChargeRateExist(e);
                      onHandelChargeType(e);
                    }}
                    optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    }
                    options={ChargesTypesResult?.data ?? []}
                  />
                </Form.Item>
              </Col>

              {showGradeLevel && (
                <Col span={12}>
                  <Form.Item
                    name="organization_grade_level_id"
                    label={setLocale("charges_and_invoices.grade_level")}
                    rules={[
                      {
                        required: true,
                        message: setLocale("charges_and_invoices.grade_level_error"),
                      },
                    ]}
                    validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.organization_grade_level_id ? "error" : ""}
                    help={ChargesRatesShowMessage && ChargesRatesErrors.organization_grade_level_id}
                  >
                    <Select
                      allowClear
                      showSearch
                      disabled={disableField}
                      // mode={ChargesRatesEditData.length > 0 ? "default" : "multiple"}
                      onChange={checkChargeRateExist}
                      optionFilterProp="children"
                      filterOption={(input, option) => (
                        (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                      )}
                      // filterSort={(optionA, optionB) =>
                      //   (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                      // }
                      options={GradeLevelResult?.data ?? []}
                    />
                  </Form.Item>
                </Col>
              )}

              {amountState === false &&
                <>
                  <Col span={12}>
                    <Form.Item
                      name="rate_apply_by"
                      label={setLocale("charges_and_invoices.charge_apply")}
                      rules={[
                        {
                          required: true,
                          message: setLocale("charges_and_invoices.charge_apply_error"),
                        },
                      ]}
                      validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.organization_grade_level_id ? "error" : ""}
                      help={ChargesRatesShowMessage && ChargesRatesErrors.organization_grade_level_id}
                    >
                      <Select
                        allowClear
                        showSearch
                        disabled={disableField}
                        // mode={ChargesRatesEditData.length > 0 ? "default" : "multiple"}
                        onChange={checkChargeApply}
                        optionFilterProp="children"
                        filterOption={(input, option) => (
                          (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                        )}
                        // filterSort={(optionA, optionB) =>
                        //   (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                        // }
                        options={[
                          {
                            value: 'By Charge Amount',
                            label: 'By Charge Amount',
                          },
                          {
                            value: 'By Charge Percentage',
                            label: 'By Charge Percentage',
                          },
                        ]}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={12} >
                    <Form.Item
                      name="charge_type_dependent_id"
                      label={setLocale("charges_and_invoices.charge_type_dependent_id")}
                      rules={[
                        {
                          required: true,
                          message: setLocale("charges_and_invoices.charge_type_dependent_id_error"),
                        },
                      ]}
                      validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.charge_type_id ? "error" : ""}
                      help={ChargesRatesShowMessage && ChargesRatesErrors.charge_type_id}
                    >
                      <Select
                        showSearch
                        allowClear
                        optionFilterProp="children"
                        filterOption={(input, option) => (
                          (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                        )}
                        filterSort={(optionA, optionB) =>
                          (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                        }
                        options={ChargesTypesResult?.data ?? []}
                      />
                    </Form.Item>
                  </Col>
                </>
              }

              {chargeBy === false &&
                <>
                  <Col span={12}>
                    <Form.Item
                      name="charge_percentage"
                      label={setLocale("charges_and_invoices.charge_percentage")}
                      rules={[
                        {
                          required: true,
                          message: setLocale("charges_and_invoices.charge_percentage_error"),
                        },
                      ]}
                      validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.amount ? "error" : ""}
                      help={ChargesRatesShowMessage && ChargesRatesErrors.amount}
                    >
                      <InputNumber className="rounded-0 w-100" min={0} max={100} />
                    </Form.Item>
                  </Col>
                </>
              }

              {chargeBy === true &&
                <>
                  <Col span={12}>
                    <Form.Item
                      name="amount"
                      label={setLocale("charges_and_invoices.amount")}
                      rules={[
                        {
                          required: true,
                          message: setLocale("charges_and_invoices.amount_error"),
                        },
                      ]}
                      validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.amount ? "error" : ""}
                      help={ChargesRatesShowMessage && ChargesRatesErrors.amount}
                    >
                      <InputNumber className="rounded-0 w-100" min={0}
                        formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={(value) => value.replace(/\$\s?|(,*)/g, '')} />
                    </Form.Item>
                  </Col>
                </>
              }

              <Col span={12}>
                <Form.Item
                  name="effective_from"
                  label={setLocale("charges_and_invoices.effective_from")}
                  rules={[
                    {
                      required: true,
                      message: setLocale(
                        "charges_and_invoices.effective_from_error"
                      ),
                    },
                  ]}
                  validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.effective_from ? "error" : ""}
                  help={ChargesRatesShowMessage && ChargesRatesErrors.effective_from}
                >
                  <DatePicker.MonthPicker
                    // disabled={disableDatePicker}
                    disabledDate={disabledDate} format={DATE_FORMAT_YYYY_MM} className="rounded-0 w-100" />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="effective_to"
                  label={setLocale("charges_and_invoices.effective_to")}
                  rules={[
                    {
                      required: true,
                      message: setLocale(
                        "charges_and_invoices.effective_to_error"
                      ),
                    },
                  ]}
                  validateStatus={ChargesRatesShowMessage && ChargesRatesErrors.effective_to ? "error" : ""}
                  help={ChargesRatesShowMessage && ChargesRatesErrors.effective_to}
                >
                  <DatePicker.MonthPicker disabled={true} disabledDate={disabledDate} format={DATE_FORMAT_YYYY_MM} className="rounded-0 w-100" />
                </Form.Item>
              </Col>

            </Row>

            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={ChargesRatesButtonSpinner}
              >
                {setLocale("save")}
              </Button>
              <Button onClick={onClose}>{setLocale('cancel')}</Button>
            </Space>
          </Form>
        </Skeleton>
      </Drawer >
    </>
  );
};
export default Index;


import React, {useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ALLERGIES } from "constants/AppConstants";
import {
    AllergiesAddDrawerStatus,
    createAllergies,
    getAllergies,
    onCloseError
  } from "store/slices/Allergies/manageAllergiesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {DrawerStatus, filter, sorting, AllergiesAddDrawer, AllergiesButtonAndModelLabel, AllergiesErrors, AllergiesShowMessage, AllergiesButtonSpinner, AllergiesEditData, tablePagination } = useSelector(
        (state) => state[ALLERGIES]
      );
const onClose = () => {
    dispatch(AllergiesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (AllergiesEditData && AllergiesEditData.id) {
    // If editing, include the id in the form values
    formValues.id = AllergiesEditData.id;
  }

  await dispatch(createAllergies(formValues))
};
useEffect(() => {
    if (Object.keys(AllergiesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getAllergies({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [AllergiesErrors]);
const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={AllergiesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={AllergiesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: AllergiesEditData?.name,
            allergy_category: AllergiesEditData?.allergy_category,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={AllergiesShowMessage && AllergiesErrors.name ? "error" : ""}
                help={AllergiesShowMessage && AllergiesErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="allergy_category"
                label={setLocale('allergies.allergy_category')}
                rules={[
                  {
                    required: false,
                    message: ("allergies.allergy_category_error"),
                  },
                ]}
                validateStatus={AllergiesShowMessage && AllergiesErrors.allergy_category ? "error" : ""}
                help={AllergiesShowMessage && AllergiesErrors.allergy_category}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AllergiesButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


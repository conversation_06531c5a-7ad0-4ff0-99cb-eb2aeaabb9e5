import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { DeleteOutlined, EditOutlined, DownOutlined } from "@ant-design/icons";
import {
  Skeleton,
  Divider,
  Tooltip,
  Typography,
  Table,
  Popconfirm,
  Input,
  Row,
  Col,
  Card,
  Form,
  Select,
  Button,
  Space,
  Radio,
  Tag,
  Spin,
  Dropdown,
  Menu
} from "antd";
import {
  getStudentGuardians,
  addStudentGuardian,
  deleteGuardian,
  makePrimaryContact
} from "store/slices/Student/manageStudentSlice.js";
import debounce from "lodash/debounce";
import { STUDENT } from "constants/student/index";
import { NAME_OF_COMMON_SLICE } from "constants/AppConstants";
import IntlMessage from "components/util-components/IntlMessage";
import { MANAGE_GUARDIAN } from "constants/guardian/index";
import {
  ManageGuardianAddDrawerStatus,
  getAllGuardians,
  getGuardianInStudentForEdit
} from "store/slices/ManageGuardian/manageManageGuardianSlice.js";
import AddManageGuardianModal from "../../ManageGuardian/Modals/index";
import { USER_INFORMATION } from "constants/AuthConstant";
import { isJSON } from "components/composeable";
// const { Text } = Typography;
// const { TextArea } = Input;
const { Option } = Select;

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const GuardianFields = () => {

  const {
    loadStudentData,
    personalInfo,
    permission,
    StudentShowMessage,
    StudentErrors,
    StudentButtonSpinner,
    studentGuardians,
  } = useSelector((state) => state[STUDENT])
  const { preLoadedStudentData } = useSelector((state) => state[NAME_OF_COMMON_SLICE])
  const {
    ManageGuardianAddDrawer,
    manageGuardianResult,
    sorting,
    ManageGuardianTableLoading,
    ManageGuardianButtonSpinner
  } = useSelector((state) => state[MANAGE_GUARDIAN])
  const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION))

  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [portalAccess, setPortalAccess] = useState(0)
  const [guardians, setGuardians] = useState([])
  const handleOpenModal = () => {
    dispatch(ManageGuardianAddDrawerStatus({ errorStatus: 1, status: true }));
  }

  useEffect(() => {
    if (personalInfo) {
      dispatch(getStudentGuardians({ id: personalInfo?.enc_id }));
      dispatch(getAllGuardians({ page: 1, perPage: 10, filter: isJSON({ name: "" }), sorting: isJSON({ name: "asc" }) }));
    }
  }, []);


  const saveGuardianToStudent = async (values) => {
    values.organization_id = currentUser?.organization_id
    if (personalInfo) {
      values.student_id = personalInfo?.id;
    }
    await dispatch(addStudentGuardian(values)).then((response) => {
      if(response?.payload){
        setPortalAccess(false)
        form.resetFields()
      }
    })
  }
  const handleDelete = async (record) => {
    if (personalInfo) {
      const data = {
        student_id: personalInfo?.id,
        guardian_id: record.id
      }
      await dispatch(deleteGuardian(data))
    }
  }
  /** update student Guardian */
  const editStudentGuardian = (record) => {
    setGuardians(studentGuardians)
    form.resetFields()
    form.setFieldsValue(record)
    setPortalAccess(record.allow_portal_access)
  }

  /**check the portal Access */
  const checkPortalAccess = (e) => {
    setPortalAccess(e.target.value);
  }

  const guardianChange = (e) => {
    const singleGuardian = manageGuardianResult?.find((guardian) => guardian.id === e)
    form.setFieldsValue(singleGuardian)    
  }

  const makePrimaryContactGuardian = async (record) => {
    if (personalInfo) {
      const data = {
        student_id: personalInfo?.id,
        guardian_id: record.id
      }
      await dispatch(makePrimaryContact(data))
    }
  }

  const handleMenuClick = async (e, record) => {
    if (e.key === "makePrimary") {
     await makePrimaryContactGuardian(record)      
    }
    if (e.key === "updateGuardianInfo") {
      if(record?.guardian_id){
       await dispatch(getGuardianInStudentForEdit({ page: 1, perPage: 10, filter: isJSON({ id: record.guardian_id }), sorting: isJSON({ name: "asc" }) }))
      }
    }
  }
  const columns = [
    {
      title: setLocale("name"),
      key: "name",
      dataIndex: "name",
    },
    {
      title: setLocale("relationship.label"),
      dataIndex: "relation",
      key: "relation",
    },
    {
      title: setLocale("guardians.is_emergency_contact"),
      key: "isEmergencyContact",
      dataIndex: "isEmergencyContact",
    },
    {
      title: setLocale("guardians.is_authorized_to_pickup"),
      key: "isAuthorizedToPickup",
      dataIndex: "isAuthorizedToPickup",
    },
    {
      title: setLocale("guardians.portal_access"),
      key: "portalAccess",
      dataIndex: "portalAccess",
    },
    {
      title: setLocale("guardians.invoice_and_payment_notification"),
      key: "invoiceAndPaymentNotification",
      dataIndex: "invoiceAndPaymentNotification",
    },
    {
      title: setLocale("operation"),
      key: "action",
      render: (record) => (
        <>
          <Popconfirm title={setLocale("sure_to_delete")} onConfirm={(e) => handleDelete(record)}>
            <DeleteOutlined style={{ fontSize: "15px", cursor: "pointer" }} className="text-danger" />{" "} &nbsp;
          </Popconfirm>
          <EditOutlined style={{ fontSize: "15px" }} className="text-success mr-2" onClick={(e) => editStudentGuardian(record)} />

          { record?.primary_contact === 1 && <Tag color="green">{setLocale("Primary Contact")}</Tag> 
            // : <Button loading={StudentButtonSpinner} onClick={() => makePrimaryContactGuardian(record)} >
            //   Make Primary
            // </Button>
          }

          
            <Dropdown 
              overlay={
                <Menu onClick={(e) => handleMenuClick(e, record)}>
                { record?.primary_contact !== 1 &&   <Menu.Item key="makePrimary">{setLocale("Make Primary Contact")}</Menu.Item> }
                <Menu.Item key="updateGuardianInfo">{setLocale("Update Guardian Information")}</Menu.Item>
                </Menu>
              } trigger={['hover']}>
              <a onClick={(e) => e.preventDefault()}>
                More <DownOutlined />
              </a>
            </Dropdown>        
            <Spin size="small" spinning={ManageGuardianButtonSpinner} />
        </>
      ),
    },
  ];

  const handleSearch = debounce(async (value) => {
    if (value) {
      setGuardians([])
      await dispatch(getAllGuardians({ page: 1, perPage: 10000, filter: isJSON({ name: value }), sorting: isJSON({ name: "asc" }) }));
    }
  }, 300);

  useEffect(() => {
    if (manageGuardianResult) {
      setGuardians(manageGuardianResult)
    }
  }, [manageGuardianResult]);
  return (
    <>
      {loadStudentData ? (
        <>
          <Skeleton active />
          <Skeleton active />
        </>
      ) : (
        <>
          <Row gutter={8}>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Table
                dataSource={studentGuardians}
                columns={columns}
                rowKey={(record) => record.id}
                pagination={false}
              />
            </Col>
          </Row>
          <Divider orientation="left">
            {setLocale("guardians.add_guardian_information")}
          </Divider>
          <Row gutter={8}>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Form
                layout="vertical"
                onFinish={saveGuardianToStudent}
                autoComplete="off"
                form={form}
              >
                <Card title="">
                  <Row gutter={8}>
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                      <Space>
                        <Form.Item style={{ width: "300px" }} name="guardian_id" label={setLocale("guardians.select_guardian")}
                          rules={[
                            {
                              required: true,
                              message: setLocale("guardians.guardian_error"),
                            },
                          ]}
                          validateStatus={ StudentShowMessage && StudentErrors.guardian_id ? "error" : "" }
                          extra={
                            <span style={{ fontSize: "12px", color: "#888", opacity: "0.9", }} >
                              {" "} For Search(First Name,Last Name)
                              {StudentShowMessage && StudentErrors.guardian_id}
                            </span>
                          } >
                          <Select showSearch placeholder="Search..." onSearch={handleSearch}
                            notFoundContent={ManageGuardianTableLoading ? (<Spin size="small" />) : null
                            } filterOption={false} onChange={(e) => guardianChange(e)}>
                            { guardians && guardians.map((guardian, index) =>
                              <Option value={guardian.id} key={index} label={guardian.name}>
                                {`${guardian.name} ${guardian?.cell_phone ?? 'N/A'}`}
                              </Option>
                            )}
                          </Select>
                          {/* <Select showSearch optionLabelProp="label"
                            filterOption={(input, option) =>
                              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            } onChange={(e) => guardianChange(e)}>
                            {preLoadedStudentData?.guardians.map((guardian, index) =>
                              <Option value={guardian.id} key={index} label={guardian.name}>
                                {`${guardian.name} ${guardian?.cell_phone ?? 'N/A'}`}
                              </Option>
                            )}
                          </Select> */}
                        </Form.Item>
                        <Tooltip title="if there is no guardian! Add new">
                          <Typography.Link onClick={() => { handleOpenModal() }} >
                            {setLocale("guardians.add_new_record")}
                          </Typography.Link>
                        </Tooltip>
                      </Space>
                    </Col>
                  </Row>
                  <Row gutter={8}>
                    <Col xs={24} sm={24} md={5} lg={5} xl={5}>
                      <Form.Item
                        name="relationship_id"
                        label={setLocale("guardians.relationship")}
                        rules={[
                          {
                            required: true,
                            message: setLocale("guardians.relationship_error"),
                          },
                        ]}
                        validateStatus={
                          StudentShowMessage && StudentErrors.relationship_id
                            ? "error"
                            : ""
                        }
                        extra={
                          StudentShowMessage && StudentErrors.relationship_id
                        }
                        initialValue={preLoadedStudentData?.relationships?.find(rel => rel.is_default == 1)?.id}
                      >
                        <Select
                          showSearch
                          optionLabelProp="label"
                          filterOption={(input, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {preLoadedStudentData?.relationships.map(
                            (relation, index) => (
                              <Option
                                value={relation.id}
                                key={index}
                                label={relation.name}
                              >
                                {relation.name}
                              </Option>
                            )
                          )}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={24} md={5} lg={5} xl={5}>
                      <Form.Item
                        name="is_emergency_contact"
                        label={setLocale("guardians.is_emergency_contact")}
                        rules={[
                          {
                            required: true,
                            message: setLocale("guardians.option_error"),
                          },
                        ]}
                        validateStatus={
                          StudentShowMessage &&
                            StudentErrors.is_emergency_contact
                            ? "error"
                            : ""
                        }
                        extra={
                          StudentShowMessage &&
                          StudentErrors.is_emergency_contact
                        }
                      >
                        <Radio.Group>
                          <Radio value={1}>{setLocale("Yes")}</Radio>
                          <Radio value={0}>{setLocale("No")}</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={24} md={5} lg={5} xl={5}>
                      <Form.Item
                        name="is_authorized_to_pickup"
                        label={setLocale("guardians.is_authorized_to_pickup")}
                        rules={[
                          {
                            required: true,
                            message: setLocale("guardians.option_error"),
                          },
                        ]}
                        validateStatus={
                          StudentShowMessage &&
                            StudentErrors.is_authorized_to_pickup
                            ? "error"
                            : ""
                        }
                        extra={
                          StudentShowMessage &&
                          StudentErrors.is_authorized_to_pickup
                        }
                      >
                        <Radio.Group>
                          <Radio value={1}>Yes</Radio>
                          <Radio value={0}>No</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                      <Form.Item
                        name="allow_portal_access"
                        label={setLocale("guardians.portal_access")}
                        rules={[
                          {
                            required: true,
                            message: setLocale("guardians.option_error"),
                          },
                        ]}
                        validateStatus={
                          StudentShowMessage &&
                            StudentErrors.allow_portal_access
                            ? "error"
                            : ""
                        }
                        extra={
                          StudentShowMessage &&
                          StudentErrors.allow_portal_access
                        } >
                        <Radio.Group onChange={(e) => checkPortalAccess(e)}>
                          <Radio value={1}>{setLocale("Yes")}</Radio>
                          <Radio value={0}>{setLocale("No")}</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>

                    <Col xs={24} sm={24} md={5} lg={5} xl={5}>
                      <Form.Item
                        name="invoice_and_payment_notification"
                        label={setLocale("guardians.invoice_and_payment_notification")}
                        rules={[
                          {
                            required: true,
                            message: setLocale("guardians.option_error"),
                          },
                        ]}
                        validateStatus={
                          StudentShowMessage &&
                            StudentErrors.invoice_and_payment_notification
                            ? "error"
                            : ""
                        }
                        extra={
                          StudentShowMessage &&
                          StudentErrors.invoice_and_payment_notification
                        } >
                        <Radio.Group>
                          <Radio value={1}>{setLocale("Yes")}</Radio>
                          <Radio value={0}>{setLocale("No")}</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>
                    {portalAccess ? (
                      <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                        <Form.Item
                          name="email"
                          label={setLocale("guardians.email")}
                          rules={[
                            {
                              type: "email",
                              message: setLocale("guardians.valid_email_error"),
                            },
                            {
                              required: true,
                              message: setLocale("guardians.email_error"),
                            },
                          ]}
                          validateStatus={
                            StudentShowMessage && StudentErrors.email
                              ? "error"
                              : ""
                          }
                          extra={StudentShowMessage && StudentErrors.email}>
                          <Input />
                        </Form.Item>
                      </Col>
                    ) : null}
                  </Row>
                  <Row gutter={8}>
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                      <Space>
                        <Form.Item>
                          <Button
                            loading={StudentButtonSpinner}
                            type="primary"
                            htmlType="submit" >
                            {setLocale("save")}
                          </Button>
                        </Form.Item>
                      </Space>
                    </Col>
                  </Row>
                </Card>
              </Form>
            </Col>

            {/* Modal For new Guardian */}
            {ManageGuardianAddDrawer ? <AddManageGuardianModal student={true} /> : null}
          </Row>
        </>
      )}
    </>
  );
};

export default GuardianFields;

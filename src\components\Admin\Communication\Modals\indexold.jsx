import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { COMMUNICATION } from "constants/AppConstants";
import {
    CommunicationAddDrawerStatus,
    createCommunication,
    getCommunication
  } from "store/slices/Communication/manageCommunicationSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const { CommunicationAddDrawer, CommunicationButtonAndModelLabel, CommunicationErrors, CommunicationShowMessage, CommunicationButtonSpinner, CommunicationEditData, tablePagination } = useSelector(
        (state) => state[COMMUNICATION]
      );
const onClose = () => dispatch(CommunicationAddDrawerStatus(false));

const onSubmit = async (formValues) => {

  if (CommunicationEditData && CommunicationEditData.id) {
    // If editing, include the id in the form values
    formValues.id = CommunicationEditData.id;
  }

  await dispatch(createCommunication(formValues))
    .then(() => {
      if (Object.keys(CommunicationErrors).length == 0) {
        dispatch(getCommunication({
          page: tablePagination.current,
          perPage: tablePagination.pageSize
        }));
        // form.resetFields();
      }
    })
    .catch((error) => {
      // Handle delete error
      console.error("Error deleting module:", error);
    });
};

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={CommunicationButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CommunicationAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: CommunicationEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={CommunicationShowMessage && CommunicationErrors.name ? "error" : ""}
                help={CommunicationShowMessage && CommunicationErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CommunicationButtonSpinner}
            >
              {CommunicationButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


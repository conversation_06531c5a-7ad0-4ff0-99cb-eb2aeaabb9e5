import React from "react";
import { Breadcrumb } from 'antd';
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import 'moment-timezone';
import Filters from "./Filters";
import InvoiceAndPaymentStates from "./InvoiceAndPaymentStates";
import RevenueAndPaymentStatus from "./RevenueAndPaymentStatus";
import TotalChargesAndBreakDown from "./TotalChargesAndBreakDown";
import InvoicesTable from "./InvoicesTable";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Invoices Dashboard')}</Breadcrumb.Item>
            </Breadcrumb>
            <Filters />

            <InvoiceAndPaymentStates />

            <RevenueAndPaymentStatus />

            <TotalChargesAndBreakDown />

            <InvoicesTable />

        </>
    );
}

export default Index;

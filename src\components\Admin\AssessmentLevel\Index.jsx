import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Switch, Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { ASSESSMENT_LEVEL } from "constants/assessment-levels/index.js";
import AddAssessmentLevelModal from "./Modals/index";
// import { GRADE_LEVEL } from "constants/AppConstants";
import {
    AssessmentLevelAddDrawerStatus,
    AssessmentLevelEditWithDrawerStatus,
    deleteAssessmentLevel,
    getAssessmentLevel,
    updateSortFilters,
    setColumnSearch,
} from "store/slices/AssessmentLevel/manageAssessmentLevelSlice.js";
// import {
//     getPreLoadGradingData
// } from "store/slices/StudentGrading/manageStudentGradingSlice.js";
import {
    getGradeLevel
} from "store/slices/GradeLevel/manageGradeLevelSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(AssessmentLevelAddDrawerStatus({ errorStatus: 1, status: true }));
    const {
        AssessmentLevelAddDrawer,
        AssessmentLevelResult,
        tablePagination, sorting, filter,
        AssessmentLevelTableLoading, permission
    } = useSelector((state) => state[ASSESSMENT_LEVEL]);


    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getAssessmentLevel({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(1, tablePagination.pageSize, filter, sorting);
        dispatch(getGradeLevel({ page: 1, perPage: 1000000, filter: {}, sorting: {} }));
        //  dispatch(getPreLoadGradingData())
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };

    const handleDelete = (record) => {
        dispatch(deleteAssessmentLevel(record.id)).then(() => {
            getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        })
    }
    const handleUpdate = (record) => {
        dispatch(AssessmentLevelEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) {
            console.log(error);
        }

    };

    const columns = [
        {
            title: setLocale('name'),
            dataIndex: "name",
            key: "name",
            sorter: true,
            ...getColumnSearchProps('name'),
        },
        {
            title: setLocale('Grade'),
            dataIndex: "grades",
            key: "grades",
        },
        {
            title: setLocale('Category'),
            dataIndex: "category",
            key: "category",
        },
        {
            title: setLocale('Show Comments'),        
            key: "show_comments",
            render: (data, record) => {
                return (
                    <Switch disabled checkedChildren="Yes" unCheckedChildren="No" checked={record.show_comments} />
                )
            }
        },
        {
            title: setLocale('Description'),
            key: "description",
            render: (data, record) => {
                return (
                    <div dangerouslySetInnerHTML={{ __html: record.description }} />
                )
            }
        },
        {
            title: setLocale('createdAt'),
            dataIndex: "created_at",
            key: "created_at",
            sorter: true,
            ...getColumnSearchProps('created_at'),
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                    {/* {permission.includes("View") && (
                        <Link to={`../../app/assessment-level_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Assessment Level')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button className="ant-btn-round ant-btn-sm" type="primary" style={{ float: "right", margin: "5px" }} onClick={handleOpenModal} >
                                {setLocale('assessmentlevel.add')}
                            </Button>
                        )}
                    </section>
                    {AssessmentLevelAddDrawer && <AddAssessmentLevelModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={AssessmentLevelTableLoading}
                            rowKey={record => record.id}
                            dataSource={AssessmentLevelResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}

                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

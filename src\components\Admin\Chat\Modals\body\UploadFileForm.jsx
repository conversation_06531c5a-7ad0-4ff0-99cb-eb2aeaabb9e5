import React, { useRef, useState, useEffect } from 'react'
// import moment from 'moment';
import moment from 'moment-timezone';
import { useSelector, useDispatch } from "react-redux";
import { CHAT } from "constants/chat/index";
import { Avatar, message, Drawer, Upload, Input, Form, Button, Menu } from 'antd';
import {
	SendOutlined,
	UploadOutlined,
	PaperClipOutlined, 
	SmileOutlined, 
	InboxOutlined,
	PlusOutlined
} from '@ant-design/icons';
import {
	sendMessage,
	setPushNewMessage,
	setUploadFileFormDrawer
} from "store/slices/Chat/manageChatSlice.js";
import { USER_INFORMATION } from "constants/AuthConstant";
import { DEFAULT_TIME_ZONE } from "constants/DateConstant";
const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION)) || null;
const { Dragger } = Upload;


const UploadFileForm = () => {
	const dispatch = useDispatch();
	const { 
		selectedConversation,
		uploadFileFormDrawer
	} = useSelector((state) => state[CHAT]);
	const formRef = useRef();	
	const [fileList, setFileList] = useState([]);	

	useEffect(() => {
		setFileList([]);
		formRef.current.setFieldsValue({
			message: ''
		});		
	}, []);

	const onSendFileForm = async (values) => {
		if (fileList.length === 0) {
			message.warning('Please select files before uploading.');
			return;
		}

		const formData = new FormData();
		fileList.forEach(file => {
			formData.append('files[]', file.originFileObj);
		});
		formData.append('conversation_id', selectedConversation.conversation_id);
		formData.append('user_id', currentUser.id);
		formData.append('type', 'file');
		formData.append('created_at', moment().tz(DEFAULT_TIME_ZONE).format('MMM D, YYYY hh:mm A'));
		
		if (values.message) {
			formData.append('message', values.message);			
		}
		const newMsgData = {
			conversation_id: selectedConversation.conversation_id,
			type: "file",
			message: values?.message || '',
			created_at: moment().tz(DEFAULT_TIME_ZONE).format('MMM D, YYYY hh:mm A'),
			user_id: currentUser.id,			
			name: currentUser.name,
			files: mappedFiles,
			remove: 'yes'
		};		
		await dispatch(setPushNewMessage(newMsgData));
		formRef.current.setFieldsValue({
			message: ''
		});
		setFileList([]);
		onClose();
		await dispatch(sendMessage(formData));		
		
	}
	const onClose = () => {
		dispatch(setUploadFileFormDrawer(false));
	};

	
	const props = {
		multiple: true,
		fileList,
		showUploadList: true,
		beforeUpload: (file, fileList) => {
			const isLt5MB = file.size / 1024 / 1024 < 5;
			if (!isLt5MB) {
			message.error(`${file.name} is larger than 5MB.`);
			return Upload.LIST_IGNORE;
			}
			// If limit exceeded after selecting, ignore extras
			if (fileList.length > 4) {
			message.warning('You can only upload up to 4 files.');
			return Upload.LIST_IGNORE;
			}
			return false; // Prevent auto upload
		},
		onChange: info => {
			let newFiles = info.fileList.slice(0, 4); // Just in case
			setFileList(newFiles);
		},
		onDrop: e => {
			console.log('Dropped files', e.dataTransfer.files);
		}
	};

	const footerInput = (
		<Form.Item name="message" className="mb-0 mt-5">			
			<Input
				autoComplete="off"
				placeholder="Add caption..."
				suffix={
					<div className="d-flex align-items-center">							
						<Button shape="circle" type="primary" size="small" htmlType="submit">
							<SendOutlined />
						</Button>
					</div>
				}
			/>
		</Form.Item>
	);
	const mappedFiles = fileList.map(file => {
		// const fileObj = file.originFileObj;
		return {
		  name: file.name,
		  size: 0,
		  type: file.type,		  
		  path: '',
		};
	});

	return (
		<>
		<Form name="msgInput" ref={formRef} onFinish={onSendFileForm} className="w-100" onKeyPress={(e) => e.key === 'Enter' && onSendFileForm() }>
			<Drawer title={selectedConversation.name} placement="bottom"
				width="100%" height="100%" closable={true} onClose={onClose} open={uploadFileFormDrawer} getContainer={false}
				style={{ position: 'absolute', marginLeft: 8, marginRight: 8 }}
				headerStyle={{ borderBottom: 'none' }} 
				footerStyle={{ borderTop: 'none' }}
				footer={footerInput}>											

					<Dragger 						
						{...props}
						style={{ width: '50%', margin: '0 auto', maxHeight: '100px' }}>
						<p className="ant-upload-drag-icon"> <InboxOutlined /> </p>
						<p className="ant-upload-text">Click or drag file to this area</p>
						<p className="ant-upload-hint"> Support for up to 4 files, max 5MB each.</p>
					</Dragger>
			</Drawer>
		</Form>
		</>
	)
}

export default UploadFileForm

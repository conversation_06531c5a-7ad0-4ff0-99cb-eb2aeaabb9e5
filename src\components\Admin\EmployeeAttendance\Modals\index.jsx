import React, { useEffect } from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space, DatePicker, Select, Radio } from "antd";
import dayjs from 'dayjs';
import { useDispatch, useSelector } from "react-redux";
import { EMPLOYEE_ATTENDANCE } from "constants/AppConstants";
import {
  EmployeeAttendanceAddDrawerStatus,
  createEmployeeAttendance,
  getEmployeeAttendance,
  onCloseError,
  getAllUsers
} from "store/slices/EmployeeAttendance/manageEmployeeAttendanceSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import moment from 'moment'
import { USER_INFORMATION } from 'constants/AuthConstant'
import { isJSON } from "components/composeable"

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const userOrganizations = userInformation.organizations;

const { TextArea } = Input;

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {
    EmployeeAttendanceAddDrawer,
    EmployeeAttendanceButtonAndModelLabel,
    sorting,
    filter,
    EmployeeAttendanceErrors,
    EmployeeAttendanceShowMessage,
    EmployeeAttendanceButtonSpinner,
    EmployeeAttendanceEditData,
    tablePagination,
    DrawerStatus,
    UserResult,
    UserResultLoading
  } = useSelector((state) => state[EMPLOYEE_ATTENDANCE]);

  const onClose = () => {
    dispatch(EmployeeAttendanceAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }

  const onSubmit = async (formValues) => {
    if (EmployeeAttendanceEditData && EmployeeAttendanceEditData.id) {
      // If editing, include the id in the form values
      formValues.id = EmployeeAttendanceEditData.id;
    }

    const formattedValues = {
      ...formValues,
      att_date_time: moment(formValues.att_date_time).format('YYYY-MM-DD HH:mm:ss')
    };

    await dispatch(createEmployeeAttendance(formattedValues));
  };

  useEffect(() => {
    if (Object.keys(EmployeeAttendanceErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getEmployeeAttendance({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [EmployeeAttendanceErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const onOrganizationChange = (option) => {
    const filterData = JSON.stringify({ organization_id: option });
    dispatch(getAllUsers({ filter: isJSON(filterData) }));
  }

  const options = [
    ...UserResult?.map(user => ({
      value: user.id,
      label: user.name,
    })),
  ];

  const org_options = userOrganizations?.length > 1
    ? [
      { value: 'all', label: 'All' },
      ...userOrganizations.map(org => ({
        value: org.id,
        label: org.org_name,
      })),
    ]
    : userOrganizations?.map(org => ({
      value: org.id,
      label: org.org_name,
    })) || [];

  return (
    <>
      <Drawer
        title={EmployeeAttendanceButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={EmployeeAttendanceAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            employee_id: EmployeeAttendanceEditData?.user_id ? EmployeeAttendanceEditData.user_id : null,
            att_date_time: EmployeeAttendanceEditData?.att_date_time ? moment(EmployeeAttendanceEditData?.att_date_time, 'YYYY-MM-DD HH:mm:ss') : null,
            status: EmployeeAttendanceEditData?.status === 1 ? 1 : 0
          }}
        >
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="organization_id"
                label={setLocale('Organization')}
                rules={[
                  {
                    required: true,
                    message: setLocale('attendance.organizationError'),
                  },
                ]}
                validateStatus={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.organization_id ? "error" : ""}
                help={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.organization_id}
              >
                <Select
                  showSearch
                  optionLabelProp="label"
                  optionFilterProp="children"
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  options={org_options}
                  onChange={onOrganizationChange}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="employee_id"
                label={setLocale('Employee')}
                rules={[
                  {
                    required: true,
                    message: setLocale('attendance.employeeError'),
                  },
                ]}
                validateStatus={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.id ? "error" : ""}
                help={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.id}
              >
                <Select
                  showSearch
                  optionLabelProp="label"
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  options={options}
                  loading={UserResultLoading}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="att_date_time"
                label={setLocale('Time')}
                rules={[
                  {
                    required: true,
                    message: setLocale('attendance.attTimeError'),
                  },
                ]}
                validateStatus={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.att_date_time ? "error" : ""}
                help={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.att_date_time}
              >
                <DatePicker
                  showTime
                  onChange={(value, dateString) => {
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="status"
                label={setLocale('Check')}
                rules={[
                  {
                    required: true,
                    message: setLocale('attendance.statusError'),
                  },
                ]}
                validateStatus={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.status ? "error" : ""}
                help={EmployeeAttendanceShowMessage && EmployeeAttendanceErrors.status}
              >
                <Radio.Group
                  block
                  options={[
                    {
                      label: setLocale('attendance.CheckIn'),
                      value: 1,
                    },
                    {
                      label: setLocale('attendance.CheckOut'),
                      value: 0,
                    }
                  ]}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Form.Item>
            </Col>
          </Row>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={EmployeeAttendanceButtonSpinner}
            >
              {EmployeeAttendanceButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );

};
export default Index;


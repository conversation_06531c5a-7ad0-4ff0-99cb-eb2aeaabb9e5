import { Select } from 'antd'
import React from 'react'

export const Days = ({onChange, value}) => {
  const { Option } = Select;
  
  // Generate array of days from 1 to 30
  const days = Array.from({length: 30}, (_, i) => i + 1);

  return (
    <Select
      placeholder="Select Days"
      style={{ minWidth: 120, width: '100%' }}
      onChange={onChange}
      value={value ?? null}

    >
      {days.map(day => (
        <Option key={day} value={day}>{day}</Option>
      ))}
    </Select>
  )
}

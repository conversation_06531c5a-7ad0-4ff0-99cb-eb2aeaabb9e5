import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Tag, Divider, Breadcrumb, Switch } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { CITY } from "constants/AppConstants";
import AddCityModal from "./Modals/index";
import {
    CityAddDrawerStatus,
    CityEditWithDrawerStatus,
    createCity,
    deleteCity,
    getCity,
    updateSortFilters,
    setColumnSearch
} from "store/slices/City/manageCitySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const [searchText, setSearchText] = useState('');
    const [searchedColumn, setSearchedColumn] = useState('');
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(CityAddDrawerStatus({ errorStatus: 1, status: true }));
    const { CityAddDrawer, CityResult, tablePagination, sorting, filter,CityErrors, CityTableLoading, permission } = useSelector(
        (state) => state[CITY]
    );
    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getCity({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }
    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };
    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };
    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };
    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);

    useEffect(() => {
        if (Object.keys(CityErrors).length === 0) {
          dispatch(getCity({
            page: tablePagination.current,
            perPage: tablePagination.pageSize,
            filter: filter, sorting: sorting
          }));
        }
    }, [CityErrors]);

    const handlePageChange = (page, pageSize) => {
        dispatch(getCity({ page: page, perPage: pageSize, filter: filter, sorting: sorting }));
    };

    const handleDelete = (record) => {
        dispatch(deleteCity(record.id)).then(() => {
            dispatch(getCity({ page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting }));
        })
    }
    const handleUpdate = (record) => {
        dispatch(CityEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            await dispatch(
                getCity({
                    page: 1,
                    perPage: tablePagination.pageSize,
                    filter: filter,
                    sorting: sorting,
                })
            );
        } catch (error) {
            console.log(error);
        }

    };

    const getSwitchValue = async (checked,record) => {
        await dispatch(createCity({
            is_default:checked,
            id:record.id,
            name:record.name
        }));
    };

    const columns = [
        {
            title: setLocale("name"),
            dataIndex: "name",
            key: "name",
            sorter: true,
            ...getColumnSearchProps('name'),
        },
        {
            title: setLocale('city.default'),
            key: "is_default",
            sorter: true,
            render: (record) => (
                <Switch
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    onChange={(checked) => getSwitchValue(checked, record)}
                    checked={record.is_default === 1 ? true : false}
                    name="is_default"
                />
            )
        },
        {
            title: "Action",
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('city.title')}</Breadcrumb.Item>
            </Breadcrumb>
            <div className="code-box">
                <section className="code-box-demo">
                    {permission.includes("Create") && (
                        <Button
                            className="ant-btn-round ant-btn-sm"
                            type="primary"
                            style={{ float: "right", margin: "5px" }}
                            onClick={handleOpenModal}
                        >
                            {setLocale("city.add")}
                        </Button>
                    )}
                </section>
                {CityAddDrawer && <AddCityModal />}
                <section className="code-box-description">
                    <Table
                        onChange={handleTableChange}
                        columns={columns}
                        loading={CityTableLoading}
                        rowKey={record => record.id}
                        dataSource={CityResult.data ?? []}
                        pagination={false}
                    />
                    <Pagination
                        style={{ margin: '16px', float: 'right' }}
                        current={tablePagination.current}
                        pageSize={tablePagination.pageSize}
                        total={tablePagination.total}
                        showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                        pageSizeOptions={['10', '20', '50', '100', '1000']}
                        showQuickJumper
                        onChange={handlePageChange}
                    />
                </section>
            </div>
        </>
    );
}

export default Index;

import React, { useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Row, Col, Table, Select, Button, Input, Space, Pagination, Card } from 'antd';
import { DownloadOutlined, SearchOutlined, EyeOutlined } from "@ant-design/icons";
import {
  detailAnnualInvoices,
  annualDrawerStatus,
  updateAnnualFilters,
  setAnnualColumnSearch,
  downloadInvoice
} from "store/slices/Invoices/manageInvoicesSlice";
import {
  ArrowLeftOutlined
} from "@ant-design/icons";
import { videPDF } from "store/slices/Batches/manageBatchesSlice.js";
import { INVOICES } from "constants/AppConstants";
import { Link } from "react-router-dom";
import IntlMessage from "components/util-components/IntlMessage";
import Highlighter from 'react-highlight-words';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
function AnnualInvoicesStatus() {

  const dispatch = useDispatch();
  const searchInput = useRef(null);
  const {
    annualFilter,
    annualSorting,
    detailAnnualInvoice,
    DetailAnnualInvoicesLoading,
    annualTablePagination
  } = useSelector((state) => state[INVOICES]);


  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(detailAnnualInvoices({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
  }

  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, annualTablePagination.pageSize, annualFilter, annualSorting);
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = annualFilter;
    await dispatch(setAnnualColumnSearch(newObject));
    getModuleData(annualTablePagination.current, annualTablePagination.pageSize, newObject, annualSorting);
  };

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setAnnualColumnSearch({ ...annualFilter, [dataIndex]: value }));
    if (value === '') {
      confirm();
      getModuleData(annualTablePagination.current, annualTablePagination.pageSize, { ...annualFilter, [dataIndex]: value }, annualSorting);
    }
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={annualFilter[dataIndex] ? annualFilter[dataIndex] : selectedKeys[0]}
          onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: 'block',
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('search')}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex)
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('reset')}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: annualFilter[dataIndex] && annualFilter[dataIndex] !== '' ? '#1677ff' : undefined,
        }}
      />
    ),
    onFilter: (value, record) => {
      if (dataIndex === 'full_name' || dataIndex === 'grade_level' || dataIndex === 'batch_no') {
        const columnValue = dataIndex.split('.').reduce((acc, key) => acc[key], record);
        return columnValue ? columnValue.toString().toLowerCase().includes(value.toLowerCase()) : false;
      } else {
        return record[dataIndex].toString().toLowerCase().includes(value.toLowerCase());
      }
    },
    render: (text) =>
      annualFilter[dataIndex]
        ? (
          <Highlighter
            highlightStyle={{
              backgroundColor: '#ffc069',
              padding: 0,
            }}
            searchWords={[annualFilter[dataIndex]]}
            autoEscape
            textToHighlight={text ? text.toString() : ''}
          />
        ) : (
          text
        ),
  });

  useEffect(() => {
    getModuleData(annualTablePagination.current, annualTablePagination.pageSize, annualFilter, annualSorting);
  }, []);


  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, annualFilter, annualSorting);
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
    };

    try {
      await dispatch(updateAnnualFilters({ filter: annualFilter, sorting: sorting }));
      getModuleData(1, annualTablePagination.pageSize, annualFilter, sorting);
    } catch (error) {
      console.log(error);
    }

  };

  const columns = [
    {
      title: setLocale('invoices.student_name'),
      dataIndex: 'full_name',
      key: 'full_name',
      fixed: 'left',
      sorter: true,
      ...getColumnSearchProps('full_name'),
      render: (text, record) => (
        <>
          <Link to={`/app/student_view/${record.student_enc_id}`}>{record.full_name}</Link>
        </>
      ),
    },
    {
      title: setLocale('invoices.grade_level'),
      dataIndex: 'grade_level',
      key: 'grade_level',
      fixed: 'left',
      sorter: true,
      ...getColumnSearchProps('grade_level'),
    },
    {
      title: setLocale('invoices.invoice'),
      dataIndex: 'invoice_no',
      key: 'invoice_no',
      sorter: true,
      ...getColumnSearchProps('invoice_no'),
    },
    {
      title: setLocale('invoices.invoice_batch'),
      key: 'batch_no',
      dataIndex: 'batch_no',
      sorter: true,
      ...getColumnSearchProps('batch_no'),
    },
    {
      title: setLocale('invoices.invoice_date'),
      dataIndex: 'invoice_date',
      key: 'invoice_date',
      sorter: true,
      ...getColumnSearchProps('invoice_date'),
    },
    {
      title: setLocale('invoices.status'),
      key: 'invoice_status_code',
      dataIndex: 'invoice_status_code',
      fixed: 'right',
      sorter: true,
      ...getColumnSearchProps('invoice_status_code'),
    },
    {
      title: setLocale('invoices.detail'),
      key: 'detail',
      fixed: 'right',
      render: (data, record) => (
        <>
          <EyeOutlined
            style={{ fontSize: '15px', marginRight: '10px' }}
            className="text-success"
            onClick={(e) => dispatch(videPDF(record))}
          />
          {record.invoice_file_name && <>
            <DownloadOutlined style={{ fontSize: '15px' }} className="text-success" onClick={(e) => dispatch(downloadInvoice({ invoice_file_name: record.invoice_file_name }))} />
          </>}
        </>
      )
    },
  ];

  return (
    <>
      <Card
        title={
          <h5>
            <span style={{ cursor: "pointer", float: "right" }}
              onClick={() => {
                dispatch(annualDrawerStatus({ drawerStatus: false, mainStatus: true }))
              }}
            >
              <ArrowLeftOutlined /> {setLocale('invoices.status_annual_invoices')}
            </span>{" "}
          </h5>
        }
      >
        <Row gutter={16} className="my-5">
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
            <Table
              onChange={handleTableChange}
              columns={columns}
              dataSource={detailAnnualInvoice ?? []}
              loading={DetailAnnualInvoicesLoading}
              pagination={false}
              rowKey={record => record.id}
            />
            <Pagination
              style={{ margin: '16px', float: 'right' }}
              current={annualTablePagination.current}
              pageSize={annualTablePagination.pageSize}
              total={annualTablePagination.total}
              pageSizeOptions={['10', '20', '50', '100', '1000']}
              onChange={handlePageChange}
              showQuickJumper
            />
          </Col>
        </Row>
      </Card>
    </>
  );
}

export default AnnualInvoicesStatus;

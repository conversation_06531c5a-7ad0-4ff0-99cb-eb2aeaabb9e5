import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    Table,
    // Popconfirm, 
    // Breadcrumb, 
    Pagination,
    Button, Input,
    Space,
    Tag
} from 'antd';
import {
    // DeleteOutlined, 
    // EditOutlined, 
    SearchOutlined,
    EyeOutlined
} from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { PURCHASE_REQUESTS } from "constants/purchase-requests/index.js";
import {
    // PurchaseRequestsAddDrawerStatus,
    getPurchaseRequests,
    updateSortFilters,
    setColumnSearch,
    setPurchaseRequestsViewDrawer
} from "store/slices/PurchaseRequests/managePurchaseRequestsSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { getColor, makeCapitilize } from "components/composeable/index";
import PurchaseRequestDetail from "./Modals/PurchaseRequestDetail";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function PurchaseRequests() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    // const handleOpenModal = () => dispatch(PurchaseRequestsAddDrawerStatus({ errorStatus: 1, status: true }));
    const {
        // PurchaseRequestsAddDrawer, 
        PurchaseRequestsResult,
        tablePagination,
        sorting, filter,
        PurchaseRequestsTableLoading,
        permission,
        purchaseRequestViewDrawer
    } = useSelector((state) => state[PURCHASE_REQUESTS]);

    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getPurchaseRequests({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };
    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) { }

    };
    const handleView = (record) => {
        dispatch(setPurchaseRequestsViewDrawer({ status: true, data: record }));
    }


    const columns = [
        {
            title: setLocale('purchaserequests.userName'),
            dataIndex: "user_name",
            key: "user_name",
            sorter: true,
            ...getColumnSearchProps('user_name'),
        },
        {
            title: setLocale('purchaserequests.request_date'),
            dataIndex: "request_date",
            key: "request_date",
            sorter: true,
            ...getColumnSearchProps('request_date'),
        },
        {
            title: setLocale('purchaserequests.designation'),
            dataIndex: "designation",
            key: "designation",
            sorter: true,
            ...getColumnSearchProps('designation'),
        },
        {
            title: setLocale('purchaserequests.priority'),
            dataIndex: "priority",
            key: "priority",
            sorter: true,
            ...getColumnSearchProps('priority'),
            render: (data, record) => (
                <>
                    <Tag color={getColor(record.priority)}>{makeCapitilize(record.priority)}</Tag>
                </>
            )
        },
        {
            title: setLocale('purchaserequests.vender_supplier'),
            dataIndex: "vender_supplier",
            key: "vender_supplier",
            sorter: true,
            ...getColumnSearchProps('vender_supplier'),
        },
        {
            title: setLocale('purchaserequests.gradeLevel'),
            dataIndex: "grade_level",
            key: "grade_level",
            sorter: true,
            ...getColumnSearchProps('grade_level'),
        },
        {
            title: setLocale('purchaserequests.class'),
            dataIndex: "class",
            key: "class",
            sorter: true,
            ...getColumnSearchProps('class'),
        },
        {
            title: setLocale('purchaserequests.status'),
            dataIndex: "status",
            key: "status",
            sorter: true,
            ...getColumnSearchProps('status'),
            render: (data, record) => (
                <>
                    <Tag color={getColor(record.status)}>{makeCapitilize(record.status)}</Tag>
                </>
            )
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("View") && (
                        <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" onClick={(e) => handleView(record)} />
                    )}
                </>
            )
        },
    ];

    return (
        <>
            {purchaseRequestViewDrawer && <PurchaseRequestDetail />}
            <Table
                onChange={handleTableChange}
                columns={columns}
                style={{ width: '100%', overflowX: 'scroll' }}
                loading={PurchaseRequestsTableLoading}
                rowKey={record => record.id}
                dataSource={PurchaseRequestsResult.data ?? []}
                pagination={false}
            />
            <Pagination
                style={{ margin: '16px', float: 'right' }}
                current={tablePagination.current}
                pageSize={tablePagination.pageSize}
                total={tablePagination.total}
                showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                pageSizeOptions={['10', '20', '50', '100', '1000']}
                showQuickJumper
                onChange={handlePageChange}
            />
        </>
    );
}

export default PurchaseRequests;

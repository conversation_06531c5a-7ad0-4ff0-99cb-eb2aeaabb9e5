import { Select } from 'antd'
import React from 'react'

const ALLOCATION_TYPES = [
  { value: "designation", label: "Designation" },
  { value: "employment_length", label: "Employment Length" },
];

export const AllocationType = ({value, onChange}) => {
const { Option } = Select;
  return (
    <Select
    value={value}
    placeholder="Allocation Type"
    style={{ minWidth: 120, width: '100%' }}
    onChange={onChange}
  >
    {ALLOCATION_TYPES.map(leave => (
      <Option key={leave.value} value={leave.value}>{leave.label}</Option>
    ))}
  </Select>
  )
}

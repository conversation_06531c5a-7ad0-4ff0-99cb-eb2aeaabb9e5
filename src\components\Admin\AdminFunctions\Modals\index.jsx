import React,{useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ADMIN_FUNCTIONS } from "constants/AppConstants";
import {
    AdminFunctionsAddDrawerStatus,
    createAdminFunctions,
    getAdminFunctions,
    onCloseError
  } from "store/slices/AdminFunctions/manageAdminFunctionsSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        AdminFunctionsAddDrawer,
        AdminFunctionsButtonAndModelLabel,
        sorting,
        filter,
        AdminFunctionsErrors,
        AdminFunctionsShowMessage,
        AdminFunctionsButtonSpinner,
        AdminFunctionsEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[ADMIN_FUNCTIONS]
    );
const onClose = () => {
    dispatch(AdminFunctionsAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (AdminFunctionsEditData && AdminFunctionsEditData.id) {
    // If editing, include the id in the form values
    formValues.id = AdminFunctionsEditData.id;
  }

  await dispatch(createAdminFunctions(formValues));
};

  useEffect(() => {
    if (Object.keys(AdminFunctionsErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getAdminFunctions({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [AdminFunctionsErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={AdminFunctionsButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={AdminFunctionsAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: AdminFunctionsEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={AdminFunctionsShowMessage && AdminFunctionsErrors.name ? "error" : ""}
                help={AdminFunctionsShowMessage && AdminFunctionsErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AdminFunctionsButtonSpinner}
            >
              {AdminFunctionsButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useEffect , useState } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Switch } from "antd";
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from "react-redux";
import { ADDRESS_TYPE } from "constants/AppConstants";
import {
    AddressTypeAddDrawerStatus,
    createAddressType,
    getAddressType,
    onCloseError
  } from "store/slices/AddressType/manageAddressTypeSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {DrawerStatus,filter, sorting, AddressTypeAddDrawer, AddressTypeButtonAndModelLabel, AddressTypeErrors, AddressTypeShowMessage, AddressTypeButtonSpinner, AddressTypeEditData, tablePagination } = useSelector(
        (state) => state[ADDRESS_TYPE]
  );
const [switchValue, setSwitchValue] = useState('checked');
const onClose = () => {
    dispatch(AddressTypeAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (AddressTypeEditData && AddressTypeEditData.id) {
    // If editing, include the id in the form values
    formValues.id = AddressTypeEditData.id;
    formValues.is_default = AddressTypeEditData.is_default == 1 ? true : false;
  }

  await dispatch(createAddressType(formValues))
};
useEffect(() => {
    if (Object.keys(AddressTypeErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getAddressType({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [AddressTypeErrors]);

  useEffect(() => {
    AddressTypeEditData.is_default === 1 ? setSwitchValue('checked') : setSwitchValue();
  }, [dispatch]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

const getSwitchValue = (checked) => {
  setSwitchValue(checked);
};

  return (
    <>

      <Drawer
        title={AddressTypeButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={AddressTypeAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            address_type: AddressTypeEditData?.address_type,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="address_type"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={AddressTypeShowMessage && AddressTypeErrors.address_type ? "error" : ""}
                help={AddressTypeShowMessage && AddressTypeErrors.address_type}
              >
                <Input />
              </Form.Item>
            </Col>
            {!(AddressTypeEditData && AddressTypeEditData.id) &&
              <Col span={12}>
                <Form.Item
                  name="is_default"
                  // valuePropName={switchValue}
                  label="Is Default"
                  rules={[
                    {
                      required: false,
                      message: "Status Error",
                    },
                  ]}
                  validateStatus={AddressTypeShowMessage && AddressTypeErrors.is_default ? "error" : ""}
                      extra={AddressTypeShowMessage && AddressTypeErrors.is_default}


                >
                  <Switch
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    onChange={getSwitchValue}
                    checked={switchValue}
                    name="is_default"
                  />
                </Form.Item>
              </Col>
            }

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AddressTypeButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


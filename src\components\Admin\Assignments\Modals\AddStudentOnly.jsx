import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { Select, Drawer, Modal, message, Space, Row, Col, Button, Form, Input } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
    setAddStudentOnlyModal,
    addMoreStudentsIntoAssignment
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import IntlMessage from "components/util-components/IntlMessage"
import { MANAGE_CLASS } from 'constants/AppConstants'

const { Option } = Select;
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AddStudentOnly() {
    const [messageApi, contextHolder] = message.useMessage();
    const [filteredClassStudents, setFilteredClassStudents] = useState([]);

    const openNotificationWithIcon = (msg, type='error') => {
        messageApi.open({
            type,
            content: msg,
            style: { marginTop: '90vh',  },
        });
    }

    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const { singleAssignment, addStudentOnlyModal, editData, showMessage, errors, saveStudentOnlyBtn, openModal, moduleLabel } = useSelector((state) => state[ASSIGNMENT_SLICE]);    
    const { ClassStudentsData } = useSelector(state => state[MANAGE_CLASS]);
    useEffect(() => {
        if (singleAssignment?.assignees && ClassStudentsData) {
            const assigneeIds = singleAssignment.assignees.map((assignee) => assignee.class_student_id); 
            const filtered = ClassStudentsData.filter(
                (student) => !assigneeIds.includes(student.id) 
            );
            setFilteredClassStudents(filtered);
        }        
    }, [])

    const onFinish = async (values) => {        
        if (!values.class_student_ids || values.class_student_ids.length === 0) {
            openNotificationWithIcon(`Please add at least one student!`);
            return false
        }
        if (singleAssignment) { 
            values.id = singleAssignment?.id; 
            await dispatch(addMoreStudentsIntoAssignment(values))     
        }       
    }
    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };

    return (
        <>
        {contextHolder}      
        <Drawer title={setLocale('Add Student')} width={'30%'}
            onClose={() => dispatch(setAddStudentOnlyModal(false))} open={addStudentOnlyModal}
            maskClosable={false} bodyStyle={{ paddingBottom: 80 }} zIndex={1002}>
                   
            <Form layout="vertical" onFinish={onFinish} onFinishFailed={onFinishFailed} form={form} encType="multipart/form-data" >                 
                <Row gutter={16}>                               
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                    <Form.Item name="class_student_ids" label={setLocale('assignment.selectStudent')}
                        rules={[
                            {
                                required: true,
                                message: setLocale('assignment.studentRequired'),
                            },
                        ]} validateStatus={showMessage && errors.class_student_ids ? "error" : ""}
                        extra={showMessage && errors.class_student_ids}>
                        <Select mode='multiple' className='rounded-0'
                            placeholder={setLocale('assignment.selectStudent')}                        
                            maxTagCount={1}
                            optionLabelProp="label"
                            filterOption={(inputValue, option) =>
                                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                            }
                            showSearch
                            onChange={(values) => {
                                if (values.includes('select-all')) {
                                    if (filteredClassStudents && filteredClassStudents.length < form.getFieldValue('class_student_ids').length) {
                                        form.setFieldsValue({
                                            class_student_ids: []
                                        });
                                    } else {
                                        form.setFieldsValue({
                                            class_student_ids: filteredClassStudents.map(student => student.id),
                                        });
                                    }
                                }
                            }}>
                            <Option value="select-all" key="select-all" label="Select All"> Select All</Option>
                            {filteredClassStudents ? filteredClassStudents.map((classStudent, index) => (
                                <Option value={classStudent.id} key={classStudent.id} label={classStudent.full_name}>
                                    {classStudent.full_name}
                                </Option>
                            )) : null}
                        </Select>
                    </Form.Item>
                    </Col>            
                </Row>
                <Row>
                    <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                        <Form.Item label="">
                            <Button className='rounded-0' loading={saveStudentOnlyBtn} type="primary" htmlType="submit">
                                {setLocale('save')}
                            </Button>
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Drawer> 
        </>
    )
}

export default AddStudentOnly;

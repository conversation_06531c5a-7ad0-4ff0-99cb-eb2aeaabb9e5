import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  Popconfirm,
  Pagination,
  Button,
  Input,
  Space,
  Breadcrumb,
  Row,
  Col,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  SearchOutlined
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { LEAVE_ALLOCATION } from "constants/AppConstants";
import AddLeaveAllocationModal from "./Modals/index";
import {
  LeaveAllocationAddDrawerStatus,
  LeaveAllocationEditWithDrawerStatus,
  deleteLeaveAllocation,
  getLeaveAllocation,
  updateSortFilters,
  setColumnSearch,
} from "store/slices/LeaveAllocation/manageLeaveAllocationSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import moment from "moment";
import { AllocationDashboard } from "./components/AllocationDashboard";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
  const dispatch = useDispatch();
  const [view, setView] = useState("grid");

  const searchInput = useRef(null);
  const handleOpenModal = () =>
    dispatch(LeaveAllocationAddDrawerStatus({ errorStatus: 1, status: true }));
  const {
    LeaveAllocationAddDrawer,
    LeaveAllocationResult,
    tablePagination,
    sorting,
    filter,
    LeaveAllocationTableLoading,
    permission,
  } = useSelector((state) => state[LEAVE_ALLOCATION]);

  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getLeaveAllocation({
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
      })
    );
  };

  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, tablePagination.pageSize, filter, sorting);
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      newObject,
      sorting
    );
  };

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === "") {
      confirm();
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        { ...filter, [dataIndex]: value },
        sorting
      );
    }
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  useEffect(() => {
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      filter,
      sorting
    );
  }, []);

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, filter, sorting);
  };

  const handleDelete = (record) => {
    dispatch(deleteLeaveAllocation(record.id)).then(() => {
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        filter,
        sorting
      );
    });
  };
  const handleUpdate = (record) => {
    dispatch(
      LeaveAllocationEditWithDrawerStatus({ errorStatus: 1, data: record })
    );
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
    };

    try {
      await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
      getModuleData(1, tablePagination.pageSize, filter, sorting);
    } catch (error) {
      console.log(error);
    }
  };

  const columns = [
    {
      title: setLocale("Name"),
      dataIndex: "leave_type",
      key: "leave_type",
      sorter: true,
      render: (_, record) => <span>{record.leave_type?.name}</span>,
    },
    {
      title: setLocale("Days"),
      dataIndex: "no_of_days",
      key: "no_of_days",
    },
    {
      title: setLocale("Carry Forward"),
      dataIndex: "carry_forward",
      key: "carry_forward",
      render: (_, record) => <span>{record.carry_forward ? "Yes" : "No"}</span>,
    },
    {
      title: setLocale("Document Required"),
      dataIndex: "document_required",
      key: "document_required",
      render: (_, record) => (
        <span>{record.document_required ? "Yes" : "No"}</span>
      ),
    },
    {
      title: setLocale("Max Days Carry Forward"),
      dataIndex: "max_days_carry_forward",
      key: "max_days_carry_forward",
    },
    {
      title: setLocale("Max Leaves Allowed Together"),
      dataIndex: "max_leaves_together",
      key: "max_leaves_together",
    },
    {
      title: setLocale("Valid"),
      dataIndex: "valid",
      key: "valid",
      render: (_, record) => {
        const { start_date, end_date } = record;
        return (
          <span>
            {start_date && end_date
              ? `${moment(start_date).format("YYYY-MM-DD")} - ${moment(
                  end_date
                ).format("YYYY-MM-DD")}`
              : "No date range set"}
          </span>
        );
      },
    },
    {
      title: setLocale("operation"),
      key: "action",
      render: (data, record) => (
        <>
          {permission.includes("Delete") && (
            <Popconfirm
              title={setLocale("sure_to_delete")}
              onConfirm={(e) => handleDelete(record)}
            >
              <DeleteOutlined
                style={{ fontSize: "15px" }}
                className="text-danger"
              />{" "}
              &nbsp;
            </Popconfirm>
          )}
          {permission.includes("Update") && (
            <EditOutlined
              style={{ fontSize: "15px", marginRight: "9px" }}
              className="text-success"
              onClick={(e) => handleUpdate(record)}
            />
          )}
          {/* {permission.includes("View") && (
                        <Link to={`../../app/leave-allocation_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
        </>
      ),
    },
  ];
  return (
    <>
      <Breadcrumb className="my-2 mx-2">
        <Breadcrumb.Item>
          <Link to="/app/default">{setLocale("home")}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{setLocale("Leave Allocation")}</Breadcrumb.Item>
      </Breadcrumb>
      <div
        className="d-flex justify-content-end mb-3 mr-3"
        style={{ marginTop: "-30px" }}
      >
        <Row style={{ width: "100%" }} align="middle" justify="space-between">
          <Col span={6}>{/* <LeaveType showAll={true} /> */}</Col>
          <Col>
            {permission.includes("Create") && (
              <Button
                className="ant-btn-round ant-btn-sm"
                type="primary"
                style={{ marginLeft: "16px" }}
                onClick={handleOpenModal}
              >
                {setLocale("leaveallocation.add")}
              </Button>
            )}
          </Col>
        </Row>
      </div>

      <>
        <div className="">
          {LeaveAllocationAddDrawer && <AddLeaveAllocationModal />}
          <section className="code-box-description">
            {view === "grid" ? (
              <AllocationDashboard />
            ) : (
              <>
                <Table
                  onChange={handleTableChange}
                  columns={columns}
                  loading={LeaveAllocationTableLoading}
                  rowKey={(record) => record.id}
                  dataSource={LeaveAllocationResult.data ?? []}
                  pagination={false}
                />
                <Pagination
                  style={{ margin: "16px", float: "right" }}
                  current={tablePagination.current}
                  pageSize={tablePagination.pageSize}
                  total={tablePagination.total}
                  showTotal={(total, range) =>
                    `${range[0]}-${range[1]} of ${total} Records`
                  }
                  pageSizeOptions={["10", "20", "50", "100", "1000"]}
                  showQuickJumper
                  onChange={handlePageChange}
                />
              </>
            )}
          </section>
        </div>
      </>
    </>
  );
}

export default Index;

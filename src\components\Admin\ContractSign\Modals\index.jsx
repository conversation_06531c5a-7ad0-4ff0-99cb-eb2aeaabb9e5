import React, { useRef } from "react";
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CONTRACT_SIGN } from "constants/AppConstants";
import {
  ContractSignAddDrawerStatus,
  createContractSign,
  getContractSign,
} from "store/slices/ContractSign/manageContractSignSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import SignatureCanvas from "react-signature-canvas";

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {
    ContractSignAddDrawer,
    ContractSignButtonAndModelLabel,
    sorting,
    filter,
    ContractSignErrors,
    ContractSignShowMessage,
    ContractSignButtonSpinner,
    ContractSignEditData,
    tablePagination,
  } = useSelector((state) => state[CONTRACT_SIGN]);
  const sigCanvas = useRef();
  const sigCanvas1 = useRef();

  const clearSignature = () => {
    sigCanvas.current.clear();
    sigCanvas1.current.clear();
  };

  const onClose = () => {
    dispatch(ContractSignAddDrawerStatus(false));
    form.resetFields();
  };

  const onFinish = async (values) => {
    // Get signature data from canvas
    const signatureData = sigCanvas.current.toDataURL();
    const signatureData1 = sigCanvas1.current.toDataURL();

    // Include signature data in the form values
    const formValues = {
      ...values,
      first_signature: signatureData,
      second_signature: signatureData1,
    };

    if (ContractSignEditData && ContractSignEditData.id) {
      // If editing, include the id in the form values
      formValues.id = ContractSignEditData.id;
    }

    await dispatch(createContractSign(formValues))
      .then(() => {
        if (Object.keys(ContractSignErrors).length === 0) {
          dispatch(
            getContractSign({
              page: tablePagination.current,
              perPage: tablePagination.pageSize,
              filter,
              sorting,
            })
          );
          form.resetFields();
        }
      })
      .catch((error) => {
        // Handle error
        console.error("Error:", error);
      });
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <Drawer
      title={ContractSignButtonAndModelLabel}
      width={window.innerWidth > 800 ? "75%" : window.innerWidth - 100}
      onClose={onClose}
      visible={ContractSignAddDrawer}
      maskClosable={false}
      zIndex={1002}
      bodyStyle={{
        paddingBottom: 20,
      }}
    >
        <Row gutter={16}>
          <Col span={24} className="p-4">
          <Form.Item>
            <h3 style={{ textAlign: "center", marginBottom: 20 }}>
              Contract Sign
            </h3>
            <p style={{ textAlign: "justify" }}>
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. Lorem Ipsum has been the industry's standard dummy text
              ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into
              electronic typesetting, remaining essentially unchanged. It was
              popularised in the 1960s with the release of Letraset sheets
              containing Lorem Ipsum passages, and more recently with desktop
              publishing software like Aldus PageMaker including versions of
              Lorem Ipsum.
            </p>
            <p style={{ textAlign: "justify" }}>
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. Lorem Ipsum has been the industry's standard dummy text
              ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book.
            </p>

            <p style={{ textAlign: "justify" }}>
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. Lorem Ipsum has been the industry's standard dummy text
              ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into
              electronic typesetting, remaining essentially unchanged. It was
              popularised in the 1960s with the release of Letraset sheets
              containing Lorem Ipsum passages, and more recently with desktop
              publishing software like Aldus PageMaker including versions of
              Lorem Ipsum.
            </p>
            <p style={{ textAlign: "justify" }}>
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. Lorem Ipsum has been the industry's standard dummy text
              ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book.
            </p>
          </Form.Item>
        </Col>
      </Row>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        // initialValues={{
        //   name: ContractSignEditData?.name,
        // }}
      >
        <Row className="d-flex">
          <Col span={14} className="pl-4 pr-4">
            <Form.Item className="mb-0">
              <SignatureCanvas
                ref={sigCanvas}
                penColor="black"
                canvasProps={{
                  width: "350",
                  height: 150,
                  className: "sigCanvas  signature-box",
                }}
              />
            </Form.Item>
            <div className="disable-text-selection" style={{ marginBottom: 20 }}>
              _______________________________________________________
            </div>
            <p className="signature-label disable-text-selection">Student Signature</p>
          </Col>
          <Col span={9} className="pl-4 pr-4" style={{textAlign: "right"}}>
            <Form.Item className="mb-0">
              <SignatureCanvas
                ref={sigCanvas1}
                penColor="black"
                canvasProps={{
                  width: "350",
                  height: 150,
                  className: "sigCanvas signature-box",
                }}
              />
            </Form.Item>
            <p className="disable-text-selection" style={{ marginBottom: 20 }}>
              _______________________________________________________
            </p>
            <p className="ml-4 signature-label disable-text-selection" style={{textAlign: "left"}}>Initials</p>
          </Col>
        </Row>
        <Form.Item>
          <Space className="mt-5">
            <Button
              type="primary"
              htmlType="submit"
              loading={ContractSignButtonSpinner}
            >
              {ContractSignButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale("Cancel")}</Button>
            <Button onClick={clearSignature}>Clear Signature</Button>
          </Space>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Index;

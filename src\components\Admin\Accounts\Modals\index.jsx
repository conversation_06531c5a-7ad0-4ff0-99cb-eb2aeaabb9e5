import React,{useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ACCOUNTS } from "constants/AppConstants";
import {
    AccountsAddDrawerStatus,
    createAccounts,
    getAccounts,
    onCloseError
  } from "store/slices/Accounts/manageAccountsSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        AccountsAddDrawer,
        AccountsButtonAndModelLabel,
        sorting,
        filter,
        AccountsErrors,
        AccountsShowMessage,
        AccountsButtonSpinner,
        AccountsEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[ACCOUNTS]
    );
const onClose = () => {
    dispatch(AccountsAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (AccountsEditData && AccountsEditData.id) {
    // If editing, include the id in the form values
    formValues.id = AccountsEditData.id;
  }

  await dispatch(createAccounts(formValues));
};

  useEffect(() => {
    if (Object.keys(AccountsErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getAccounts({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [AccountsErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={AccountsButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={AccountsAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: AccountsEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={AccountsShowMessage && AccountsErrors.name ? "error" : ""}
                help={AccountsShowMessage && AccountsErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AccountsButtonSpinner}
            >
              {AccountsButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


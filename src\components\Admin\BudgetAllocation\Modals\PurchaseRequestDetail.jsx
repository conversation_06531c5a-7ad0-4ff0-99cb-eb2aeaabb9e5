import React, { useState, useEffect } from "react";
import { Drawer, Descriptions, Tag, Alert, Button, Table, Select, Modal, Tooltip } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { ExclamationCircleOutlined } from '@ant-design/icons';

import {
    updatePurchaseRequestStatus,
    setPurchaseRequestsViewDrawer,
    getPurchaseRequests
} from "store/slices/PurchaseRequests/managePurchaseRequestsSlice.js";
import { getColor, makeCapitilize } from "components/composeable/index";
import IntlMessage from "components/util-components/IntlMessage"
import { PURCHASE_REQUESTS } from "constants/purchase-requests/index.js";
import { env } from "configs/EnvironmentConfig"
import {
    purchaseRequestStatusesAdmin
} from 'components/Helper/Index';
const { confirm } = Modal;
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function PurchaseRequestDetail() {
    const dispatch = useDispatch();
    // const navigate = useNavigate();
    // const params = useParams();
    const {
        permission,
        purchaseRequestViewDrawer,
        PurchaseRequestsButtonAndModelLabel,
        PurchaseRequestsEditData,
        sorting,
        filter,
        tablePagination
    } = useSelector((state) => state[PURCHASE_REQUESTS]);


    const [requestStatus, setRequestStatus] = useState(PurchaseRequestsEditData?.status);
    useEffect(() => {

    }, []);
    const onClose = () => {
        dispatch(setPurchaseRequestsViewDrawer({ status: false, data: [] }));
    };
    const changeRequestStatus = async (value) => {
        confirm({
            icon: <ExclamationCircleOutlined />,
            content: setLocale('are-you-sure'),
            async onOk() {
                const values = {
                    status: value,
                    id: PurchaseRequestsEditData?.id
                }
                await dispatch(updatePurchaseRequestStatus(values))
                await getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
                await onClose()
            },
            onCancel() {
                setRequestStatus(PurchaseRequestsEditData?.status)
            },
            zIndex: 1100,
        });
    }
    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getPurchaseRequests({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }
    const columns = [
        {
            title: setLocale('purchaserequests.account_head_id'),
            // dataIndex: "account_head_name",
            key: "account_head_name",
            render: (data, record) => (
                <>
                    <Tooltip title={record?.account_head_name}>
                        {record?.account_head_code}
                    </Tooltip>
                </>
            )
        },
        {
            title: setLocale('purchaserequests.rate'),
            dataIndex: "rate",
            key: "rate",
        },
        {
            title: setLocale('purchaserequests.quantity'),
            dataIndex: "quantity",
            key: "quantity",
        },
        {
            title: setLocale('purchaserequests.amount'),
            dataIndex: "amount",
            key: "amount",
        },
        {
            title: setLocale('purchaserequests.product_life'),
            dataIndex: "product_life",
            key: "product_life",
        },
        {
            title: setLocale('purchaserequests.description'),
            dataIndex: "description",
            key: "description",
        },
    ];

    return (
        <Drawer
            title={PurchaseRequestsButtonAndModelLabel}
            width={window.innerWidth > 800 ? "80%" : window.innerWidth - 100}
            onClose={onClose}
            open={purchaseRequestViewDrawer}
            maskClosable={false}
            zIndex={1002}
            bodyStyle={{
                paddingBottom: 80,
            }}
            extra={
                PurchaseRequestsEditData?.status !== 'approved' &&
                <>
                    <Select style={{ width: 200, }}
                        value={requestStatus}
                        className='mr-2'
                        size="small"
                        onChange={changeRequestStatus}
                        placeholder={setLocale('purchaserequests.status')}
                        options={purchaseRequestStatusesAdmin}
                    />
                </>
            }>
            <Descriptions column={4} title={''} layout="vertical" bordered>
                <Descriptions.Item label={setLocale('purchaserequests.priority')}>
                    <Tag color={getColor(PurchaseRequestsEditData?.priority)}>{makeCapitilize(PurchaseRequestsEditData?.priority)}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.request_date')}>{PurchaseRequestsEditData?.request_date}</Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.designation')}>
                    {PurchaseRequestsEditData?.designation}
                </Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.userName')}>
                    {PurchaseRequestsEditData?.user_name}
                </Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.orgName')}>{PurchaseRequestsEditData?.org_name}</Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.gradeLevel')}>{PurchaseRequestsEditData?.grade_level}</Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.class')}>{PurchaseRequestsEditData?.class}</Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.vender_supplier')}>
                    {PurchaseRequestsEditData?.vender_supplier}
                </Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.status')}>
                    <Tag color={getColor(PurchaseRequestsEditData?.status)}>{makeCapitilize(PurchaseRequestsEditData?.status)}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label={setLocale('purchaserequests.updated_by')}>
                    {PurchaseRequestsEditData?.updated_by}
                </Descriptions.Item>
                <Descriptions.Item span={4} label={setLocale('purchaserequests.product_file')}>
                    {
                        PurchaseRequestsEditData?.files.map((file, index) => (
                            <Alert key={index}
                                message={`uploaded file`} type="info"
                                action={
                                    <Button size="small" type="link" target="_blank"
                                        href={env.FILE_ENDPOINT_URL + file.file} ghost> Preview </Button>
                                } />
                        ))
                    }
                    {/* { PurchaseRequestsEditData?.files.length && 

                        <Alert key={PurchaseRequestsEditData?.id}
                            message={`uploaded file`} type="info"
                            action={
                                <Button size="small" type="link" target="_blank"
                                href={env.FILE_ENDPOINT_URL+PurchaseRequestsEditData?.product_image} ghost> Preview </Button>
                            } />
                    }   */}
                </Descriptions.Item>
            </Descriptions>
            <Table
                columns={columns}
                rowKey={record => record.id}
                dataSource={PurchaseRequestsEditData?.items ?? []}
                pagination={false}
            />
        </Drawer>
    );
}
export default PurchaseRequestDetail;

import React, { useEffect, useRef } from "react";
import { Input, Space, Button, Table, Pagination, Popconfirm } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { usePara<PERSON>, <PERSON> } from "react-router-dom";
import {
    EyeOutlined,
    SearchOutlined, DownloadOutlined,
    DeleteOutlined, EditOutlined
} from "@ant-design/icons";
import {
    viewBatches,
    setInvoiceColumnSearch,
    updateInvoiceSortFilters,
    videPDF,
    deleteStudentFromBatch,
    BatchesEditWithDrawerStatus
} from "store/slices/Batches/manageBatchesSlice.js";
import Highlighter from 'react-highlight-words';
import IntlMessage from "components/util-components/IntlMessage"
import { BATCHES } from "constants/AppConstants";
import AvatarStatus from "components/composeable/AvatarStatus";
import { env } from "configs/EnvironmentConfig"

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function InvoiceTable() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);

    const params = useParams();
    const { permission, invoiceFilter, invoiceSorting, batchInvoiceTableLoading, ViewBatchesData, tableInvoicePagination } = useSelector(
        (state) => state[BATCHES]
    );

    console.log(ViewBatchesData?.data ?? '');
    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(viewBatches({ id: params?.id, page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tableInvoicePagination.pageSize, invoiceFilter, invoiceSorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = invoiceFilter;
        await dispatch(setInvoiceColumnSearch(newObject));
        getModuleData(tableInvoicePagination.current, tableInvoicePagination.pageSize, newObject, invoiceSorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setInvoiceColumnSearch({ ...invoiceFilter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tableInvoicePagination.current, tableInvoicePagination.pageSize, { ...invoiceFilter, [dataIndex]: value }, invoiceSorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={invoiceFilter[dataIndex] ? invoiceFilter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: invoiceFilter[dataIndex] && invoiceFilter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => {
            if (dataIndex === 'full_name' || dataIndex === 'grade_level' || dataIndex === 'invoice_batch') {
                const columnValue = dataIndex.split('.').reduce((acc, key) => acc[key], record);
                return columnValue ? columnValue.toString().toLowerCase().includes(value.toLowerCase()) : false;
            } else {
                return record[dataIndex].toString().toLowerCase().includes(value.toLowerCase());
            }
        },
        render: (text) =>
            invoiceFilter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[invoiceFilter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tableInvoicePagination.current, tableInvoicePagination.pageSize, invoiceFilter, invoiceSorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, invoiceFilter, invoiceSorting);
    };

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateInvoiceSortFilters({ filter: invoiceFilter, sorting: sorting }));
            getModuleData(1, tableInvoicePagination.pageSize, invoiceFilter, sorting);
        } catch (error) {
            console.log(error);
        }

    };

    const columns = [
        {
            title: setLocale('invoices.student_name'),
            dataIndex: 'full_name',
            key: 'full_name',
            fixed: 'left',
            // sorter: true,
            ...getColumnSearchProps('full_name'),
            render: (text, record) => (
                <>
                    <Link to={`/app/student_view/${record.student_enc_id}`}>
                        <AvatarStatus src={record?.profile_picture ? env.FILE_ENDPOINT_URL + record?.profile_picture : env.img} name={text + '(' + record.studentID + ')'} subTitle={''} />
                    </Link>
                </>
            ),
        },
        {
            title: setLocale('invoices.grade_level'),
            dataIndex: 'grade_level',
            key: 'grade_level',
            fixed: 'left',
            // sorter: true,
            // ...getColumnSearchProps('grade_level'),
        },
        {
            title: setLocale('invoices.invoice'),
            dataIndex: 'invoice_no',
            key: 'invoice_no',
            sorter: true,
            ...getColumnSearchProps('invoice_no'),
        },
        {
            title: setLocale('invoices.invoice_batch'),
            key: 'invoice_batch',
            dataIndex: 'invoice_batch',
            // sorter: true,
            // ...getColumnSearchProps('invoice_batch'),
        },
        {
            title: setLocale('invoices.invoice_date'),
            dataIndex: 'invoice_date',
            key: 'invoice_date',
            // sorter: true,
            // ...getColumnSearchProps('invoice_date'),
        },
        {
            title: setLocale('invoices.invoice_due_date'),
            dataIndex: 'due_date',
            key: 'due_date',
            // sorter: true,
            // ...getColumnSearchProps('due_date'),
        },
        {
            title: setLocale('invoices.status'),
            key: 'invoice_status_code',
            dataIndex: 'invoice_status_code',
            fixed: 'right',
            sorter: true,
            ...getColumnSearchProps('invoice_status_code'),
        },
        {
            title: setLocale('invoices.detail'),
            key: 'detail',
            fixed: 'right',
            render: (data, record) => (
                <>
                    <EyeOutlined
                        style={{ fontSize: '15px', marginRight: '10px' }}
                        className="text-success"
                        onClick={(e) => dispatch(videPDF(record))}
                    />
                    {permission.includes("Delete") && record.batch_status !== 'Posted' && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ marginLeft: '10px', cursor: 'pointer', fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {record?.batch_status === 'Posted' && (
                        <DownloadOutlined
                            style={{ fontSize: '15px' }}
                            className="text-success"
                            onClick={() => handleDownloadClick(record?.invoice_file_path)}
                        />
                    )}
                    {/* {permission.includes("Update") && record.batch_status !== 'Posted' && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )} */}
                </>
            )
        }
    ];

    const handleUpdate = (record) => {
        dispatch(BatchesEditWithDrawerStatus({ data: record, errorStatus: 1, status: true }));
    }

    const handleDelete = (record) => {
        dispatch(deleteStudentFromBatch({ id: record.id })).then(() => {
            getModuleData(tableInvoicePagination.current, tableInvoicePagination.pageSize, invoiceFilter, invoiceSorting);
        })
    }

    const handleDownloadClick = (invoiceFilePath) => {
        window.open(invoiceFilePath, '_blank');
    };

    return (
        <>
            <Table
                onChange={handleTableChange}
                columns={columns}
                loading={batchInvoiceTableLoading}
                rowKey={record => record.id}
                dataSource={ViewBatchesData.data ?? []}
                pagination={false}
            />
            <Pagination
                style={{ margin: '16px', float: 'right' }}
                current={tableInvoicePagination.current}
                pageSize={tableInvoicePagination.pageSize}
                total={tableInvoicePagination.total}
                showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} items`}
                pageSizeOptions={['10', '20', '50', '100', '1000']}
                showQuickJumper
                onChange={handlePageChange}
            />

        </>
    );
}
export default InvoiceTable;

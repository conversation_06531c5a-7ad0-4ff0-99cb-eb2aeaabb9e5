import moment from 'moment';
import React, { useEffect, useState } from 'react';

import { Row, Form, Col, <PERSON><PERSON>, Card, DatePicker, Select, Skeleton } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import {
    getAttendanceSummary,
    makeStudentAttNull,
    getClassStudents,
    updateSortFilters
} from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import AttendanceReportSummaryView from './ReportView/AttendanceReportSummaryView'
import IntlMessage from 'components/util-components/IntlMessage';
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
import { USER_INFORMATION } from 'constants/AuthConstant';
import OrganizationSelect from "components/Admin/OrganizationDropdown/OrganizationSelect";
import { getManageClass } from 'store/slices/ManageClass/manageManageClassSlice';
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


const { RangePicker } = DatePicker;
const { Option } = Select;

function ClassStudentSummaryReport(props) {
    const { classes } = props;
    const [defaultDate, setDefaultDate] = useState([moment().subtract(30, 'days'), moment()]);
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [reportLoad, setReportLoad] = useState(false);
    const [reportName, setReportName] = useState(null);

    const { studentAttReport, classStudents,
        filter,
        sorting,
        tablePagination
    } = useSelector((state) => state[ATTENDANCE_REPORT]);

    useEffect(() => {
        /**this is just to clear previous data  */
        dispatch(makeStudentAttNull());
    }, []);


    const onFinish = (values) => {
        setReportLoad(true)
        const dateRange = values['date'];
        /** make date formate */
        values.from_date = moment(dateRange[0]).format("YYYY-MM-DD")
        values.end_date = moment(dateRange[1]).format("YYYY-MM-DD")

        const formData = {
            from_date: values.from_date,
            end_date: values.end_date,
            class: values.class,
            student_id: values.student_id,
            organization_id: filter?.organization_id ?? selectedOrganization

        }
        /**set report name  */
        setReportName(`Attendance Summary Report ${values.from_date}-To-${values.end_date}`);
        /** class to studentAttReport report */
        dispatch(getAttendanceSummary(formData)).then((result) => {
            setReportLoad(false)
        });
    }
    /**get Class Students by ClassId */
    const getStudentByClass = (value) => {
        dispatch(getClassStudents({ id: value })).then((result) => {

        });
    }

    const onChangeOrganization = (value) => {
        dispatch(getManageClass({ organization_id: value })).then((result) => {
            // setPageLoad(false)
        });
    }
    return (
        <>
            <Row gutter={12} className='absentees-report'>
                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                    <div className='px-2 py-2'>
                        <Form layout="vertical" onFinish={onFinish} form={form}
                            initialValues={{ attview: 'table', date: defaultDate }} >
                            <Row gutter={12}>
                                {userOrganizations.length > 1 &&
                                    <>
                                        <Col xs={24} sm={24} md={5} lg={5} xl={5} >
                                            <Form.Item
                                                name="organization_id"
                                                label={setLocale('Organization')}
                                                rules={[
                                                    {
                                                        required: false,
                                                        message: setLocale('Please Select Class'),
                                                    },
                                                ]}
                                            >
                                                <OrganizationSelect
                                                    updateSortFilters={updateSortFilters}
                                                    filter={filter}
                                                    onChange={onChangeOrganization}
                                                    sorting={sorting}
                                                    tablePagination={tablePagination}
                                                    getModuleData={onFinish}
                                                    runUseEffect={false}
                                                    dropStyle={true}
                                                />
                                            </Form.Item>
                                        </Col>
                                    </>
                                }

                                <Col xs={24} sm={24} md={5} lg={5} xl={5} className='px-2'>
                                    <Form.Item name="date"
                                        label={setLocale("attendance_report.date_from_to")}
                                        rules={[
                                            {
                                                required: true,
                                                message: setLocale('attendance_report.daterange_error'),
                                            },
                                        ]}>
                                        <RangePicker className='w-100' />
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={24} md={5} lg={5} xl={5} className='px-2'>
                                    <Form.Item name="class"
                                        label={setLocale("classes.label")}
                                        rules={[
                                            {
                                                required: false,
                                                message: setLocale('classes.label_error'),
                                            },
                                        ]}>
                                        <Select onChange={getStudentByClass} optionLabelProp="label">
                                            {classes ? classes.map((clas, index) =>
                                                <Option value={clas.id} key={index} label={clas.name}>{clas.name}</Option>
                                            ) : null}
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col xs={24} sm={24} md={5} lg={5} xl={5} className='px-2'>
                                    <Form.Item name="student_id"
                                        label={setLocale("attendance_report.select_student")}
                                        rules={[
                                            {
                                                required: false,
                                                message: setLocale('attendance_report.student_error'),
                                            },
                                        ]}>
                                        <Select mode="multiple" optionLabelProp="label"
                                            filterOption={(inputValue, option) =>
                                                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                                            }
                                        >
                                            {classStudents ? classStudents.map((clsStd, index) =>
                                                <Option value={clsStd.students.id} key={index} label={clsStd.students.full_name}>{clsStd.students.full_name}</Option>
                                            ) : null}
                                        </Select>
                                    </Form.Item>
                                </Col>

                                <Col xs={24} sm={24} md={4} lg={4} xl={4} className='px-2'>
                                    <Form.Item label=" " className='text-right'>
                                        <Button loading={reportLoad} type="primary" htmlType="submit">
                                            {setLocale('attendance_report.generate_report')}
                                        </Button>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form>
                        {reportLoad &&
                            <><Skeleton active /><Skeleton active /></>
                        }
                        {(!reportLoad && studentAttReport) &&
                            <>
                                <AttendanceReportSummaryView data={studentAttReport} reportName={reportName} />

                            </>
                        }
                    </div>
                </Col>
            </Row>
        </>
    )
}

export default ClassStudentSummaryReport




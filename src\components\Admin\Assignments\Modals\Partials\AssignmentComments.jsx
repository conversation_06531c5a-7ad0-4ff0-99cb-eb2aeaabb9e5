import React, { useEffect, useState } from 'react'
import { useSelector, useDispatch } from "react-redux"
import { Typography, Row, Col, Skeleton, Input } from 'antd'
import { USER_INFORMATION } from 'constants/AuthConstant'
import { env } from "configs/EnvironmentConfig"
import { SendOutlined } from '@ant-design/icons'
import IntlMessage from "components/util-components/IntlMessage"
import { MANAGE_CLASS } from 'constants/AppConstants'
import AvatarStatus from "components/composeable/AvatarStatus"
import {
  saveAssignmentComment,
  getAssignmentComments,
  pushNewComment
} from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import Echo from 'broadcasting/laravelEcho'
// import { success } from 'composeable'
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Title } = Typography;
const currentUser = localStorage.getItem(USER_INFORMATION) || null;

function AssignmentComments() {
  const dispatch = useDispatch();
  const [message, setMessage] = useState(null);
  const { singleAssignment, loadingComments, assignmentComments } = useSelector((state) => state[ASSIGNMENT_SLICE]);
  const { ClassCourseEditData } = useSelector(state => state[MANAGE_CLASS]);

  useEffect(() => {
    const channel = Echo.private(`assignment-comments-${ClassCourseEditData?.manage_class_id}-${ClassCourseEditData.course_id}-${singleAssignment?.id}`);
    channel.listen('AssignmentCommentEvent', async (data) => {
      await dispatch(pushNewComment(data[0]))
      if (JSON.parse(currentUser).id != data[0].created_by) {
        // success('new comment')
      }
    })
    return () => {
      channel.stopListening('AssignmentCommentEvent');
    }
  }, [])

  useEffect(() => {
    dispatch(getAssignmentComments({ assignment_id: singleAssignment.id }))
  }, [singleAssignment])

  //save assignment message
  const singleAssignmentMessage = async () => {
    if (message) {
      const data = {
        comments: message,
        assignment_id: singleAssignment.id,
        is_pvt_comment: 0,
        class: ClassCourseEditData?.manage_class_id,
        course: ClassCourseEditData.course_id,
        assignment: singleAssignment?.id
      }
      await dispatch(saveAssignmentComment(data))
      setMessage('');
    }
  }
  return (
    <>
      <Row gutter={4} className='mt-4'>
        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
          <Input
            placeholder="Type your message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onPressEnter={singleAssignmentMessage} // Trigger sending on Enter key press
            addonAfter={<SendOutlined onClick={singleAssignmentMessage} />} />
        </Col>
      </Row>
      <Row>
        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
          {loadingComments ?
            <Skeleton avatar paragraph={{ rows: 2, }} />
            : <>
              <Title level={4}>{setLocale('assignment.comments')}</Title>
              {assignmentComments ? assignmentComments.map((comment, i) => (
                <div className={`${i === (assignmentComments?.length - 1) ? '' : 'mb-4'}`} key={`eduction-${i}`}>
                  <AvatarStatus src={env.FILE_ENDPOINT_URL + comment?.user?.profile_picture} name={comment?.user?.name} subTitle={comment?.created_at} />
                  <p className="pl-5 mt-2 mb-0">{comment?.comments}</p>
                </div>
              )) : null}

            </>
          }
        </Col>
      </Row>


    </>
  )
}

export default AssignmentComments;
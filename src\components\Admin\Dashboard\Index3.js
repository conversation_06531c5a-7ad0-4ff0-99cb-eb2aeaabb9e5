import React, { useEffect,useState } from 'react';
import moment from 'moment';
import { useDispatch,useSelector } from 'react-redux';
import { Statistic,message,Form,DatePicker,Select,Tooltip, Dropdown, Menu,Space,Button,Col, Row,Avatar, List,Input, Typography,Skeleton,Modal, Table, Tag, Card } from 'antd';
import { ExclamationCircleFilled, CloseCircleTwoTone,CheckCircleTwoTone,EllipsisOutlined ,UserOutlined,PlusOutlined,  BookOutlined, CalendarFilled, BookFilled , EyeFilled, SortAscendingOutlined, DownloadOutlined, RightOutlined } from '@ant-design/icons';
// import { finalizedCourseGradeCategory,addStudentObtainedMarks,addCourseGradeBookCategory,getClassStudentsForGrades } from 'store/slices/gradeSystem/studentGradeBookSlice';
// import { getAllGradeBookCategory } from 'store/slices/gradeSystem/gradeSystemSlice';

import { 
  finalizedCourseGradeCategory,
  addStudentObtainedMarks,
  addCourseGradeBookCategory,
  getClassStudentsForGrades
} from "store/slices/Grade/studentGradeBookSlice.js";
import { GRADE_BOOK_CATEGORY, STUDENT_GRADE_BOOK_SLICE, MANAGE_CLASS } from "constants/AppConstants";
import { getGradeBookCategory } from "store/slices/GradeBookCategory/manageGradeBookCategorySlice.js";

import IntlMessage from 'components/util-components/IntlMessage';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;


function StudentGrades(props) {
  const { GradeBookCategoryResult } = useSelector((state) => state[GRADE_BOOK_CATEGORY])
  const { classStudentsForGrades, activeStudents } = useSelector((state) => state[STUDENT_GRADE_BOOK_SLICE])
  const { ClassCoursesData, ClassCourseEditData } = useSelector((state) => state[MANAGE_CLASS]);
  console.log(ClassCoursesData, ClassCourseEditData)

  const iconColor = { color: '#BC404F' };
  const [form] = Form.useForm();

  const { data } = props;  
  const [courseId, setCourseId] = useState(ClassCourseEditData?.course_id);

  const dispatch = useDispatch();
  const [loadStudents,setLoadStudents] = useState(true);
  const [studenMarkPercentage, setStudenMarkPercentage] = useState({});
  const [loading, setLoading] = useState(false);
  const [addNewGradeCategory, setAddNewGradeCategory] = useState(false);


  const [messageApi, contextHolder] = message.useMessage();
  const success = (msg) => {
    messageApi.open({
      type: 'success',
      content: msg,
      style: {
        marginTop: '90vh',
      },
    })
  }
  const error = (msg) => {
    messageApi.open({
      type: 'error',
      content: msg,
      style: {
        marginTop: '90vh',
      },
    })
  }


  useEffect(() => {
    /**get All GradeBook Categories  */
    dispatch(getGradeBookCategory())
    /**get All CourseGradeBookCategories with student Marks */
    dispatch(getClassStudentsForGrades( 
      { class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }))
      .then((response) => {
        setLoadStudents(false);
      });

  }, []);

  /**change Student Obtained Marks */
  const handleStudentMarkChange = (e, student, catStdId) => {
    setStudenMarkPercentage((prevInputValues) => {
      return {
        ...prevInputValues,
        [catStdId]: e.target.value,
      };
    });
  };

  /**when teacher add Student mark and move to next It will update that student marks in DB */
  const handleStudentMarkBlur = (obtaibMarks, classStudentId, category) => {
    const { total_marks, id } = category;
    let courseGradeBookCategoryId = id;
    let totalMarks = total_marks;
    if(obtaibMarks > totalMarks){
      error('Obtain marks should be less or equal to total marks')
      return false
    }
    if(obtaibMarks){
      dispatch(addStudentObtainedMarks({totalMarks,obtaibMarks,classStudentId,courseGradeBookCategoryId}))
    }
  }
  
  /**open CourseGradeCategory Modal to add new Category */
  const openCategoryGradeModal = ()=>{
    form.resetFields();
    setAddNewGradeCategory(true);
  }
  const saveCourseGradeCategory =(values)=>{
    /**add some variables into request for some processing */
    values.class_id = ClassCourseEditData?.manage_class_id;
    values.course_id = ClassCourseEditData?.course_id;
    /**Date formate before saving */
    values.due_date = moment(values.due_date).format('YYYY-MM-DD')

    setLoading(true);
    dispatch(addCourseGradeBookCategory(values)).then(() =>{ 
      setLoading(false);
      setAddNewGradeCategory(false);
      /** get Updated date for GradeBook Category */
      dispatch(getClassStudentsForGrades({ class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }))
       
    })
  }
  /**on cancel CourseGrade Modal */
  const handleCancel = () => {
    setAddNewGradeCategory(false); 
    setLoading(false);
  }
  /**when teacher make a category Finalized after He is unable to change any one marks */
  const finalizedHandler = (category) => {
    confirm({
      title: 'Are you sure? you want to finalize it!',
      icon: <ExclamationCircleFilled />,
      zIndex: 1011,
      style: {
        top: '50%',
        transform: 'translateY(-50%)',
      },
      onOk() {
        const { id } = category;
        let status = 'Finalized';
        dispatch(finalizedCourseGradeCategory({id,status})).then(() =>{ 
          /** get Updated date for GradeBook Category */
          dispatch(getClassStudentsForGrades({ class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }))
           
        })
      },
      onCancel() {
       
      },
    });
  }

  /** handle course change get new Course categories */
  const handleCourseChange = (value) => {
    setCourseId(value);
    setLoading(true);

    /**get All CourseGradeBookCategories with student Marks */
    dispatch(getClassStudentsForGrades({ class_id: ClassCourseEditData?.manage_class_id, course_id: value }))
      .then((response) => {
        setLoading(false);
      });
  };

  const courseOptions = ClassCoursesData.map(item => ({
    value: item.course_id,
    label: item.course
  }))

  const items = [
    {
      label: 'Finalized',
      key: 1,
    },
  ];
  const menuItems = items.map(item => (
    <Menu.Item key={item.key}>{item.label}</Menu.Item>
  ));
 
  const columns = [
    {
      title: 'Ramsha',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Term 1',
      dataIndex: 'term1',
      key: 'term1',
      className: 'grade-table-cell-red',
    },
    {
      title: 'Term 2',
      dataIndex: 'term2',
      key: 'term2',
      className: 'grade-table-cell-yellow',
    },
    {
      title: 'Term 3',
      dataIndex: 'term3',
      key: 'term3',
      className: 'grade-table-cell-green',
    },
    {
      title: 'Term 4',
      dataIndex: 'term4',
      key: 'term4',
      className: 'grade-table-cell-red',
    },
    {
      title: 'Total',
      dataIndex: 'total',
      key: 'total',
      className: 'grade-table-cell-yellow',
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',

    },

  ];
  const dataItem = [
    {
      key: '1',
      name: 'Mathematics',
      term1: "59%",
      term2: "34%",
      term3: "20%",
      term4: "48%",
      total: '73%',
      action: "View",
    },
    {
      key: '2',
      name: 'Ethics',
      term1: "22%",
      term2: "42%",
      term3: "24%",
      term4: "35%",
      total: '52%',
      action: "View",
    },
    {
      key: '1',
      name: 'Geography',
      term1: "18%",
      term2: "97%",
      term3: "62%",
      term4: "73%",
      total: '69%',
      action: "View",
    },
    {
      key: '1',
      name: 'English Lit.',
      term1: "49%",
      term2: "75%",
      term3: "92%",
      term4: "48%",
      total: '74%',
      action: "View",
    },
    {
      key: '1',
      name: 'French',
      term1: "29%",
      term2: "85%",
      term3: "49%",
      term4: "73%",
      total: '55%',
      action: "View",
    },
    {
      key: '1',
      name: 'Science',
      term1: "19%",
      term2: "39%",
      term3: "73%",
      term4: "64%",
      total: '74%',
      action: "View",
    },
  ]
  return (
    <>
      {contextHolder}
      { loadStudents ? <><Skeleton active /><Skeleton active /></> : 
        <>
          <Row gutter={16} className=''>
            <Col span={24}>
            <Select
                className='mx-2'
                defaultValue={{ label: <> Tayyab</> }}
                optionLabelProp="label"
              >
                <Option value="Ejaz" label={<>Ejaz</>}></Option>
                <Option value="Khalid" label={<>Khalid</>}></Option>
                <Option value="Hamza" label={<>Hamza</>}></Option>
              </Select>
            </Col>
          </Row>
          <hr></hr>
          <Row gutter={16} className=' mb-3 pt-3 pb-3 bg-white'>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <div className='grade-book-select'>
            </div>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <div style={{display: "flex", justifyContent: "end"}}>
            <Select
                className='mx-2'
                defaultValue={{ label: <><SortAscendingOutlined style={iconColor}  /> Sort by</> }}
                optionLabelProp="label"
              >
                <Option value="1" label={<><SortAscendingOutlined style={iconColor}  /> 1</>}></Option>
                <Option value="2" label={<><SortAscendingOutlined style={iconColor}  /> 2</>}></Option>
                <Option value="3" label={<><SortAscendingOutlined style={iconColor}  /> 3</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <><EyeFilled style={iconColor}  /> View as</> }}
                optionLabelProp="label"
              >
                <Option value="Excel" label={<><EyeFilled style={iconColor}  /> Excel</>}></Option>
                <Option value="PDF" label={<><EyeFilled style={iconColor}  /> PDF</>}></Option>
              </Select>
              <Button size='small'  className='mx-2'> Compare
              </Button>
              <Button size='small' >
              <DownloadOutlined /> Export CSV
              </Button>
              </div>
            </Col>
          </Row>
          <Row gutter={16} className='mx-2'>
            <Col span={24}>
              <Card className='grade-top-card'>
              <h2 className='mt-2'>Tayyab</h2>
              <div className='d-flex justify-content-between mt-4'>
                <h5>Grade 7</h5>
                <h5>Go to Gradebook <RightOutlined /></h5>
              </div>
              </Card>
            </Col>
          </Row>
          <Row gutter={16} className='my-3 mx-2'>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>  
          <Table className='Grade-book-table' columns={columns} dataSource={dataItem} />
          </Col>
          </Row>

        </>
      }
    </>
  )
}

export default StudentGrades

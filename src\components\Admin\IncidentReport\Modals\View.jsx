import React, { useState, useEffect } from "react";
import { Col, Row, Card, Avatar, Skeleton, Breadcrumb, Table } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { Icon } from "components/util-components/Icon";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
    ArrowLeftOutlined,
    ContainerOutlined
} from "@ant-design/icons";
import {
    viewIncidentReport
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import Flex from "components/shared-components/Flex";
import IntlMessage from "components/util-components/IntlMessage"
import { INCIDENT_REPORT } from "constants/AppConstants";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function View() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const params = useParams();
    const { ViewIncidentReportData, ViewIncidentReportLoader } = useSelector(
        (state) => state[INCIDENT_REPORT]
    );

    useEffect(() => {
        dispatch(viewIncidentReport(params?.id));
    }, []);

    const columns = [
        {
            title: 'Participant Name',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Role',
            dataIndex: 'participant_role',
            key: 'participant_role',
        },
        {
            title: 'Action Taken',
            dataIndex: 'incident_action_code',
            key: 'incident_action_code',
        },
        {
            title: 'Start Date',
            dataIndex: 'date_from',
            key: 'date_from',
        },
        {
            title: 'End Date',
            dataIndex: 'date_to',
            key: 'date_to',
        },
        {
            title: 'Duration',
            dataIndex: 'duration_code',
            key: 'duration_code',
        },
        {
            title: 'Added to Attendance',
            dataIndex: 'attendance',
            key: 'attendance',
        },
    ];


    const otherColumns = [
        {
            title: 'Participant Name',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Primary Phone',
            dataIndex: 'others_primary_phone',
            key: 'others_primary_phone',
        },
        {
            title: 'Email',
            dataIndex: 'others_email',
            key: 'others_email',
        },
        {
            title: 'Alternate Phone',
            dataIndex: 'others_alternate_phone',
            key: 'others_alternate_phone',
        },
        {
            title: 'Address',
            dataIndex: 'others_address',
            key: 'others_address',
        },
        {
            title: 'Role',
            dataIndex: 'participant_role',
            key: 'participant_role',
        },
        {
            title: 'Action Taken',
            dataIndex: 'incident_action_code',
            key: 'incident_action_code',
        },
        {
            title: 'Start Date',
            dataIndex: 'date_from',
            key: 'date_from',
        },
        {
            title: 'End Date',
            dataIndex: 'date_to',
            key: 'date_to',
        },
        {
            title: 'Duration',
            dataIndex: 'duration_code',
            key: 'duration_code',
        },
        {
            title: 'Added to Attendance',
            dataIndex: 'attendance',
            key: 'attendance',
        },
    ];

    console.log(ViewIncidentReportData);
    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Incident Report')}</Breadcrumb.Item>
            </Breadcrumb>
            {ViewIncidentReportLoader ? <><Skeleton active ></Skeleton></> :
                <>
                    <Card
                        title={
                            <h5>
                                <span style={{ cursor: "pointer", float: "right" }}
                                    onClick={() => navigate(-1)}
                                >
                                    <ArrowLeftOutlined /> Back
                                </span>{" "}
                            </h5>
                        }
                        className="profile-card"
                    >
                        <Row gutter={16} className="incident-summary mx-2">
                            <Col span={24}>
                                <h3>Incident Summary</h3>
                            </Col>
                            <Col span={24} className="mb-3">
                                <h4 className="mb-3">Incident</h4>
                                <div className="label mb-2">Incident Title</div>
                                <span className="description">{ViewIncidentReportData?.title}</span>
                            </Col>
                            <Col span={6} className="mb-3">
                                <div className="label mb-2">Description</div>
                                <span className="description">{ViewIncidentReportData?.description}</span>
                            </Col>
                            <Col span={6} className="mb-3">
                                <div className="label mb-2">Incident Type</div>
                                {ViewIncidentReportData?.incident_types.map((item, index) => (
                                    <span className="description">{item?.incident_type} </span>
                                ))}
                            </Col>
                            <Col span={6} className="mb-3">
                                <div className="label mb-2">Time frame</div>
                                <span className="description">{ViewIncidentReportData?.time_frame}</span>
                            </Col>
                            <Col span={6} className="mb-3">
                                <div className="label mb-2">Incident Date</div>
                                <span className="description">{ViewIncidentReportData?.incident_date}</span>
                            </Col>
                            <Col span={6} className="mb-3">
                                <div className="label mb-2">Location</div>
                                <span className="description">{ViewIncidentReportData?.location}</span>
                            </Col>
                            <Col span={6} className="mb-3">
                                <div className="label mb-2">Prepared By</div>
                                <span className="description">{ViewIncidentReportData?.prepared_by}</span>
                            </Col>
                            <Col span={6} className="mb-3">
                                <div className="label mb-2">Reported By</div>
                                <span className="description">{ViewIncidentReportData?.reported_by}</span>
                            </Col>
                        </Row>
                        <hr></hr>
                        <Row className="incident-summary mx-2 mt-4">
                            <Col span={24} className="mb-3 mx-2">
                                <h4 className="mb-3">Participants</h4>
                                <div className="label mb-2">Who is involved ?</div>
                            </Col>
                            {
                                ViewIncidentReportData?.incident_participant?.student?.length > 0 && (
                                    <Col span={24}>
                                        <span className="description">Student</span>
                                        <Table dataSource={ViewIncidentReportData?.incident_participant?.student ?? []} columns={columns} />
                                    </Col>
                                )
                            }
                            {
                                ViewIncidentReportData?.incident_participant?.staff?.length > 0 && (
                                    <Col span={24}>
                                        <span className="description">Staff</span>
                                        <Table dataSource={ViewIncidentReportData?.incident_participant?.staff ?? []} columns={columns} />
                                    </Col>
                                )
                            }
                            {
                                ViewIncidentReportData?.incident_participant?.other?.length > 0 && (
                                    <Col span={24}>
                                        <span className="description">Other</span>
                                        <Table dataSource={ViewIncidentReportData?.incident_participant?.other ?? []} columns={otherColumns} />
                                    </Col>
                                )
                            }
                        </Row>
                    </Card>

                </>
            }
        </>
    );
}
export default View;

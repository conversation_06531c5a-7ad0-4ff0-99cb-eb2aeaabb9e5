import React, { useState, useEffect } from 'react';
import { Select, Button, Col, Drawer, Form, Input, Row, Space } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import {
  setCloseModal,
  addTopic,
  getAllTopics,
  clearErrors
} from "store/slices/Topics/topicSlice";
import { TOPIC_SLICE } from 'constants/topics/index'
import { MANAGE_CLASS } from 'constants/AppConstants'
import IntlMessage from 'components/util-components/IntlMessage'
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { Option } = Select;

function AddTopicModal(props) {
  const {
    showMessage,errors,
    loading,openTopicModal,
    topicLabel,editData
  } = useSelector((state) => state[TOPIC_SLICE]);  
  const { 
    ClassCourseEditData, 
    ViewManageClassData 
  }  = useSelector(state => state[MANAGE_CLASS]);    
  const dispatch = useDispatch();
  const [form] = Form.useForm();

  const onClose = () => {
    dispatch(setCloseModal())
  }
  // console.log('=> ClassCourseEditData =>',ClassCourseEditData,'=> ViewManageClassData',ViewManageClassData)
  const onFinish = async(values) => {
    try{
      /**check course and gradelevel */
      if (ClassCourseEditData) { values.course_id = ClassCourseEditData.course_id }
      if (ViewManageClassData) { values.organization_grade_level_id = ViewManageClassData.organization_grade_level_id }
      /**  If editing, include the id in the form values*/
      if (editData && editData.id) {
        values.id = editData.id;
      }

      values.name = values.name.trim();
      await dispatch(addTopic(values))
      
    } catch (error) { } finally {
      // console.log('values =>',showMessage, 'editData =>',errors)
      // if (!showMessage && Object.keys(errors).length === 0) {
      //   await dispatch(getAllTopics(values));        
      //   form.resetFields();
      // }
    }
  }
 useEffect(() => {  
   if (!showMessage && Object.keys(errors).length === 0) {    
        const values = { course_id: ClassCourseEditData.course_id, organization_grade_level_id: ViewManageClassData.organization_grade_level_id };
        dispatch(getAllTopics(values));        
        form.resetFields();
      }
 },[errors])
 
  useEffect(() => {
    dispatch(clearErrors())
    // dispatch(getAllCourses())    
  }, [])

  return (
    <>
      <Drawer title={setLocale(`${topicLabel}`)} width={720}
        onClose={onClose} open={openTopicModal}
        maskClosable={false} bodyStyle={{ paddingBottom: 80 }} zIndex={1002}>
        <Form layout="vertical" onFinish={onFinish} form={form} initialValues={editData}>
          <Row gutter={4}>
            <Col xs={24} sm={24} md={24} lg={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={showMessage && errors.name ? "error" : ""}
                extra={showMessage && errors.name}  >
                <Input />
              </Form.Item>
            </Col>
            {/* <Col  xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                  name="course_id"
                  label={setLocale("course.course")}
                  rules={[ {
                      required: true,
                      message: setLocale('selectCourse'),
                    } ]} 
                  validateStatus={showMessage && errors.course_id ? "error" : ""}
                      extra={showMessage && errors.course_id}>
                  <Select >
                  {Course ? Course.map((course, index) =>
                      <Option value={course.id} key={index} label={course.label}>{course.label}</Option>
                  ) : null}
                  </Select>
              </Form.Item>
            </Col>
            <Col  xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                  name="grade_level_id"
                  label={setLocale("gradeLevel")}
                  rules={[ {
                      required: true,
                      message: setLocale('selectGradeLevel'),
                    } ]} 
                  validateStatus={showMessage && errors.grade_level_id ? "error" : ""}
                      extra={showMessage && errors.grade_level_id}>
                  <Select >
                  {GradeLevel ? GradeLevel.map((gLevel, index) =>
                      <Option value={gLevel.id} key={index} label={gLevel.label}>{gLevel.label}</Option>
                  ) : null}
                  </Select>
              </Form.Item>
            </Col> */}
          </Row>

          <Space>
            <Form.Item >
              <Button loading={loading} type="primary" htmlType="submit" >
                {setLocale('save')}
              </Button>
            </Form.Item>
          </Space>
        </Form>
      </Drawer>
    </>
  )
}

export default AddTopicModal
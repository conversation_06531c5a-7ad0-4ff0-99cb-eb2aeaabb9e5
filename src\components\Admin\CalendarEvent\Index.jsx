import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { CALENDAR_EVENT } from "constants/AppConstants";
import AddCalendarEventModal from "./Modals/index";
import {
    CalendarEventAddDrawerStatus,
    CalendarEventEditWithDrawerStatus,
    deleteCalendarEvent,
    getCalendarEvent,
    updateSortFilters,
    setColumnSearch
} from "store/slices/CalendarEvent/manageCalendarEventSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import moment from 'moment';
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(CalendarEventAddDrawerStatus({ errorStatus: 1, status: true }));
    const { CalendarEventAddDrawer, CalendarEventResult, tablePagination, sorting, filter, CalendarEventTableLoading, permission } = useSelector(
        (state) => state[CALENDAR_EVENT]
    );


    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getCalendarEvent({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };
    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };
    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };
    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };

    const handleDelete = (record) => {
        dispatch(deleteCalendarEvent(record.id)).then(() => {
            getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        })
    }
    const handleUpdate = (record) => {
        dispatch(CalendarEventEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) {
            console.log(error);
        }

    };

    const columns = [
        {
            title: setLocale('name'),
            dataIndex: "name",
            key: "name",
            sorter: true,
            ...getColumnSearchProps('name'),
        },
        {
            title: setLocale('calendarEvent.category'),
            dataIndex: "calendar_category_id",
            key: "calendar_category_id",
            sorter: true,
            ...getColumnSearchProps('calendar_category_id'),
            render: (text, record) => (
                <>{record.calendar_category ? record.calendar_category.name : ''}</>
            ),
        },
        {
            title: setLocale('calendarEvent.color_code'),
            dataIndex: "color_code",
            key: "color_code",
            sorter: true,
            render: (data, record, text) => (
                <>
                    <span style={{ backgroundColor: record.color_code, borderRadius: '50%', padding: '8px 17px' }}></span>
                </>
            ),
            // ...getColumnSearchProps('color_code'),
        },
        {
            title: setLocale('school_year.start_date'),
            key: 'start_date',
            label: 'start_date',
            render: (text, record) => {
                return `${moment(record.start_date).format('YYYY-MM-DD')}`;
            }
        },
        {
            title: setLocale('school_year.end_date'),
            dataIndex: "end_date",
            key: "end_date",
            render: (text, record) => {
                return `${moment(record.end_date).format('YYYY-MM-DD')}`;
            }
        },
        {
            title: setLocale('calendarEvent.is_operational_day'),
            dataIndex: "is_operational_day",
            key: "is_operational_day",
            render: (data, record, text) => (
                <>
                    <span>{record.is_operational_day ? setLocale('YES') : setLocale('NO')}</span>
                </>
            ),
        },
        {
            // is whole day
            title: setLocale('calendarEvent.is_whole_day'),
            dataIndex: "is_whole_day",
            key: "is_whole_day",
            render: (data, record, text) => (
                <>
                    <span>{record.is_whole_day ? setLocale('YES') : setLocale('NO')}</span>
                </>
            ),
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}

                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Calendar Event')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale('calendarEvent.add')}
                            </Button>
                        )}

                        {permission.includes("View") && (
                            <Link to={`../../app/calendar-event_view/${'asd113'}`}>
                                <Button style={{ float: "right", margin: "5px" }} className="ant-btn-round ant-btn-sm" type="primary">
                                    View Calendar
                                </Button>
                            </Link>
                        )}
                    </section>
                    {CalendarEventAddDrawer && <AddCalendarEventModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={CalendarEventTableLoading}
                            rowKey={record => record.id}
                            dataSource={CalendarEventResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

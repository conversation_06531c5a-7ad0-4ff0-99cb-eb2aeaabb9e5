import React, { useState } from "react";
import { Space } from "antd";
import { Status } from "../Select/Status";
import { DateRange } from "../Select/DateRange";
import { LeaveType } from "../Select/LeaveType";
import { Designation } from "../Select/Designation";
import { EMPLOYEE_LEAVE } from "constants/AppConstants";
import { useSelector } from "react-redux";

const LeaveFilters = ({
  leaveTypeValue,
  onFilterChange,
  onLeaveTypeChange,
  selectedDesignation,
  selectedTab= 'history',
}) => {
  const [filters, setFilters] = useState({
    type: undefined,
    dateRange: undefined,
    status: undefined,
    searchText: "",
    leave_type_id: undefined,
    designation_id: undefined,
  });
  const { permission } = useSelector((state) => state[EMPLOYEE_LEAVE]);

  const updateFilters = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const onDateChange = (dates, dateStrings) => {
    updateFilters("dateRange", dates ? dateStrings : undefined);
  };

  return (
    <div className="d-flex justify-content-between">
      <Space size={8}>
        <DateRange onChange={onDateChange} />
        <LeaveType
          value={leaveTypeValue}
          onChange={(value) => {
            updateFilters("leave_type_id", value);
            if (onLeaveTypeChange) onLeaveTypeChange(value);
          }}
          isMultiple={true}
        />
        <Status onChange={(value) => updateFilters("status", value)} />
        {permission.includes("Employee Leave Requests") && selectedTab === 'requests' && (
          <Designation
            value={selectedDesignation}
            onChange={(value) => updateFilters("designation_id", value)}
            placeholder="Filter by Designation"
            isMultiple={true}
            allowClear
          />
        )}
      </Space>
    </div>
  );
};

export default LeaveFilters;

import React, { useState, useEffect, useRef } from "react";
import {
  Table,
  Form,
  Input,
  Button,
  message,
  Skeleton,
  Select,
  Row,
  Col,
  Checkbox,
  Divider,
  Space,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  MANAGE_CLASS,
  CONVERSATION_TEMPLATES,
  CONVERSATION_TYPES,
  COMMUNICATION,
  GRADE_LEVEL,
} from "constants/AppConstants";
import {
  getManageClass,
  updateSortFilters,
} from "store/slices/ManageClass/manageManageClassSlice";
import { getConversationTemplates } from "store/slices/ConversationTemplates/manageConversationTemplatesSlice";
import { getConversationTypes } from "store/slices/ConversationTypes/manageConversationTypesSlice";
import {
  sentCommunicationMail,
  getUsersOfClasses,
} from "store/slices/Communication/manageCommunicationSlice";
import IntlMessage from "components/util-components/IntlMessage";
import { USER_INFORMATION } from "../../../../constants/AuthConstant";
import { getRoles } from "store/slices/sliceUtils";
import { NAME_OF_ROLE_SLICE } from "constants/AppConstants";
import TextEditor from "../../../../components/TextEditor";
import { getGradeLevel } from "store/slices/GradeLevel/manageGradeLevelSlice";
import { SearchOutlined } from "@ant-design/icons";
import Highlighter from "react-highlight-words";

import { EditorState, convertToRaw } from "draft-js";
import { Editor } from "react-draft-wysiwyg";
import "../../../../../node_modules/react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import draftToHtml from "draftjs-to-html";


const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;
const schoolYear = userInformation.school_year;

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const MailCompose = () => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const { ConversationTemplatesResult } = useSelector(
    (state) => state[CONVERSATION_TEMPLATES]
  );
  const { ConversationTypesResult } = useSelector(
    (state) => state[CONVERSATION_TYPES]
  );
  const {
    CommunicationButtonSpinner,
    UsersOfClassesLoader,
    getUsersOfClassesResult,
  } = useSelector((state) => state[COMMUNICATION]);

  const searchInput = useRef(null);
  const { RolesResult } = useSelector((state) => state[NAME_OF_ROLE_SLICE]);
  const { GradeLevelResult } = useSelector((state) => state[GRADE_LEVEL]);
  const [pageLoading, setPageLoading] = useState(true);
  const [organization, setOrganization] = useState(selectedOrganization);
  const [selectedClasses, setSelectedClasses] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [editorState, setEditorState] = useState(EditorState.createEmpty());

  const [selectedGradeLevels, setSelectedGradeLevels] = useState([]);
  const [selectedRole, setSelectedRole] = useState({});
  const [classOptions, setClassOptions] = useState([]);
  const [gradeLevelOptions, setGradeLevelOptions] = useState([]);
  const [editorContent, setEditorContent] = useState(""); // New state to store editor content
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const [userOptions, setUserOptions] = useState([]);
  const adminRoles = ["Admin", "Accountant", "Principal", "HR"];
  const [tempEditorContent, setTempEditorContent] = useState(editorContent);
  const { filter, ManageClassResult } = useSelector(
    (state) => state[MANAGE_CLASS]
  );

  const handleEditorChange = (newEditorState) => {
    setEditorState(newEditorState);
  };

  const navigate = useNavigate();

  const back = () => {
    navigate(-1);
  };

  const onSubmit = async (formValues) => {
    // Add editor content to the form values

    const content = draftToHtml(convertToRaw(editorState.getCurrentContent()));
    const fineContent = content.replace(/("|\r\n|\n|\r)/gm, ""); // Remove newline characters

    formValues.mail.content = JSON.stringify(fineContent);
    formValues.mail.selectedIds = selectedRowKeys;
    // console.log(formValues);
    await dispatch(sentCommunicationMail(formValues))
      .then(() => {
        // message.success('Email has been sent');
        navigate("/app/communication/conversation/sent");
        setSelectedRowKeys([]);
      })
      .catch((error) => {
        console.error("Error sending email:", error);
        setSelectedRowKeys([]);
      });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        await dispatch(getConversationTemplates({ perPage: 1000 }));
        await dispatch(getConversationTypes({ perPage: 1000 }));
        await dispatch(
          updateSortFilters({
            ...filter,
            organization_id: selectedOrganization,
            schoolYear: schoolYear.id,
          })
        );
        dispatch(getManageClass(filter)).then(() => {
          setPageLoading(false);
        });
        await dispatch(getRoles());
      } catch (error) {
        console.error("Error fetching school year data:", error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    setClassOptions([]);
    if (ManageClassResult && ManageClassResult.length > 0) {
      setClassOptions([
        { value: "all", label: "All" },
        ...ManageClassResult.map((cls) => ({
          value: cls.id,
          label: cls.name,
        })),
      ]);
    }
  }, [ManageClassResult]);

  useEffect(() => {
    setGradeLevelOptions([]);
    if (GradeLevelResult?.data) {
      setGradeLevelOptions(
        [
          { value: "all", label: "All" },
          ...GradeLevelResult.data.map((g) => ({
            value: g.id,
            label: g.grade_level,
          })),
        ].sort((a, b) => {
          if (a.value === "all") return -1; // "All" always comes first
          if (b.value === "all") return 1;
          if (a.label === "KG") return -1; // "KG" comes after "All"
          if (b.label === "KG") return 1;
          return a.label.localeCompare(b.label, undefined, { numeric: true }); // Numeric sorting for the rest
        })
      );
    }
  }, [GradeLevelResult]);

  useEffect(() => {
    const users = [
      ...getUsersOfClassesResult?.map((user) => ({
        key: user.id,
        value: user.id,
        full_name: user.full_name,
      })),
    ];

    setUserOptions(users);
  }, [getUsersOfClassesResult]);

  const onChangeOrganization = async (value) => {
    dispatch(
      getManageClass({
        organization_id: value,
        schoolYear: schoolYear.id,
      })
    );

    dispatch(
      getGradeLevel({
        filter: { organization_id: value },
      })
    );

    form.resetFields([["mail", "class_id"]]);
    form.resetFields([["mail", "grade_id"]]);
    setOrganization(value);
  };

  const classOnChange = async (value) => {
    setUserOptions([]);
    if (value.includes("all")) {
      form.setFieldValue(["mail", "class_id"], ["all"]);
      setSelectedClasses(["all"]);
    } else {
      setSelectedClasses(value);
    }

    await dispatch(
      getUsersOfClasses({
        classes: value,
        role: selectedRole.label,
        organization_id: organization,
      })
    );
  };

  const gradeLevelOnChange = async (value) => {
    dispatch(
      getManageClass({
        organization_id: organization,
        schoolYear: schoolYear.id,
        gradeLevelArray: value,
        // gradeLevel: 1,
      })
    );

    form.resetFields([["mail", "class_id"]]);

    if (value.includes("all")) {
      form.setFieldValue(["mail", "grade_id"], ["all"]);
      setSelectedGradeLevels(["all"]);
    } else {
      setSelectedGradeLevels(value);
    }
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      setEditorContent(tempEditorContent);
    }, 0.8); // 200ms delay

    return () => clearTimeout(timeout);
  }, [tempEditorContent]);

  const onRoleChange = async (value, option) => {
    setUserOptions([]);
    form.setFieldValue(["mail", "class_id"], []);
    if (option.label == "Student") {
      dispatch(
        getGradeLevel({
          filter: { organization_id: organization },
        })
      );
    }

    if (adminRoles.includes(option.label)) {
      await dispatch(
        getUsersOfClasses({
          role: option.label,
          organization_id: organization,
        })
      );
    }
    setSelectedRole(option);
  };

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const handleReset = (clearFilters, selectedKeys, confirm, dataIndex) => {
    clearFilters(); // Clear the filter state
    setSearchText(""); // Reset the search text
    setSearchedColumn(""); // Reset the searched column
    handleSearch(selectedKeys, confirm, dataIndex);
  };

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
    }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: "block" }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() =>
              handleReset(clearFilters, selectedKeys, confirm, dataIndex)
            }
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{ color: filtered ? "#1890ff" : "black", fontSize: "large" }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
          .toString()
          .toLowerCase()
          .includes(value.toLowerCase())
        : "",
    onFilterDropdownVisibleChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
    render: (text) =>
      searchedColumn === dataIndex ? (
        <Highlighter
          highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
          searchWords={[searchText]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  const options = [
    ...userOrganizations?.map((organization) => ({
      value: organization.id,
      label: organization.org_name,
    })),
  ];

  const roleOptions = [
    ...RolesResult?.map((role) => ({
      value: role.id,
      label: role.name,
    })),
  ];

  const rowSelection = {
    getUsersOfClassesResult,
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
      {
        key: "odd",
        text: "Select Odd Row",
        onSelect: (changeableRowKeys) => {
          console.log(changeableRowKeys);
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter(
            (_, index) => index % 2 === 0
          );
          setSelectedRowKeys(newSelectedRowKeys);
        },
      },
      {
        key: "even",
        text: "Select Even Row",
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter(
            (_, index) => index % 2 !== 0
          );
          setSelectedRowKeys(newSelectedRowKeys);
        },
      },
    ],
  };

  const columns = [
    {
      title: setLocale("name"),
      key: "full_name",
      dataIndex: "full_name",
      ...getColumnSearchProps("full_name"), // Apply search props
      render: (text) => <>{text}</>,
    },
  ];

  return (
    <div className="mail-compose">
      <h4 className="mb-4">New Message</h4>
      <Skeleton active loading={pageLoading}>
        <Form
          name="nest-messages"
          onFinish={onSubmit}
          form={form}
          initialValues={{
            organization_id: organization || selectedOrganization,
          }}
        >
          <Row>
            <Col span={12}>
              {userOrganizations.length > 1 && (
                <Row>
                  <Col xs={24} sm={24} md={5}>
                    <span
                      style={{
                        height: "40px",
                        fontSize: "14px",
                        fontWeight: "500",
                        alignItems: "center",
                        display: "inline-flex",
                        color: "#1A3353",
                      }}
                    >
                      {setLocale("Campus")}:
                    </span>
                  </Col>
                  <Col xs={24} sm={24} md={15}>
                    <Form.Item
                      name={["mail", "organization_id"]}
                      rules={[
                        {
                          required: true,
                          message: setLocale("Please Select Organization"),
                        },
                      ]}
                    >
                      <Select
                        placeholder={setLocale("organizations.label")}
                        optionLabelProp="label"
                        onChange={onChangeOrganization}
                        options={options}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )}
              <Row>
                <Col xs={24} sm={24} md={5}>
                  <span
                    style={{
                      height: "40px",
                      fontSize: "14px",
                      fontWeight: "500",
                      alignItems: "center",
                      display: "inline-flex",
                      color: "#1A3353",
                    }}
                  >
                    {setLocale("User Role")}:
                  </span>
                </Col>
                <Col xs={24} sm={24} md={15}>
                  <Form.Item
                    name={["mail", "role"]}
                    rules={[
                      {
                        required: true,
                        message: setLocale("Please Select Role"),
                      },
                    ]}
                  >
                    <Select
                      placeholder={setLocale("User Roles")}
                      optionLabelProp="label"
                      options={roleOptions}
                      onChange={onRoleChange}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row>
                {!(selectedRole && adminRoles.includes(selectedRole.label)) && (
                  <Col xs={24} sm={24} md={5}>
                    <span
                      style={{
                        height: "40px",
                        fontSize: "14px",
                        fontWeight: "500",
                        alignItems: "center",
                        display: "inline-flex",
                        color: "#1A3353",
                      }}
                    >
                      {selectedRole && selectedRole.label == "Student"
                        ? setLocale("Class & Grade")
                        : setLocale("Class")}
                      :
                    </span>
                  </Col>
                )}
                {selectedRole && selectedRole.label == "Student" && (
                  <Col xs={24} sm={24} md={15}>
                    <Form.Item
                      name={["mail", "grade_id"]}
                      rules={[
                        {
                          required: true,
                          message: setLocale("grade_level.label_error"),
                        },
                      ]}
                    >
                      <Select
                        className="rounded-0"
                        showSearch
                        maxTagCount={1}
                        optionLabelProp="label"
                        placeholder={setLocale("grade_level.label")}
                        allowClear
                        mode="multiple"
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.label ?? "").toLowerCase().includes(input)
                        }
                        value={selectedGradeLevels}
                        onChange={gradeLevelOnChange}
                        options={
                          gradeLevelOptions.map((option) => ({
                            ...option,
                            disabled:
                              selectedGradeLevels.includes("all") &&
                              option.value !== "all",
                          })) || []
                        }
                      />
                    </Form.Item>
                  </Col>
                )}
                {!(selectedRole && adminRoles.includes(selectedRole.label)) && (
                  <>
                    {selectedRole && selectedRole.label == "Student" && (
                      <Col xs={24} sm={24} md={5}>
                        <span
                          style={{
                            height: "40px",
                            fontSize: "14px",
                            fontWeight: "500",
                            alignItems: "center",
                            display: "inline-flex",
                            color: "#1A3353",
                          }}
                        ></span>
                      </Col>
                    )}
                    <Col xs={24} sm={24} md={15}>
                      <Form.Item
                        name={["mail", "class_id"]}
                        rules={[
                          {
                            required: true,
                            message: setLocale("classes.label_error"),
                          },
                        ]}
                        className="ml-1"
                      >
                        <Select
                          className="rounded-0"
                          showSearch
                          maxTagCount={1}
                          optionLabelProp="label"
                          placeholder={setLocale("classes.label")}
                          allowClear
                          mode="multiple"
                          optionFilterProp="children"
                          filterOption={(input, option) =>
                            (option?.label ?? "").toLowerCase().includes(input)
                          }
                          filterSort={(optionA, optionB) =>
                            (optionA?.label ?? "")
                              .toLowerCase()
                              .localeCompare(
                                (optionB?.label ?? "").toLowerCase()
                              )
                          }
                          value={selectedClasses}
                          onChange={classOnChange}
                          options={
                            classOptions.map((option) => ({
                              ...option,
                              disabled:
                                selectedClasses.includes("all") &&
                                option.value !== "all", // Disable others if 'All' is selected
                            })) || []
                          }
                        />
                      </Form.Item>
                    </Col>
                  </>
                )}
              </Row>

              <Row>
                <Col xs={24} sm={24} md={5}>
                  <span
                    style={{
                      height: "40px",
                      fontSize: "14px",
                      fontWeight: "500",
                      alignItems: "center",
                      display: "inline-flex",
                      color: "#1A3353",
                    }}
                  >
                    {setLocale("Subject")}:
                  </span>
                </Col>
                <Col xs={24} sm={24} md={15}>
                  <Form.Item name={["mail", "subject"]}>
                    <Input placeholder="Subject:" />
                  </Form.Item>
                </Col>
              </Row>

              {selectedRole && selectedRole.label == "Student" && (
                <Row>
                  <Col xs={24} sm={24} md={5}>
                    <span
                      style={{
                        height: "40px",
                        fontSize: "14px",
                        fontWeight: "500",
                        alignItems: "center",
                        display: "inline-flex",
                        color: "#1A3353",
                      }}
                    >
                      {setLocale("Guardian")}:
                    </span>
                  </Col>
                  <Col xs={24} sm={24} md={5}>
                    <Form.Item
                      name={["mail", "to_mother"]}
                      valuePropName="checked"
                    >
                      <Checkbox>Send to Mother</Checkbox>
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={24} md={5}>
                    <Form.Item
                      name={["mail", "to_father"]}
                      valuePropName="checked"
                    >
                      <Checkbox>Send to Father</Checkbox>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              <Row>
                <Col xs={24} sm={24} md={5}>
                  <span
                    style={{
                      height: "40px",
                      fontSize: "14px",
                      fontWeight: "500",
                      alignItems: "center",
                      display: "inline-flex",
                      color: "#1A3353",
                    }}
                  >
                    {setLocale("Conversation Template")}:
                  </span>
                </Col>
                <Col xs={24} sm={24} md={15}>
                  <Form.Item
                    name={["mail", "conversation_template_id"]}
                    rules={[
                      {
                        required: true,
                        message: setLocale(
                          "conversation_template.conversation_template_error"
                        ),
                      },
                    ]}
                  >
                    <Select
                      className="rounded-0"
                      showSearch
                      optionLabelProp="label"
                      placeholder={setLocale(
                        "conversation_template.conversation_template"
                      )}
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        (option?.label ?? "").toLowerCase().includes(input)
                      }
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? "")
                          .toLowerCase()
                          .localeCompare((optionB?.label ?? "").toLowerCase())
                      }
                      options={ConversationTemplatesResult?.data || []}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row>
                <Col xs={24} sm={24} md={5}>
                  <span
                    style={{
                      height: "40px",
                      fontSize: "14px",
                      fontWeight: "500",
                      alignItems: "center",
                      display: "inline-flex",
                      color: "#1A3353",
                    }}
                  >
                    {setLocale("Conversation Type")}:
                  </span>
                </Col>
                <Col xs={24} sm={24} md={15}>
                  <Form.Item
                    name={["mail", "conversation_type_id"]}
                    rules={[
                      {
                        required: true,
                        message: setLocale("conversation_types.label_error"),
                      },
                    ]}
                  >
                    <Select
                      className="rounded-0"
                      showSearch
                      optionLabelProp="label"
                      placeholder={setLocale("conversation_types.label")}
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        (option?.label ?? "").toLowerCase().includes(input)
                      }
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? "")
                          .toLowerCase()
                          .localeCompare((optionB?.label ?? "").toLowerCase())
                      }
                      options={ConversationTypesResult?.data || []}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col span={12}>
              <Table
                rowSelection={rowSelection}
                columns={columns}
                dataSource={userOptions ?? []}
                pagination={{
                  pageSize: 200,
                }}
                scroll={{
                  y: 270,
                }}
              />
            </Col>
          </Row>
          <Form.Item name={["mail", "content"]}>
            <Editor
              editorState={editorState}
              onEditorStateChange={handleEditorChange}
              wrapperClassName="wrapper-class"
              editorStyle={{
                height: "200px",
                borderBottom: "1px solid #4d5b75",
                borderRight: "1px solid #4d5b75",
                borderLeft: "1px solid #4d5b75",
              }}
              editorClassName="editor-class"
              toolbarClassName="toolbar-class"
            />
          </Form.Item>
          <Form.Item>
            <div className="mt-5 text-right">
              <Button className="mr-2" onClick={back}>
                Discard
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={CommunicationButtonSpinner}
              >
                Send
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Skeleton>
    </div>
  );
};

export default MailCompose;

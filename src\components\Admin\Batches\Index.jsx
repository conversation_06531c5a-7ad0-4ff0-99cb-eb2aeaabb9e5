import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Modal, Select, Row, Col } from 'antd';
import { DeleteOutlined, EditOutlined, ExclamationCircleOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { BATCHES, INVOICES } from "constants/AppConstants";
import {
    getBatches,
    setColumnSearch,
    updateSortFilters,
    postBatch,
    deleteBatches,
} from "store/slices/Batches/manageBatchesSlice.js";
import {
    InvoicesAddDrawerStatus,
    InvoicesEditWithDrawerStatus
} from "store/slices/Invoices/manageInvoicesSlice.js";
import AddInvoicesModal from "../Invoices/Modals/index";

import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import { USER_INFORMATION } from 'constants/AuthConstant';
import OrganizationSelect from "components/Admin/OrganizationDropdown/OrganizationSelect";
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { Option } = Select;

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const [selectedStatusMap, setSelectedStatusMap] = useState({});
    const handleOpenModal = () => dispatch(InvoicesAddDrawerStatus({ errorStatus: 1, status: true }));
    const { BatchesResult, tablePagination, sorting, filter, BatchesTableLoading, permission } = useSelector(
        (state) => state[BATCHES]
    );

    const { InvoicesAddDrawer } = useSelector(
        (state) => state[INVOICES]
    );

    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getBatches({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);

    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) {
            console.log(error);
        }

    };

    const handleDelete = (record) => {
        dispatch(deleteBatches(record.id)).then(() => {
            getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        })
    }

    const columns = [
        {
            title: setLocale('name'),
            dataIndex: "batch_name",
            key: "batch_name",
            sorter: true,
            ...getColumnSearchProps('batch_name'),
        },
        {
            title: setLocale('invoices.batch_no'),
            dataIndex: "batch_no",
            key: "batch_no",
            sorter: true,
            ...getColumnSearchProps('batch_no'),
        },
        {
            title: setLocale('Batch Date'),
            key: "batch_date",
            sorter: true,
            ...getColumnSearchProps('batch_date'),
            render: (value, record) => (
                <Space>
                    {record?.invoices[0]?.invoice_date ? record?.invoices[0]?.invoice_date : ''}
                </Space>
            )
        },
        // {
        //     title: setLocale('Grade'),
        //     dataIndex: "grade_level",
        //     key: "grade_level",
        //     sorter: true,
        //     ...getColumnSearchProps('grade_level'),
        //     render: (data, record) => (
        //         <>{record.invoices[0].organization_grade_level?.grade_level?.grade_level}</>
        //     )
        // },
        {
            title: setLocale('Batch Status'),
            dataIndex: "batch_status",
            key: "batch_status",
            sorter: true,
            ...getColumnSearchProps('batch_status'),
            render: (value, record) => (
                <Space>
                    {record.batch_status === 'Posted' ? record.batch_status :
                        <>
                            <Select size={'small'} style={{ width: 100 }}
                                defaultValue={record.batch_status === 'Posted' ? 'Posted' : 'Pending'}
                                value={selectedStatusMap[record.id] !== undefined ? selectedStatusMap[record.id] : (record.batch_status === 'Posted' ? 'Posted' : 'Pending')}
                                onChange={(e) => {
                                    setSelectedStatusMap(prevState => ({
                                        ...prevState,
                                        [record.id]: e,
                                    }));
                                    changeStatus(e, record)
                                }}
                            >
                                <Option value='Posted' label={setLocale('Posted')}>{setLocale('Posted')}</Option>
                                <Option value='Pending' label={setLocale('Pending')}>{setLocale('Pending')}</Option>
                            </Select>
                        </>}
                </Space>
            )
        },
        {
            title: setLocale('Total Invoices'),
            dataIndex: "invoices_count",
            key: "invoices_count",
            sorter: true,
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>

                    {permission.includes("View") && (
                        <Link to={`../../app/batches_view/${record.enc_id + '_' + record.organization_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )}
                    {permission.includes("Delete") && record.batch_status !== 'Posted' && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ marginLeft: '10px', cursor: 'pointer', fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && record.batch_status !== 'Posted' && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                </>
            )
        },
    ];

    const handleUpdate = (record) => {
        dispatch(InvoicesEditWithDrawerStatus(record));
    }

    const post = async (value, id) => {
        await dispatch(postBatch({ status: value, id: id }));
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }
    const changeStatus = async (value, record) => {
        Modal.confirm({
            title: setLocale('are_you_sure'),
            icon: <ExclamationCircleOutlined />,
            onOk() {
                post(value, record.id);
            },
            onCancel() {
                setSelectedStatusMap(prevState => ({
                    ...prevState,
                    [record.id]: `${record.batch_status}`
                }));
            },
        });
    }

    return (
        <>
            <Row gutter={16}>
                <Col xs={24} sm={24} md={12} lg={12}>
                    <Breadcrumb className="my-2 mx-2">
                        <Breadcrumb.Item>
                            <Link to="/app/default">{setLocale('home')}</Link>
                        </Breadcrumb.Item>
                        <Breadcrumb.Item>{setLocale('Batches')}</Breadcrumb.Item>
                    </Breadcrumb>
                </Col>
                <Col xs={24} sm={24} md={12} lg={12} style={{ textAlign: 'right' }}>
                    <OrganizationSelect
                        updateSortFilters={updateSortFilters}
                        filter={filter}
                        sorting={sorting}
                        tablePagination={tablePagination}
                        getModuleData={getModuleData}
                    />
                </Col>
            </Row>

            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale('invoices.add')}
                            </Button>
                        )}
                    </section>
                    {InvoicesAddDrawer && <AddInvoicesModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={BatchesTableLoading}
                            rowKey={record => record.id}
                            dataSource={BatchesResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

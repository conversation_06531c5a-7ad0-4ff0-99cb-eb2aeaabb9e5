import React, { useEffect, useState } from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space, Select, Switch,Skeleton } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { CONVERSATION_TEMPLATES } from "constants/AppConstants";
import {
    ConversationTemplatesAddDrawerStatus,
    createConversationTemplates,
    getConversationTemplates,
    onCloseError
  } from "store/slices/ConversationTemplates/manageConversationTemplatesSlice.js";
  import { getConversationChannel } from "store/slices/ConversationChannel/manageConversationChannelSlice.js";
  import { getTemplatesTypes } from "store/slices/TemplatesTypes/manageTemplatesTypesSlice.js";
  import { getModule } from "store/slices/ManageModuleSlice/manageModuleSlice.js";
  
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
const [switchValue, setSwitchValue] = useState('checked');
const [pageLoading, setPageLoading] = useState(true);

  const { moduleResult } = useSelector(state => state.manageModuleSlice)
  const { ConversationChannelResult } = useSelector(state => state.manageConversationChannelSlice)
  const { TemplatesTypesResult } = useSelector(state => state.manageTemplatesTypesSlice)
  const {DrawerStatus,sorting, filter, ConversationTemplatesAddDrawer, ConversationTemplatesButtonAndModelLabel, ConversationTemplatesErrors, ConversationTemplatesShowMessage, ConversationTemplatesButtonSpinner, ConversationTemplatesEditData, tablePagination } = useSelector(
      (state) => state[CONVERSATION_TEMPLATES]
    );
const onClose = () => {
    dispatch(ConversationTemplatesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (ConversationTemplatesEditData && ConversationTemplatesEditData.id) {
    // If editing, include the id in the form values
    formValues.id = ConversationTemplatesEditData.id;
  }

  await dispatch(createConversationTemplates(formValues))
  };
  useEffect(() => {
    if (Object.keys(ConversationTemplatesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getConversationTemplates({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ConversationTemplatesErrors]);
  useEffect(() => {
    const obj = { communication_avail :  1 }
    dispatch(getModule(obj));
    fetchData();
    ConversationTemplatesEditData.is_active === 1 ? setSwitchValue('checked') : setSwitchValue();
  }, [dispatch]);


  const fetchData = async () => {
    try {

      dispatch(getConversationChannel());
      await dispatch(getTemplatesTypes()).then(() => {
        setPageLoading(false);
      });  

    } catch (error) {
      console.error('Error fetching school year data:', error);
    }

  };


const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
  };
const getSwitchValue = (checked) => {
  setSwitchValue(checked);
};

  return (
    <>

      <Drawer
        title={ConversationTemplatesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ConversationTemplatesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Skeleton active loading={pageLoading}>
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            ...ConversationTemplatesEditData,
            conversation_channel_id: ConversationChannelResult && ConversationTemplatesEditData.conversation_channel_id ? ConversationTemplatesEditData?.conversation_channel_id : ConversationChannelResult.data?.length > 0 ? ConversationChannelResult.data[0]?.id : null,
            template_type_id: TemplatesTypesResult && ConversationTemplatesEditData.template_type_id ? ConversationTemplatesEditData?.template_type_id : TemplatesTypesResult.data?.length > 0 ? TemplatesTypesResult.data[0]?.id : null,

          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="template_name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={ConversationTemplatesShowMessage && ConversationTemplatesErrors.template_name ? "error" : ""}
                help={ConversationTemplatesShowMessage && ConversationTemplatesErrors.template_name}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
                  <Form.Item
                    name="conversation_channel_id"
                    label={setLocale("conversation_template.conversation_channel")}
                    rules={[
                      {
                        required: true,
                        message: setLocale("conversation_template.conversation_channel_error"),
                      },
                    ]}
                    validateStatus={ConversationTemplatesShowMessage && ConversationTemplatesErrors.conversation_channel_id ? "error" : ""}
                		    extra={ConversationTemplatesShowMessage && ConversationTemplatesErrors.conversation_channel_id}


                  >
                    <Select className='rounded-0' showSearch optionLabelProp="label"
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                      }
                      options={ConversationChannelResult.data ? ConversationChannelResult.data : null}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="template_type_id"
                    label={setLocale("conversation_template.template_type")}
                    rules={[
                      {
                        required: true,
                        message: setLocale('conversation_template.template_type_error'),
                      },
                    ]}
                    validateStatus={ConversationTemplatesShowMessage && ConversationTemplatesErrors.template_type_id ? "error" : ""}
                		    extra={ConversationTemplatesShowMessage && ConversationTemplatesErrors.template_type_id}


                  >
                    <Select className='rounded-0' showSearch optionLabelProp="label"
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                      }
                      options={TemplatesTypesResult.data ? TemplatesTypesResult.data : null}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="module_id"
                    label={setLocale("conversation_template.conversation_module")}
                    rules={[
                      {
                        required: true,
                        message: setLocale("conversation_template.conversation_module_error"),
                      },
                    ]}
                    validateStatus={ConversationTemplatesShowMessage && ConversationTemplatesErrors.module_id ? "error" : ""}
                		    extra={ConversationTemplatesShowMessage && ConversationTemplatesErrors.module_id}


                  >
                    <Select className='rounded-0' showSearch optionLabelProp="label"
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                      }
                      options={moduleResult ? moduleResult : null}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="is_active"
                    // valuePropName={switchValue}
                    label={setLocale("conversation_template.status")}
                    rules={[
                      {
                        required: false,
                        message: "Status Error",
                      },
                    ]}
                    validateStatus={ConversationTemplatesShowMessage && ConversationTemplatesErrors.is_active ? "error" : ""}
                		    extra={ConversationTemplatesShowMessage && ConversationTemplatesErrors.is_active}


                  >
                    <Switch
                      checkedChildren={<CheckOutlined />}
                      unCheckedChildren={<CloseOutlined />}
                      onChange={getSwitchValue}
                      checked={switchValue}
                      name="is_active"
                    />
                  </Form.Item>
                </Col>
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={ConversationTemplatesButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
        </Skeleton>
      </Drawer>
    </>
  );
};
export default Index;


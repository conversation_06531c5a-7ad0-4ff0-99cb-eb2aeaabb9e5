import React, { useRef, useState, useEffect } from 'react'
import { useSelector, useDispatch } from "react-redux";
import { Scrollbars } from 'react-custom-scrollbars';
import { Avatar,Card, Skeleton,Row, Col, Spin, Button, Menu } from 'antd';
import { CHAT } from "constants/chat/index";
import { env } from "configs/EnvironmentConfig"
// import Flex from 'components/shared-components/Flex';
import {
	FileOutlined,
	FilePdfOutlined,
	FileWordOutlined,
	FileExcelOutlined,
	FileImageOutlined,
	FileTextOutlined,
	FileZipOutlined,
	DownloadOutlined
} from '@ant-design/icons';
import ChatContentFooter from './ChatContentFooter';
import { USER_INFORMATION } from "constants/AuthConstant";
import Echo from 'broadcasting/laravelEcho'
import {
	setPushNewMessage,
	markReadMessages,
	setConversationOrder		
} from "store/slices/Chat/manageChatSlice.js";
import UploadFileForm from './UploadFileForm';
// import AvatarStatus from 'components/shared-components/AvatarStatus/chatAvatar';
// import moment from 'moment';
// import moment from 'moment-timezone';
import { humanizeDate } from "components/Helper/Index";

const currentUser = JSON.parse(localStorage.getItem(USER_INFORMATION)) || null;

const ChatContentBody = () => {
	const dispatch = useDispatch();
	const { 
		selectedConversation, 
		conversationBodyLoader, 
		conversationBody,
		uploadFileFormDrawer,
		sendMessageLoader
	} = useSelector((state) => state[CHAT]);
	const chatBodyRef = useRef();

	useEffect(() => {
		scrollToBottom()
	}, [conversationBody])

	const getFileIcon = (fileName) => {
		const ext = fileName.split('.').pop().toLowerCase();
	  
		const icons = {
		  pdf: <FilePdfOutlined />,
		  doc: <FileWordOutlined />,
		  docx: <FileWordOutlined />,
		  xls: <FileExcelOutlined />,
		  xlsx: <FileExcelOutlined />,
		  jpg: <FileImageOutlined />,
		  jpeg: <FileImageOutlined />,
		  png: <FileImageOutlined />,
		  txt: <FileTextOutlined />,
		  zip: <FileZipOutlined />,
		};
	  
		return icons[ext] || <FileOutlined />;
	  };
	  const formatFileSize = (bytes) => {
		if (bytes >= 1024 * 1024) {
		  return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
		} else {
		  return (bytes / 1024).toFixed(1) + ' KB';
		}
	  };
	const scrollToBottom = () => {
		chatBodyRef.current.scrollToBottom()
	}

	useEffect(() => {
		const channel = Echo.private(`user-chat-${selectedConversation?.conversation_id}`);
		channel.listen('PrivateChatMessageEvent', async (data) => {			
			if (
				Number(data.conversation_id) === Number(selectedConversation?.conversation_id) &&  
				(data.user_id !== currentUser.id || 
					String(data.type).toLowerCase() === 'image' || 
					String(data.type).toLowerCase() === 'file')
				)
				// if message Type File then push to conversation body with actual file path
			{					
				await dispatch(setPushNewMessage(data));
				await dispatch(markReadMessages({ conversation_id: selectedConversation?.conversation_id }));
			} 

			await dispatch(setConversationOrder(data));		
			return () => {
				channel.stopListening('PrivateChatMessageEvent');
			}
		})
	}, [])		
	return (
		<>
			<Skeleton loading={conversationBodyLoader} active />
			<Skeleton loading={conversationBodyLoader} active />
			<Skeleton loading={conversationBodyLoader} active />
			{!conversationBodyLoader &&
				<>	
				<div>
				 <div className="chat-content-body">
				  <Scrollbars ref={chatBodyRef} autoHide>
							{
								conversationBody.map((elm, i) => (
									<div key={`msg-${elm.id}-${i}`} className={`msg ${elm.user_id === currentUser.id ? 'msg-sent' : 'msg-recipient'}`} >
										{elm.user_id !== currentUser.id &&
											<div className="mr-2">												
												<Avatar src={elm.profile_picture ? env.FILE_ENDPOINT_URL + elm.profile_picture : "/img/avatars/user.png"} />
											</div>
										}
										{
											<div className={`bubble`}>			
												<div style={{ userSelect: 'none', fontStyle: 'italic', fontSize: 'small', fontWeight: 'bold' }}>{elm.name} </div>
												<div className="bubble-wrapper" style={{ marginBottom: '0px' }}>
													{ ((elm.type === 'image' || elm.type === 'file') && elm.files) && (												
														<Row gutter={[2,2]}>
															{elm.files?.map((file, idx) => (
															<Col span={24} key={`file-${idx}`} >
																<Card size="small" style={{ width: 300 }} bodyStyle={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '10px', }} >
																<div style={{ display: 'flex', alignItems: 'center', gap: 4, flex: 1 }}>
																	<div style={{ fontSize: 24 }}>{getFileIcon(file.name)}</div>
														
																	<div style={{ overflow: 'hidden' }}>
																	<div style={{fontWeight: 500, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: 180, }} >
																		{file.name.length > 25 ? file.name.substring(0, 22) + '...' : file.name}
																	</div>
																	<div style={{ fontSize: 12, color: '#888' }}>{formatFileSize(file.size)}</div>
																	</div>
																</div>													
																<Button loading={sendMessageLoader && i === conversationBody.length - 1} type="primary" shape="circle" icon={<DownloadOutlined />} href={env.FILE_ENDPOINT_URL + file.path} target="_blank" download />
																</Card>
															</Col>
															))}
														</Row>																					
													)}												
													<div style={{ whiteSpace: 'pre-wrap' }} dangerouslySetInnerHTML={{ __html: elm.message }} ></div>
												</div>
												<div style={{ userSelect: 'none', fontSize: 8, float: 'right'}}>{humanizeDate(elm.created_at)}</div>
											</div>
										}

										
										{/* {
											elm.msgType === 'date'?
											<Divider>{elm.time}</Divider>
											: 
											null
										} */}
									</div>
								))
							}
				  </Scrollbars>
				 </div>
				 <ChatContentFooter />		
				 { uploadFileFormDrawer && <UploadFileForm /> }
				</div>
				</>
			}
			
		</>
	)
}

export default ChatContentBody

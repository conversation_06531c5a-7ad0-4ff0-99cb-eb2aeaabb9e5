import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { Select, Form, Row, Col, Card } from 'antd';
import Chart from "react-apexcharts";
// import { 
//   ArrowDownOutlined 
// } from '@ant-design/icons';
// import IntlMessage from "components/util-  components/IntlMessage";
// import {
//   getAttendanceDashboardReport,
//   getClassStudents
// } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import { 
//     MANAGE_CLASS, 
//     SCHOOL_YEAR 
// } from "constants/AppConstants";

// const setLocale = (localeKey, isLocaleOn = true) =>
//     isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const colorMap = {
  "Present": "#54B227",
  "Unexcused Absence": "#FF754A",
  "Excused Absence": "#D5433A",
  "Late": "#FBD409",
  "Unspecified": "#B0AFAD"
};

function AttendanceClassBar() {
  // const [form] = Form.useForm();
  // const dispatch = useDispatch();
  const {
    //   AttendanceReportButtonSpinner,
    attendanceDashboardReport,
    attendanceClassBar
    //   classStudents 
  } = useSelector((state) => state[ATTENDANCE_REPORT]);

  const [attCodes, setAttCodes] = useState([
    { label: "Present", value: "Present" },
    { label: "Unexcused Absence", value: "Unexcused Absence" },
    { label: "Excused Absence", value: "Excused Absence" },
    { label: "Late", value: "Late" },
    { label: "Unspecified", value: "Unspecified" }
  ]);
  const [color, setColor] = useState('#47A580');
  const [attType, setAttType] = useState('Present');
  const [grades, setGrades] = useState([]);
  const [gradeData, setGradeData] = useState([]);
  const [state, setState] = useState({
    series: [{
      name: attType,
      data: []
    }],
    options: {
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          endingShape: 'rounded'
        },
      },
      chart: {
        toolbar: {
          show: false
        }
      },
      colors: color,
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: []
      },
      fill: {
        opacity: 1
      },
      // tooltip: {
      //   y: {
      //     formatter: val => (`$${val} thousands`)
      //   }
      // }
    }
  });

  useEffect(() => {
    extractAttendanceData();
  }, [attendanceClassBar, attType]);

  const extractAttendanceData = () => {
    const extractedData = [];
    const gradeKeys = [];

    Object.keys(attendanceClassBar).forEach(key => {
      gradeKeys.push(key);
      const count = attendanceClassBar[key][attType] || 0;
      extractedData.push(count);
    });


    setGrades(gradeKeys);
    setGradeData(extractedData);

    setState({
      series: [{
        name: attType,
        data: extractedData
      }],
      options: {
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          },
        },
        chart: {
          toolbar: {
            show: false
          }
        },
        colors: color,
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: gradeKeys
        },
        fill: {
          opacity: 1
        },
        // tooltip: {
        //   y: {
        //     formatter: val => (`$${val} thousands`)
        //   }
        // }
      }
    });
  }

  const handleChange = (value) => {
    setColor(colorMap[value])
    setAttType(value);
    extractAttendanceData();
  };

  return (
    <>
      <Card className="p-2" style={{ height: "350px" }}>
        <div className="d-flex justify-content-between mb-3">
          <div>
            <h1 style={{ color: "#3C3C3C" }}>Attendance by Class</h1>
            <span>Summary of attendance by class</span>
          </div>
          <div>
            <Select defaultValue={attType} onChange={handleChange}
              style={{ color: "black", fontWeight: 900, width: "200px" }}
              options={attCodes}
            />
          </div>
        </div>
        <div style={{ width: "98%" }}>
          <Chart options={state.options} series={state.series} height={230} type="bar" />
        </div>
      </Card>
    </>
  );
}

export default AttendanceClassBar;

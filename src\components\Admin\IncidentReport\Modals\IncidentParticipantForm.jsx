import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Popconfirm, Col, Modal, Form, Input, Row, Space, Select, Divider, Table, Pagination, Skeleton } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { INCIDENT_REPORT, USER } from "constants/AppConstants";
import { SearchOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import {
  setOtherParticipantData,
  setStudentSelectedRowKeys,
  updateStudentSelectedRowKeys,
  updateSelectedRowKeys,
  deleteParticipant,
  setSelectedRowKeys,
  getParticipantData,
  saveParticipant,
  viewIncidentReport,
  saveOtherParticipant,
  setCurrentPageStudentRecord,
  setCurrentPageUserRecord,
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import { useParams } from "react-router-dom";
import { getAllUsers, updateSortFilters, setColumnSearch } from "store/slices/User/manageUserSlice";
import { isJSON } from "components/composeable"
import IntlMessage from "components/util-components/IntlMessage";
import Highlighter from 'react-highlight-words';
import { getStudents } from 'store/slices/Student/manageStudentSlice';
import PhoneNumberInput from "components/Admin/PhoneNumberInput";
import { STUDENT } from 'constants/student';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const IncidentParticipantForm = ({ submitForm }) => {
  const [form] = Form.useForm();
  const [form1] = Form.useForm();
  const dispatch = useDispatch();
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [userOption, setUserOption] = useState(false);
  const [studentOption, setStudentOption] = useState(false);
  const [OthersOption, setOthersOption] = useState(false);
  const [OtherUpdate, setOtherUpdate] = useState(false);
  const [otherParticipantId, setOtherParticipantId] = useState(null);
  // const [currentPageStudentRecord, setCurrentPageStudentRecord] = useState([]);
  // const [currentPageUserRecord, setCurrentPageUserRecord] = useState([]);
  const {
    IncidentParticipantFormStatus,
    ViewIncidentReportData,
    otherParticipantButtonSpinner,
    otherParticipant,
    ParticipantButtonSpinner,
    currentPageStudentRecord,
    currentPageUserRecord,
    participantData,
    IncidentReportButtonAndModelLabel,
    selectedRowKeys,
    selectedStudentsRowKeys,
    IncidentReportEditData,
    differencePageStudentRecord,
    differencePageUserRecord,
  } = useSelector(
    (state) => state[INCIDENT_REPORT]
  );


  const {
    StudentResult,
    tableStudentPagination,
    // sorting,
    // filter,
    StudentTableLoading
  } = useSelector((state) => state[STUDENT]);

  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(getAllUsers({ page: page, perPage: perPage, filter: isJSON(filterData), sorting: isJSON(sortingData) })).then((result) => {
      const ids = result?.payload?.data.map(user => user.id);
      dispatch(setCurrentPageUserRecord(ids));
    });
  }

  const getStudentModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(getStudents({ page: page, perPage: perPage, filter: isJSON(filterData), sorting: isJSON(sortingData) })).then((result) => {
      const ids = result?.payload?.data.map(student => student.id);
      dispatch(setCurrentPageStudentRecord(ids));
    });
  }

  const searchInput = useRef(null);
  const {
    UserResult,
    tablePagination,
    UserTableLoading,
    sorting, filter,
  } = useSelector((state) => state[USER])

  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, tablePagination.pageSize, filter, sorting);
  }
  const handleStudentSearch = async (confirm) => {
    confirm();
    getStudentModuleData(1, tableStudentPagination.pageSize, filter, sorting);
  }

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
  }

  const handleStudentReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getStudentModuleData(tableStudentPagination.current, tableStudentPagination.pageSize, newObject, sorting);
  }

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === '') {
      confirm();
      getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
    }
  }

  const handleStudentOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === '') {
      confirm();
      getStudentModuleData(getStudentModuleData.current, getStudentModuleData.pageSize, { ...filter, [dataIndex]: value }, sorting);
    }
  }


  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: 'block',
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('search')}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex)
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('reset')}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
        }}
      />
    ),
    onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex]
        ? (
          <Highlighter
            highlightStyle={{
              backgroundColor: '#ffc069',
              padding: 0,
            }}
            searchWords={[filter[dataIndex]]}
            autoEscape
            textToHighlight={text ? text.toString() : ''}
          />
        ) : (
          text
        ),
  })

  const getStudentColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) => handleStudentOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
          onPressEnter={(e) => handleStudentSearch(confirm)}
          style={{
            marginBottom: 8,
            display: 'block',
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleStudentSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('search')}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleStudentReset(dataIndex)
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('reset')}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
        }}
      />
    ),
    onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex]
        ? (
          <Highlighter
            highlightStyle={{
              backgroundColor: '#ffc069',
              padding: 0,
            }}
            searchWords={[filter[dataIndex]]}
            autoEscape
            textToHighlight={text ? text.toString() : ''}
          />
        ) : (
          text
        ),
  })

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };


  const fetchData = async () => {
    await getModuleData(1, tablePagination.pageSize, filter, sorting);
    await getStudentModuleData(1, tableStudentPagination.pageSize, filter, sorting).then((result) => {
      setLoading(false);
    });
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleFormSubmit = async (formValues) => {
    formValues.students = selectedStudentsRowKeys;
    formValues.staff = selectedRowKeys;
    formValues.other = otherParticipant;
    if (IncidentReportEditData && IncidentReportEditData.id) {
      // If editing, include the id in the form values
      formValues.incident_id = IncidentReportEditData.id;
    }


    console.log(selectedStudentsRowKeys.length, selectedRowKeys.length, otherParticipant.length);

    if (selectedStudentsRowKeys.length > 0 || selectedRowKeys.length > 0 || otherParticipant.length > 0) {
      await dispatch(saveParticipant(formValues)).then((result) => {
        if (params?.id) {
          dispatch(viewIncidentReport(params?.id))
        }
      })
    }
  };

  const participantRole = [
    {
      label: 'Offender',
      value: 'Offender'
    },
    {
      label: 'Victim',
      value: 'Victim'
    },
    {
      label: 'Reporter',
      value: 'Reporter'
    },
    {
      label: 'Witness',
      value: 'Witness'
    },
  ];


  const participantType = [
    {
      label: 'Student',
      value: 'Student'
    },
    {
      label: 'Staff',
      value: 'Staff'
    },
    {
      label: 'Others',
      value: 'Others'
    }
  ]

  const onChangeType = (value) => {
    if (value === 'Student') {
      setStudentOption(true);
      setOthersOption(false);
      setUserOption(false);
      getStudentModuleData(getStudentModuleData.current, getStudentModuleData.pageSize, {}, sorting);
      setOtherUpdate(false);
    }
    else if (value === 'Others') {
      setStudentOption(false);
      setOthersOption(true);
      setUserOption(false);
      setOtherUpdate(false);
    }
    else if (value === 'Staff') {
      setStudentOption(false);
      setOthersOption(false);
      setUserOption(true);
      setOtherUpdate(false);
      getModuleData(tablePagination.current, tablePagination.pageSize, {}, sorting);
    }
    dispatch(setColumnSearch({}));
  }

  const onSelectChange = async (newSelectedRowKeys) => {
    const isDuplicate = newSelectedRowKeys?.some(selectedRow => selectedRow === selectedRowKeys);
    if (!isDuplicate) {
      await dispatch(setSelectedRowKeys({ selected: newSelectedRowKeys }));
    }
  };

  const onSelectStudentChange = async (newSelectedRowKeys) => {
    const isDuplicate = newSelectedRowKeys?.some(selectedRowKeys => selectedRowKeys === selectedStudentsRowKeys);
    if (!isDuplicate) {
      await dispatch(setStudentSelectedRowKeys({ selected: newSelectedRowKeys }));
    }
  };

  const userRowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
      {
        key: 'odd',
        text: 'Select Odd Row',
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => {
            if (index % 2 !== 0) {
              return false;
            }
            return true;
          });
          dispatch(setSelectedRowKeys({ selected: newSelectedRowKeys }));
        },
      },
      {
        key: 'even',
        text: 'Select Even Row',
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => {
            if (index % 2 !== 0) {
              return true;
            }
            return false;
          });
          dispatch(setSelectedRowKeys({ selected: newSelectedRowKeys }));
        },
      },
    ],
  };

  const studentRowSelection = {
    selectedRowKeys: selectedStudentsRowKeys,
    onChange: onSelectStudentChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
      {
        key: 'odd',
        text: 'Select Odd Row',
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => {
            if (index % 2 !== 0) {
              return false;
            }
            return true;
          });
          dispatch(setStudentSelectedRowKeys({ selected: newSelectedRowKeys }));
        },
      },
      {
        key: 'even',
        text: 'Select Even Row',
        onSelect: (changeableRowKeys) => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changeableRowKeys.filter((_, index) => {
            if (index % 2 !== 0) {
              return true;
            }
            return false;
          });
          dispatch(setStudentSelectedRowKeys({ selected: newSelectedRowKeys }));
        },
      },
    ],
  };

  const hasSelected = selectedRowKeys.length > 0;
  const hasStudentSelected = selectedStudentsRowKeys.length > 0;

  const userColumns = [
    {
      dataIndex: 'name',
      key: 'name',
      title: setLocale('name'),
      ...getColumnSearchProps('name'),
      sorter: true,
    },
    {
      dataIndex: 'email',
      key: 'email',
      title: setLocale('email'),
      ...getColumnSearchProps('email'),
    }
  ];

  const studentColumns = [
    {
      title: setLocale("student.name"),
      key: "name",
      dataIndex: "name",
      ...getStudentColumnSearchProps("name"),
      sorter: true,
    },
    {
      title: setLocale("grade.label"),
      dataIndex: "grade",
      key: "grade",
      ...getStudentColumnSearchProps("grade"),
    },

  ];

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, filter, sorting);
  };

  const handleStudentPageChange = (page, pageSize) => {
    getStudentModuleData(page, pageSize, filter, sorting);
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
    };

    try {
      await dispatch(updateSortFilters({ filter: filter, sorting: sorting }))
      getModuleData(1, tablePagination.pageSize, filter, sorting);
    } catch (error) {
      console.log(error);
    }
  }

  const handleStudentTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
    };

    try {
      await dispatch(updateSortFilters({ filter: filter, sorting: sorting }))
      getStudentModuleData(tableStudentPagination.current, tableStudentPagination.pageSize, filter, sorting);
    } catch (error) {
      console.log(error);
    }
  }

  const handleParticipantSelected = async () => {
    let incidentId = null;
    if (IncidentReportEditData && IncidentReportEditData.id) {
      incidentId = IncidentReportEditData.id;
    }
    const participantType = form.getFieldValue('participant_type');
    await dispatch(getParticipantData({ incidentId: incidentId, studentParticipant: selectedStudentsRowKeys, userParticipant: selectedRowKeys, participantType: participantType }));
  }



  const handleUserDelete = async (record, participantType) => {
    let incidentId = null;
    if (IncidentReportEditData && IncidentReportEditData.id) {
      incidentId = IncidentReportEditData.id;
    }

    if (participantType === 'student') {
      const updatedStudentData = selectedStudentsRowKeys?.filter(item => item !== record.student_id);
      // Dispatch delete action if needed
      if (record.id) {
        await dispatch(deleteParticipant({ incidentId: incidentId, id: record.id, participantType: participantType }));
      }
      await dispatch(updateStudentSelectedRowKeys({ selected: updatedStudentData }));
      await dispatch(getParticipantData({ incidentId: incidentId, studentParticipant: updatedStudentData, userParticipant: selectedRowKeys, participantType: participantType }));
    } else if (participantType === 'staff') {
      const updatedStaffData = selectedRowKeys?.filter(item => item !== record.user_id);
      // Dispatch delete action if needed
      if (record.id) {
        await dispatch(deleteParticipant({ incidentId: incidentId, id: record.id, participantType: participantType }));
      }
      await dispatch(updateSelectedRowKeys({ selected: updatedStaffData }));
      await dispatch(getParticipantData({ incidentId: incidentId, studentParticipant: selectedStudentsRowKeys, userParticipant: updatedStaffData, participantType: participantType }));
    } else {
      const updatedOtherData = otherParticipant.filter(item => item.others_primary_phone !== record.others_primary_phone);
      // Dispatch delete action if needed
      if (updatedOtherData.length < otherParticipant.length) {
        // If any item was removed, dispatch the updated data
        await dispatch(setOtherParticipantData({ data: updatedOtherData, throughDelete: true }));
      }
      if (record.id) {
        await dispatch(deleteParticipant({ incidentId: incidentId, id: record.id, participantType: participantType }));
      }
      // await dispatch(getParticipantData({ incidentId: incidentId, studentParticipant: selectedStudentsRowKeys, userParticipant: updatedOtherData, participantType: participantType }));
    }
  }


  const handleOtherEdit = (record, participantType) => {
    form1.setFieldsValue(record);
    setOtherParticipantId(record.id);
    setOthersOption(true);
    setStudentOption(false);
    setUserOption(false);
    setOtherUpdate(true);
  }

  const handleOtherParticipant = () => {
    const otherData = {
      id: otherParticipantId ?? null,
      others_first_name: form1.getFieldValue('others_first_name'),
      others_last_name: form1.getFieldValue('others_last_name'),
      others_address: form1.getFieldValue('others_address'),
      others_primary_phone: form1.getFieldValue('others_primary_phone'),
      others_alternate_phone: form1.getFieldValue('others_alternate_phone'),
      others_email: form1.getFieldValue('others_email'),
      participantType: 'Other'
    };

    if (otherParticipantId) {
      dispatch(saveOtherParticipant(otherData));
      if (params?.id) {
        dispatch(viewIncidentReport(params?.id))
      }
    } else {
      const isDuplicate = otherParticipant?.some(participant => participant.others_primary_phone === otherData.others_primary_phone);

      if (!isDuplicate && otherData.others_primary_phone) {
        dispatch(setOtherParticipantData({ data: otherData, throughDelete: false }));
      }
    }



  }

  const handleChangeUpperCase = (event) => {
    form1.setFieldsValue({
      others_last_name: event.target.value.toUpperCase(),
    });
  }


  return (
    <>
      <Row gutter={16}>
        <Col span={24} style={{ textAlign: "center" }}>
          <h2>
            Who is involved in the incident?
          </h2>
        </Col>
        <Skeleton active loading={loading}>
          <Col span={24}>

            <Row gutter={16}>
              <Col span={12}>
                <Row gutter={16}>
                  {studentOption && <>
                    <Col span={24}>
                      <div style={{ width: "50%", float: "left", textAlign: "left" }}>
                        <h4>{setLocale("incidentReport.student_id")}</h4>
                      </div>
                      {hasStudentSelected ?
                        <div style={{ width: "50%", float: "right", textAlign: "right" }}>
                          <Button type="primary" onClick={handleParticipantSelected} className="ant-btn-round ant-btn-sm" style={{ float: "right", margin: "5px" }}>
                            {setLocale('incidentReport.add_participant')}
                          </Button>
                        </div>
                        : null}
                      <Table
                        onChange={handleStudentTableChange}
                        columns={studentColumns}
                        loading={StudentTableLoading}
                        rowKey={record => record.id}
                        dataSource={StudentResult ?? []}
                        pagination={false}
                        rowSelection={studentRowSelection}
                      />
                      <Pagination
                        style={{ margin: '16px', float: 'right' }}
                        current={tableStudentPagination.current}
                        pageSize={tableStudentPagination.pageSize}
                        total={tableStudentPagination.total}
                        pageSizeOptions={['10', '20', '50', '100', '1000']}
                        showQuickJumper
                        onChange={handleStudentPageChange}
                      />
                    </Col>
                  </>}
                  {userOption && <>
                    <Col span={24}>
                      <div style={{ width: "50%", float: "left", textAlign: "left" }}>
                        <h4>{setLocale("incidentReport.user_id")}</h4>
                      </div>
                      {hasSelected ?
                        <div style={{ width: "50%", float: "right", textAlign: "right" }}>
                          <Button type="primary" onClick={handleParticipantSelected} className="ant-btn-round ant-btn-sm" style={{ float: "right", margin: "5px" }}>
                            {setLocale('incidentReport.add_participant')}
                          </Button>
                        </div>
                        : null}
                      <Table
                        onChange={handleTableChange}
                        columns={userColumns}
                        loading={UserTableLoading}
                        rowKey={record => record.id}
                        dataSource={UserResult ?? []}
                        pagination={false}
                        rowSelection={userRowSelection}
                      />
                      <Pagination
                        style={{ margin: '16px', float: 'right' }}
                        current={tablePagination.current}
                        pageSize={tablePagination.pageSize}
                        total={tablePagination.total}
                        showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                        pageSizeOptions={['10', '20', '50', '100', '1000']}
                        showQuickJumper
                        onChange={handlePageChange}
                      />
                    </Col>
                  </>}
                  {OthersOption && <>

                    <Row gutter={16}>
                      <Col span={24}>
                        <Form
                          layout="vertical"
                          form={form1}
                          style={{ padding: "10px" }}
                          onFinish={handleOtherParticipant}
                          onFinishFailed={onFinishFailed}
                          autoComplete="off"
                        >
                          <Row gutter={16}>
                            <Col span={24}>
                              <div style={{ float: "center", textAlign: "center" }}>
                                <h4>{setLocale("incidentReport.other")}</h4>
                              </div>
                            </Col>

                            <Col span={12}>
                              <Form.Item
                                name="others_first_name"
                                label={setLocale('incidentReport.others_first_name')}
                                rules={[
                                  {
                                    required: true,
                                    message: setLocale('incidentReport.others_first_nameError'),
                                  },
                                ]}
                              // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                              // help={IncidentReportShowMessage && IncidentReportErrors.name}
                              >
                                <Input />
                              </Form.Item>
                            </Col>

                            <Col span={12}>
                              <Form.Item
                                name="others_last_name"
                                onKeyUp={handleChangeUpperCase}
                                label={setLocale('incidentReport.others_last_name')}
                                rules={[
                                  {
                                    required: true,
                                    message: setLocale('incidentReport.others_last_nameError'),
                                  },
                                ]}
                              // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                              // help={IncidentReportShowMessage && IncidentReportErrors.name}
                              >
                                <Input />
                              </Form.Item>
                            </Col>

                            <Col span={12}>
                              <Form.Item
                                name="others_email"
                                label={setLocale('incidentReport.others_email')}
                                rules={[
                                  {
                                    required: false,
                                    type: 'email',
                                    message: setLocale('incidentReport.others_emailError'),
                                  },
                                ]}
                              // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                              // help={IncidentReportShowMessage && IncidentReportErrors.name}
                              >
                                <Input />
                              </Form.Item>
                            </Col>

                            <Col span={12}>
                              <Form.Item
                                name="others_primary_phone"
                                label={setLocale('incidentReport.others_primary_phone')}
                                rules={[
                                  {
                                    required: true,
                                    message: setLocale('incidentReport.others_primary_phoneError'),
                                  },
                                ]}
                              // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                              // help={IncidentReportShowMessage && IncidentReportErrors.name}
                              >
                                {/* <Input /> */}

                                <PhoneNumberInput value={''} />
                              </Form.Item>
                            </Col>

                            <Col span={12}>
                              <Form.Item
                                name="others_alternate_phone"
                                label={setLocale('incidentReport.others_alternate_phone')}
                                rules={[
                                  {
                                    required: false,
                                    message: setLocale('incidentReport.others_alternate_phoneError'),
                                  },
                                ]}
                              // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                              // help={IncidentReportShowMessage && IncidentReportErrors.name}
                              >
                                <Input />
                              </Form.Item>
                            </Col>

                            <Col span={12}>
                              <Form.Item
                                name="others_address"
                                label={setLocale('incidentReport.others_address')}
                                rules={[
                                  {
                                    required: false,
                                    message: setLocale('incidentReport.others_addressError'),
                                  },
                                ]}
                              // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                              // help={IncidentReportShowMessage && IncidentReportErrors.name}
                              >
                                <TextArea rows={4} />
                              </Form.Item>
                            </Col>

                            <Col span={24}>
                              <Space>
                                <Button
                                  type="primary"
                                  htmlType="submit"
                                  loading={otherParticipantButtonSpinner}
                                  className="ant-btn-round ant-btn-sm"
                                  style={{ float: "right", margin: "5px" }}
                                >
                                  {OtherUpdate ? setLocale('incidentReport.update_participant') : setLocale('incidentReport.add_participant')}
                                </Button>
                              </Space>
                            </Col>

                          </Row>
                        </Form>
                      </Col>
                    </Row>
                  </>}
                </Row>
              </Col>
              <Col span={12}>
                <Form
                  layout="vertical"
                  onFinish={handleFormSubmit}
                  form={form}
                  onFinishFailed={onFinishFailed}
                  // onValuesChange={handleFormChange}
                  autoComplete="off"
                >


                  <Row gutter={16}>
                    <Col span={4}></Col>
                    <Col span={16}>
                      <Form.Item
                        name="participant_type"
                        label={setLocale('incidentReport.participant_type')}
                        rules={[
                          {
                            required: false,
                            message: setLocale('incidentReport.participant_typeError'),
                          },
                        ]}
                      // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                      // help={IncidentReportShowMessage && IncidentReportErrors.name}
                      >
                        <Select
                          className="rounded-0"
                          showSearch
                          optionLabelProp="label"
                          allowClear
                          onChange={onChangeType}
                          optionFilterProp="children"
                          filterOption={(input, option) => (
                            (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                          )}
                          filterSort={(optionA, optionB) =>
                            (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                          }
                          options={participantType ?? []}

                        />
                      </Form.Item>
                    </Col>
                    <Col span={4}></Col>
                  </Row>
                  <div>
                    <h4>{setLocale("Selected Participants")}</h4>
                  </div>
                  <br></br>
                  <Row gutter={16}>
                    {participantData?.staff && Object.keys(participantData?.staff).length > 0 && (
                      <Divider orientation="left" plain>
                        <strong>{setLocale('incidentReport.staff')}</strong>
                      </Divider>
                    )}
                    {participantData?.staff && Object.keys(participantData?.staff).length > 0 && participantData.staff.map((item, index) => (
                      <React.Fragment key={item.user_id}>
                        <Col span={10}>
                          <div style={{ width: "50%", float: "left", textAlign: "left" }}>
                            {item.name}
                          </div>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name={"staff_participant_role_" + item.user_id}
                            initialValue={item?.participant_role ?? ''}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.participant_roleError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >
                            <Select
                              placeholder={setLocale('incidentReport.participant_role')}
                              className="rounded-0"
                              showSearch
                              optionLabelProp="label"
                              value={item?.participant_role ?? ''}
                              allowClear
                              optionFilterProp="children"
                              filterOption={(input, option) => (
                                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                              )}
                              filterSort={(optionA, optionB) =>
                                (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                              }
                              options={participantRole ?? []}

                            />
                          </Form.Item>
                        </Col>
                        <Col span={4}>
                          <div style={{ width: "50%", float: "right", textAlign: "right" }}>
                            <Popconfirm placement="topLeft" title={setLocale('sure_to_delete')} onConfirm={(e) => handleUserDelete(item, 'staff')}>
                              <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                            </Popconfirm>
                          </div>
                        </Col>
                      </React.Fragment>
                    ))}

                    {participantData?.student && Object.keys(participantData?.student).length > 0 && (
                      <>
                        <Divider orientation="left" plain>
                          <strong>{setLocale('incidentReport.students')}</strong>
                        </Divider>
                      </>
                    )}
                    {participantData?.student && Object.keys(participantData?.student).length > 0 && participantData.student.map((item, index) => (
                      <React.Fragment key={item.student_id}>
                        <Col span={10}>
                          <div style={{ float: "left", textAlign: "left" }}>
                            {item.name}
                          </div>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            initialValue={item?.participant_role ?? ''}
                            name={"student_participant_role_" + item.student_id}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.participant_roleError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >
                            <Select
                              className="rounded-0"
                              placeholder={setLocale('incidentReport.participant_role')}
                              value={item?.participant_role ?? ''}
                              // onChange={(value) => handleParticipantRoleChange(value, item.id)} // Update participant_role when value changes
                              showSearch
                              optionLabelProp="label"
                              allowClear
                              optionFilterProp="children"
                              filterOption={(input, option) => (
                                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                              )}
                              filterSort={(optionA, optionB) =>
                                (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                              }
                              options={participantRole ?? []}

                            />
                          </Form.Item>
                        </Col>
                        <Col span={4}>
                          <div style={{ float: "right", textAlign: "right" }}>
                            <Popconfirm placement="topLeft" title={setLocale('sure_to_delete')} onConfirm={(e) => handleUserDelete(item, 'student')}>
                              <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                            </Popconfirm>
                          </div>
                        </Col>
                      </React.Fragment>
                    ))}

                    {Object.keys(otherParticipant).length > 0 && (
                      <>
                        <Divider orientation="left" plain>
                          <strong>{setLocale('incidentReport.other')}</strong>
                        </Divider>
                      </>
                    )}
                    {Object.keys(otherParticipant).length > 0 && otherParticipant?.map((item, index) => (
                      <React.Fragment key={index}>
                        <Col span={10}>
                          <div style={{ float: "left", textAlign: "left" }}>
                            {item.others_first_name} {item.others_last_name}
                          </div>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name={"other_participant_role_" + index}
                            initialValue={item?.participant_role ?? ''}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.participant_roleError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >
                            <Select
                              className="rounded-0"
                              placeholder={setLocale('incidentReport.participant_role')}
                              showSearch
                              optionLabelProp="label"
                              value={item?.participant_role ?? ''}
                              allowClear
                              optionFilterProp="children"
                              filterOption={(input, option) => (
                                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                              )}
                              filterSort={(optionA, optionB) =>
                                (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                              }
                              options={participantRole ?? []}

                            />
                          </Form.Item>
                        </Col>
                        <Col span={4}>
                          <div style={{ float: "right", textAlign: "right" }}>
                            {item.id && (
                              <>
                                <EditOutlined style={{ fontSize: '15px' }} onClick={() => handleOtherEdit(item, 'other')} className="text-success" />
                              </>
                            )}

                            <Popconfirm placement="topLeft" title={setLocale('sure_to_delete')} onConfirm={(e) => handleUserDelete(item, 'other')}>
                              <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                            </Popconfirm>
                          </div>
                        </Col>
                      </React.Fragment>
                    ))}

                  </Row>
                  {
                    (participantData?.staff && Object.keys(participantData?.staff).length > 0) ||
                      (participantData?.student && Object.keys(participantData?.student).length > 0) ||
                      (Object.keys(otherParticipant).length > 0) ? (
                      <>

                        <Space>
                          <Button
                            type="primary"
                            htmlType="submit"
                            loading={ParticipantButtonSpinner}
                          >
                            {IncidentReportButtonAndModelLabel}
                          </Button>
                        </Space>
                      </>
                    ) : ''
                  }
                </Form>


              </Col>

            </Row>


          </Col>
        </Skeleton>
      </Row>
    </>
  );
};
export default IncidentParticipantForm;


import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Col, Drawer, Form, Input, Row, Select, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { INCIDENT_ACTION_CODE } from "constants/AppConstants";
import {
  IncidentActioncodeAddDrawerStatus,
  createIncidentActioncode,
  getIncidentActioncode
} from "store/slices/IncidentActionCode/manageIncidentActionCodeSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { getAttendanceCodes } from 'store/slices/AttendanceManagement/attendanceSlice';
import { ATTENDANCE_SLICE } from 'constants/attendance';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { IncidentActioncodeAddDrawer, IncidentActioncodeButtonAndModelLabel, sorting, filter, IncidentActioncodeErrors, IncidentActioncodeShowMessage, IncidentActioncodeButtonSpinner, IncidentActioncodeEditData, tablePagination } = useSelector(
    (state) => state[INCIDENT_ACTION_CODE]
  );
  const {
    attendanceCodes,
  } = useSelector(state => state[ATTENDANCE_SLICE])
  const onClose = () => dispatch(IncidentActioncodeAddDrawerStatus(false));

  const [showAttendanceCode, setShowAttendanceCode] = useState(false);

  const handleCheckboxChange = (e) => {
    setShowAttendanceCode(e.target.checked);
  };

  useEffect(() => {
    dispatch(getAttendanceCodes());
    IncidentActioncodeEditData && Object.keys(IncidentActioncodeEditData).length > 0 && IncidentActioncodeEditData.effects_attendance === 1 ? setShowAttendanceCode(true) : setShowAttendanceCode(false);
  }, [])

  console.log(attendanceCodes);
  const onSubmit = async (formValues) => {

    if (IncidentActioncodeEditData && IncidentActioncodeEditData.id) {
      // If editing, include the id in the form values
      formValues.id = IncidentActioncodeEditData.id;
    }

    await dispatch(createIncidentActioncode(formValues))
      .then(() => {
        if (Object.keys(IncidentActioncodeErrors).length === 0) {
          dispatch(getIncidentActioncode({
            page: tablePagination.current,
            perPage: tablePagination.pageSize,
            filter: filter, sorting: sorting
          }));
          // form.resetFields();
        }
      })
      .catch((error) => {
        // Handle delete error
        console.error("Error deleting module:", error);
      });
  };

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  console.log(IncidentActioncodeEditData);
  return (
    <>

      <Drawer
        title={IncidentActioncodeButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={IncidentActioncodeAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            incident_action_code: IncidentActioncodeEditData?.incident_action_code,
            effects_attendance: IncidentActioncodeEditData?.effects_attendance,
            attendance_code_id: IncidentActioncodeEditData?.attendance_code_id,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="incident_action_code"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={IncidentActioncodeShowMessage && IncidentActioncodeErrors.incident_action_code ? "error" : ""}
                help={IncidentActioncodeShowMessage && IncidentActioncodeErrors.incident_action_code}
              >
                <Input />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="effects_attendance"
                style={{ marginTop: "25px" }}
                label={''}
                valuePropName="checked"
                rules={[
                  {
                    required: false,
                    message: setLocale("calendarEvent.effects_attendance_error"),
                  },
                ]}
                validateStatus={IncidentActioncodeShowMessage && IncidentActioncodeErrors.effects_attendance ? "error" : ""}
                help={IncidentActioncodeShowMessage && IncidentActioncodeErrors.effects_attendance}
              >
                <Checkbox onChange={handleCheckboxChange} >{setLocale('incidentactioncode.effects_attendance')}</Checkbox>
              </Form.Item>
            </Col>

            {showAttendanceCode && (
              <Col span={12}>
                <Form.Item
                  name={"attendance_code_id"}
                  label={setLocale('leave_request.attendance_code')}
                  rules={[
                    {
                      required: true,
                      message: setLocale('leave_request.attendance_code'),
                    },
                  ]}
                >
                  <Select
                    className="rounded-0"
                    showSearch
                    optionLabelProp="label"
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    }
                    options={attendanceCodes ?? []}
                  />
                </Form.Item>
              </Col>
            )}

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={IncidentActioncodeButtonSpinner}
            >
              {IncidentActioncodeButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useEffect,useState } from 'react';
import moment from 'moment';
import { useDispatch,useSelector } from 'react-redux';
import { Form,DatePicker,Select, Space,Button,Col, Row,Input, Modal } from 'antd';
import { 
  addCourseGradeBookCategory,
  getClassStudentsForGrades
} from "store/slices/Grade/studentGradeBookSlice.js";
import { 
  setAddNewGradeCategoryModal,
} from "store/slices/GradeBookCategory/manageGradeBookCategorySlice.js";
import { GRADE_BOOK_CATEGORY, STUDENT_GRADE_BOOK_SLICE, MANAGE_CLASS } from "constants/AppConstants";

import IntlMessage from 'components/util-components/IntlMessage';

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { Option } = Select;


function AddNewCategory() {
  const [form] = Form.useForm()
  const dispatch = useDispatch()
 
  const {  
    addNewGradeCategoryModal,
    saveBtnLoading,
    selectedCategory,
    GradeBookCategoryResult
  } = useSelector((state) => state[GRADE_BOOK_CATEGORY])
  const { ClassCourseEditData } = useSelector((state) => state[MANAGE_CLASS]);
  const classCourse = { class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }
  useEffect(() => {
   console.log(selectedCategory, 'KK')
  }, [])
  /**open CourseGradeCategory Modal to add new Category */
  const openCategoryGradeModal = (data)=>{
    form.resetFields()
    dispatch(setAddNewGradeCategoryModal(data))
  }
  const saveCourseGradeCategory = async (values)=>{  
    values.class_id = ClassCourseEditData?.manage_class_id;
    values.course_id = ClassCourseEditData?.course_id;    
    values.due_date = moment(values.due_date).format('YYYY-MM-DD')
    values.grade_book_category_id = selectedCategory?.id
  
    await dispatch(addCourseGradeBookCategory(values))
    await dispatch(getClassStudentsForGrades(classCourse))

  }
  return (
    <>
      { addNewGradeCategoryModal ? 
        <Modal zIndex={1010} title={ selectedCategory?.name }
          centered   open={addNewGradeCategoryModal}  onCancel={()=>openCategoryGradeModal(false)}  footer={null} >
          <Space direction="vertical"  style={{ width: '100%' }} >
            <Form onFinish={ saveCourseGradeCategory } layout="vertical" form={form} autoComplete="off">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="name"
                      label={setLocale("name")}
                      rules={[
                        {
                          required: true,
                          message: setLocale('name_error'),
                        },
                      ]}>
                      <Input className='rounded-0' />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                        name="due_date"
                        label={setLocale("course_gradebook_category.due_date")}
                        rules={[
                          {
                            required: true,
                            message: setLocale('course_gradebook_category.due_date_error'),
                          },
                        ]}>
                        <DatePicker style={{display:'block'}} format={'YYYY-MM-DD'} />
                    </Form.Item>
                  </Col>               
                  {/* <Col span={12}>
                    <Form.Item
                      name="grade_book_category_id"
                      label={setLocale("course_gradebook_category.grade_book_category")}
                      rules={[
                        {
                          required: true,
                          message: setLocale('course_gradebook_category.grade_book_category_error'),
                        },
                      ]}>
                        <Select className='rounded-0' optionLabelProp="label">
                          { GradeBookCategoryResult.map((gradeBC, index) =>
                            <Option value={gradeBC.id} key={index} label={gradeBC.name}>{gradeBC.name}</Option>
                          ) }
                        </Select>
                    </Form.Item>
                  </Col> */}
                  <Col span={12}>
                    <Form.Item
                      name="final_grade_weightage"
                      label={setLocale("course_gradebook_category.final_grade_weightage")}
                      rules={[
                        {
                          required: true,
                          message: setLocale('course_gradebook_category.final_grade_weightage_error'),
                        },
                      ]}>
                      <Input className='rounded-0' />
                    </Form.Item>
                  </Col>
                
                  <Col span={12}>
                    <Form.Item
                      name="total_marks"
                      label={setLocale("course_gradebook_category.total_marks")}
                      rules={[
                        {
                          required: true,
                          message: setLocale('course_gradebook_category.total_marks_error'),
                        },
                      ]}>
                      <Input className='rounded-0' />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={8}>
                    <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                        <Form.Item >
                            <Button loading={saveBtnLoading} type="primary" htmlType="submit" className='rounded-0'>
                                {setLocale("save")}
                            </Button>
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
          </Space>
        </Modal> :null
      } 
    </>
  )
}

export default AddNewCategory

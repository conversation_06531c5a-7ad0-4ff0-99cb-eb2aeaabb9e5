import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "antd";

const ViewModal = ({ visible, onClose, viewRecord }) => {
  return (
    <Modal
      title="Leave Request Details"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
      ]}
      width={600}
    >
      {viewRecord && (
        <div>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>Employee:</strong>{" "}
              {viewRecord.user?.name || viewRecord.user?.email}
            </Col>
            <Col span={12}>
              <strong>Leave Type:</strong> {viewRecord.leave_type?.name}
            </Col>
            <Col span={12}>
              <strong>From:</strong> {viewRecord.from}
            </Col>
            <Col span={12}>
              <strong>To:</strong> {viewRecord.to}
            </Col>
            <Col span={12}>
              <strong>Status:</strong>
              <span className="text-capitalize font-weight-semibold ml-2">
                {viewRecord.status}
              </span>
            </Col>
            <Col span={12}>
              <strong>Days:</strong> {viewRecord.days || "N/A"}
            </Col>
            <Col span={24}>
              <strong>Leave Description:</strong>
              <p style={{ marginTop: 8 }}>{viewRecord.description}</p>
            </Col>
            {viewRecord.documents && viewRecord.documents !== "null" && (
              <Col span={24}>
                <strong>Document:</strong>
                <div style={{ marginTop: 8 }}>
                  <a
                    href={viewRecord.documents}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ wordBreak: "break-all" }}
                  >
                    {viewRecord.documents.split("/").pop()}
                  </a>
                </div>
              </Col>
            )}
            {viewRecord.reason && (
              <Col span={24}>
                <strong>Reason:</strong>
                <p style={{ marginTop: 8 }}>{viewRecord.reason}</p>
              </Col>
            )}
          </Row>
        </div>
      )}
    </Modal>
  );
};

export default ViewModal;
import React, { useRef, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import {
    Row, Col, Table, Card, Skeleton, Input, Space, Button
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import moment from 'moment';

import { INVOICES } from "constants/AppConstants";

function InvoicesTable() {
    const { dashboardData, dashboardDataLoading } = useSelector(
        state => state[INVOICES]
    );

    // --- GLOBAL SEARCH STATE ---
    const [globalSearch, setGlobalSearch] = useState('');

    // --- COLUMN SEARCH STATE ---
    const [searchText, setSearchText] = useState('');
    const [searchedColumn, setSearchedColumn] = useState('');
    const searchInput = useRef(null);

    // your existing getColumnSearchProps, adjusted so the onFilter logic is parameterized:
    const getColumnSearchProps = (dataIndexOrIndices, searchInput, searchText, setSearchText, setSearchedColumn) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => {
                        confirm();
                        setSearchText(selectedKeys[0]);
                        setSearchedColumn(
                            Array.isArray(dataIndexOrIndices) ? dataIndexOrIndices.join(',') : dataIndexOrIndices
                        );
                    }}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => {
                            confirm();
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(
                                Array.isArray(dataIndexOrIndices) ? dataIndexOrIndices.join(',') : dataIndexOrIndices
                            );
                        }}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters();
                            setSearchText('');
                            setSearchedColumn('');
                            confirm();
                        }}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => (
            <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined, fontSize: '16px' }} />
        ),
        onFilter: (value, record) => {
            const indices = Array.isArray(dataIndexOrIndices)
                ? dataIndexOrIndices
                : [dataIndexOrIndices];
            return indices.some(key => {
                const cell = record[key];
                return cell
                    ?.toString()
                    .toLowerCase()
                    .includes(value.toLowerCase());
            });
        },
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current?.select(), 100);
            }
        },
        render: text =>
            (Array.isArray(dataIndexOrIndices)
                ? dataIndexOrIndices.includes(searchText && searchedColumn)
                : searchedColumn === dataIndexOrIndices) ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text?.toString() || ''}
                />
            ) : (
                text
            ),
    });

    // raw array:
    const rawData = dashboardData?.monthlyInvoices?.data || [];

    // apply globalSearch across name, id, invoice_no, etc:
    const globalFilteredData = useMemo(() => {
        const txt = globalSearch.trim().toLowerCase();
        if (!txt) return rawData;
        return rawData.filter(rec =>
            rec.full_name.toLowerCase().includes(txt) ||
            rec.student_id.includes(txt) ||
            rec.invoice_no.toLowerCase().includes(txt)
        );
    }, [rawData, globalSearch]);

    const columns = [
        {
            title: 'Student',
            dataIndex: 'student_id',
            key: 'student_id',
            ...getColumnSearchProps(
                ['full_name', 'student_id'],
                searchInput,
                searchText,
                setSearchText,
                setSearchedColumn
            ),
            render: (id, r) => `${r.full_name} (${id})`,
            sorter: (a, b) => a.student_id.localeCompare(b.student_id),
        },
        {
            title: 'Grade Level',
            dataIndex: 'grade_level',
            key: 'grade_level',
            ...getColumnSearchProps(
                'grade_level',
                searchInput,
                searchText,
                setSearchText,
                setSearchedColumn
            ),
            sorter: (a, b) => +a.grade_level - +b.grade_level,
        },
        {
            title: 'Batch #',
            dataIndex: 'batch_no',
            key: 'batch_no',
            ...getColumnSearchProps(
                'batch_no',
                searchInput,
                searchText,
                setSearchText,
                setSearchedColumn
            ),
            sorter: (a, b) => a.batch_no.localeCompare(b.batch_no),
        },
        {
            title: 'Invoice #',
            dataIndex: 'invoice_no',
            key: 'invoice_no',
            ...getColumnSearchProps(
                'invoice_no',
                searchInput,
                searchText,
                setSearchText,
                setSearchedColumn
            ),
            sorter: (a, b) => a.invoice_no.localeCompare(b.invoice_no),
        },
        {
            title: 'Due Date',
            dataIndex: 'due_date',
            key: 'due_date',
            ...getColumnSearchProps(
                'due_date',
                searchInput,
                searchText,
                setSearchText,
                setSearchedColumn
            ),
            sorter: (a, b) => new Date(a.due_date) - new Date(b.due_date),
            render: d => moment(d).format('YYYY-MM-DD'),
        },
        {
            title: 'Status',
            dataIndex: 'invoice_status_code',
            key: 'invoice_status_code',
            filters: [
                { text: 'Paid', value: 'Paid' },
                { text: 'Pending', value: 'Pending' },
                { text: 'Partially Paid', value: 'Partially Paid' },
            ],
            onFilter: (value, record) =>
                record.invoice_status_code === value,
            sorter: (a, b) =>
                a.invoice_status_code.localeCompare(b.invoice_status_code),
        },
    ];

    return (
        <Row gutter={16}>
            <Col span={24}>
                <Card className="p-3">
                    {/* --- Global Search Box --- */}
                    {/* <Space style={{ marginBottom: 16 }}>
                        <Input.Search
                            placeholder="Global search (name, ID, invoice#…)​"
                            allowClear
                            onSearch={value => setGlobalSearch(value)}
                            style={{ width: 300 }}
                        />
                    </Space> */}

                    {dashboardDataLoading ? (
                        <Skeleton active paragraph={{ rows: 4 }} />
                    ) : (
                        <Table
                            rowKey="invoice_no"
                            dataSource={globalFilteredData}
                            columns={columns}
                            pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                pageSizeOptions: ['5', '10', '20', '50'],
                            }}
                        />
                    )}
                </Card>
            </Col>
        </Row>
    );
}

export default InvoicesTable;

import React from "react";
import { Modal, Form, Input, Button, Space } from "antd";
import { useSelector } from "react-redux";
import { EMPLOYEE_LEAVE } from "constants/AppConstants";

const ReasonModal = ({
  visible,
  onCancel,
  onSubmit,
  currentAction,
  form,
}) => {
  const { EmployeeLeaveTableLoading } = useSelector(
    (state) => state[EMPLOYEE_LEAVE]
  );

  return (
    <Modal
      title={`${
        currentAction === "rejected" ? "Reject" : "Cancel"
      } Leave Request`}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={500}
    >
      <Form form={form} layout="vertical" onFinish={onSubmit}>
        <Form.Item
          label="Reason"
          name="reason"
          rules={[
            {
              required: true,
              message: `Please enter reason for ${
                currentAction === "rejected" ? "rejection" : "cancellation"
              }`,
            },
          ]}
        >
          <Input.TextArea
            rows={4}
            placeholder={`Enter reason for ${
              currentAction === "rejected" ? "rejecting" : "cancelling"
            } this leave request...`}
          />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              disabled={EmployeeLeaveTableLoading}
              loading={EmployeeLeaveTableLoading}
            >
              Submit
            </Button>
            <Button onClick={onCancel}>Cancel</Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReasonModal;
import React, { useState, useEffect } from "react";
import {
  Table,
  Avatar,
  Badge,
  Tooltip,
  Dropdown,
  Menu,
  Input,
  Skeleton,
} from "antd";
import {
  StarOutlined,
  StarFilled,
  DeleteOutlined,
  TagOutlined,
  FileTextTwoTone,
} from "@ant-design/icons";
import MailData from "assets/data/mail.data.json";
import { labels, getLabelColor } from "./MailLabels";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { COMMUNICATION } from "constants/AppConstants";
import { getCommunicationConversation } from "store/slices/Communication/manageCommunicationSlice";
import IntlMessage from "components/util-components/IntlMessage";

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const ConversationItem = () => {
  const dispatch = useDispatch();
  const params = useParams();
  const navigate = useNavigate();

  const [mails, setMails] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pageLoading, setPageLoading] = useState(true);

  const { getCommunicationConversationResult } = useSelector(
    (state) => state[COMMUNICATION]
  );

  const onSelectChange = (keys) => {
    setSelectedRowKeys(keys);
  };

  const onStarTicked = (elm) => {
    let { id, starred } = elm;

    setMails((prevMails) =>
      prevMails.map((item) => {
        if (item.id === id) {
          // Create a new object with the updated 'starred' property
          return { ...item, starred: !starred };
        }
        return item;
      })
    );
  };

  const formatBody = (body) => {
    return body.replace(/<(?:.|\n)*?>/gm, " ");
  };

  const loadMail = () => {
    const data = getCurrentCategory();

    setMails(data);
    setSelectedRowKeys([]);
  };

  useEffect(() => {
    fetchData();
  }, [params, dispatch, pageLoading]);

  const fetchData = async () => {
    try {
      await dispatch(getCommunicationConversation()).then((resp) => {
        loadMail();
        setPageLoading(false);
      });
      console.log(getCommunicationConversationResult);
    } catch (error) {
      console.error("Error fetching school year data:", error);
    }
  };

  const massDeleted = (selectedKey) => {
    let data = mails;
    selectedKey.forEach((num) => {
      data = data.filter((elm) => elm.id !== num);
    });
    setMails(data);
    setSelectedRowKeys([]);
  };

  const massStar = (selectedKey) => {
    let data = mails;
    selectedKey.forEach((num) => {
      data = data.map((elm) => {
        if (elm.id === num) {
          elm.starred = true;
          return elm;
        } else return elm;
      });
    });
    setMails(data);
    setSelectedRowKeys([]);
  };

  const massCategorize = (label, selectedKey) => {
    let data = mails;
    selectedKey.forEach((num) => {
      data = data.map((elm) => {
        if (elm.id === num) {
          elm.label = label;
          return elm;
        } else return elm;
      });
    });
    setMails(data);
    setSelectedRowKeys([]);
  };

  const onSelectEmail = (elm) => {
    return {
      onClick: (e) => {
        e.preventDefault();
        navigate(`/app/communication/${elm.id}`);
      },
    };
  };

  const search = (e) => {
    let query = e.target.value;
    let data = [];
    data = getCurrentCategory().filter((item) => {
      return query === "" ? item : item.name.toLowerCase().includes(query);
    });
    setMails(data);
  };

  const getCurrentCategory = () => {
    // alert(pageLoading)
    const { category } = params;
    if (labels.includes(category)) {
      return getCommunicationConversationResult.inbox.filter(
        (elm) => elm.label === category
      );
    }
    switch (category) {
      case "sent":
        return getCommunicationConversationResult.sent;
      case "unsent":
        return getCommunicationConversationResult.unsent;
      default:
        break;
    }
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const locale = {
    emptyText: (
      <div className="text-center my-5">
        <img src="/img/others/img-10.png" alt="Add credit card" />
        <h3 className="mt-3 font-weight-light">There is no mail!</h3>
      </div>
    ),
  };

  const tableColumns = [
    {
      title: () => (
        <div className="mail-list-action">
          <div>
            {hasSelected ? (
              <div>
                <Dropdown
                  overlay={
                    <Menu>
                      {labels.map((elm) => (
                        <Menu.Item
                          key={`key-${elm}`}
                          onClick={() => {
                            massCategorize(elm, selectedRowKeys);
                          }}
                        >
                          <Badge color={getLabelColor(elm)} />
                          <span className="text-capitalize">{elm}</span>
                        </Menu.Item>
                      ))}
                    </Menu>
                  }
                >
                  <span
                    className="mail-list-action-icon ml-0"
                    onClick={(e) => e.preventDefault()}
                  >
                    <TagOutlined />
                  </span>
                </Dropdown>
                <span
                  className="mail-list-action-icon"
                  onClick={() => {
                    massDeleted(selectedRowKeys);
                  }}
                >
                  <Tooltip title="Delete">
                    <DeleteOutlined />
                  </Tooltip>
                </span>
                <span
                  className="mail-list-action-icon"
                  onClick={() => {
                    massStar(selectedRowKeys);
                  }}
                >
                  <Tooltip title="Star">
                    <StarOutlined />
                  </Tooltip>
                </span>
              </div>
            ) : null}
          </div>
          <div>
            <Input
              size="small"
              placeholder="Search"
              onChange={(e) => {
                search(e);
              }}
            />
          </div>
        </div>
      ),
      colSpan: 4,
      dataIndex: "name",
      className: "mail-list-sender",
      render: (_, elm) => (
        <div className="d-flex align-items-center">
          <div
            onClick={(e) => {
              e.stopPropagation();
              onStarTicked(elm);
            }}
            className={`mail-list-star font-size-md ${elm.starred ? "checked" : "uncheck"
              }`}
          >
            {/* {elm.starred ? <StarFilled /> : <StarOutlined />} */}
          </div>
          <div className="d-flex align-items-center">
            {elm.avatar ? (
              <Avatar
                src={null}
                size={30}
                style={{ backgroundColor: "#fde3cf", color: "#f56a00" }}
              >
                {elm.avatar}
              </Avatar>
            ) : (
              <Avatar
                size={30}
                style={{ backgroundColor: "#fde3cf", color: "#f56a00" }}
              >
                0
              </Avatar>
            )}
            <div>
              <div className="d-flex justify-content-center align-items-center">
                <p
                  className="mb-0 ml-2"
                  style={{
                    color: "#464E5F",
                    fontSize: "14px",
                    fontWeight: "400",
                    marginRight: "10px",
                  }}
                >
                  {elm.name}
                </p>
                <FileTextTwoTone />
              </div>
              <p
                className="mb-0 ml-2"
                style={{
                  color: "#3C3C3C",
                  fontSize: "12px",
                  fontWeight: "700",
                }}
              >
                subject: {elm.subject}
              </p>
              <p
                className="mb-0 ml-2"
                style={{
                  color: "#464E5F",
                  fontSize: "14px",
                  fontWeight: "400",
                }}
                dangerouslySetInnerHTML={{ __html: elm.content }}
              ></p>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "",
      colSpan: 0,
      className: "mail-list-date",
      render: (_, elm) => <div>{elm.date}</div>,
    },
  ];

  const hasSelected = selectedRowKeys.length > 0;

  return (
    <Skeleton active loading={pageLoading}>
      <div className="mail-list">
        <Table
          // rowSelection={rowSelection}
          columns={tableColumns}
          dataSource={mails}
          locale={locale}
          onRow={(elm) => {
            return {
              onClick: (e) => {
                e.preventDefault();
                navigate(
                  `/app/communication/conversation/${params.category}/${elm.id}`
                );
              },
            };
          }}
          rowKey="id"
        />
      </div>
    </Skeleton>
  );
};

export default ConversationItem;

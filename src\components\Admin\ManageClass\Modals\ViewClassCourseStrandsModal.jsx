import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Switch,
  Table,
  Popconfirm,
  Pagination,
  Button,
  Input,
  Space,
  Breadcrumb,
  Skeleton,
  Col,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { MANAGE_CLASS, STRANDS } from "constants/AppConstants";
import {
  getClassCourseStrands,
  updateStrandsSortFilters,
  setClassCourseStrandsColumnSearch,
  updateCourseStrandsSort,
  deleteClassCourseStrands,
  updateClassCourseStrands,
  setClassCourseStrands,
} from "store/slices/ManageClass/manageManageClassSlice";
import { DndContext } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const Row = ({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props["data-row-key"],
  });

  const style = {
    ...props.style,
    transform: CSS.Transform.toString(
      transform && {
        ...transform,
        scaleY: 1,
      }
    ),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 9999,
        }
      : {}),
  };

  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if (child.key === "sort") {
          return React.cloneElement(child, {
            children: (
              <MenuOutlined
                ref={setActivatorNodeRef}
                style={{
                  touchAction: "none",
                  cursor: "move",
                }}
                {...listeners}
              />
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

function ViewClassCourseStrandsModal() {
  const dispatch = useDispatch();
  const searchInput = useRef(null);
  const {
    classCourseStrands,
    classCourseStrandsIds,
    ClassCourseEditData,
    strandsTablePagination,
    StrandsSorting,
    strandsFilters,
    ClassCourseStrandsTableLoading,
    permission,
  } = useSelector((state) => state[MANAGE_CLASS]);

  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getClassCourseStrands({
        class_course_id: ClassCourseEditData?.id,
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
      })
    ).then((result) => {
      const strandsId = result.payload.data.map((item) => item.strand_id);
      dispatch(setClassCourseStrands(strandsId));
    });
  };

  const handleSearch = async (confirm) => {
    confirm();
    try {
      await getModuleData(
        1,
        strandsTablePagination.pageSize,
        strandsFilters,
        StrandsSorting
      );
    } catch (error) {
      console.error("Error in search:", error);
    }
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = strandsFilters;
    await dispatch(setClassCourseStrandsColumnSearch(newObject));
    try {
      await getModuleData(
        strandsTablePagination.current,
        strandsTablePagination.pageSize,
        newObject,
        StrandsSorting
      );
    } catch (error) {
      console.error("Error resetting filters:", error);
    }
  };

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(
      setClassCourseStrandsColumnSearch({
        ...strandsFilters,
        [dataIndex]: value,
      })
    );
    if (value === "") {
      confirm();
      getModuleData(
        strandsTablePagination.current,
        strandsTablePagination.pageSize,
        { ...strandsFilters, [dataIndex]: value },
        StrandsSorting
      );
    }
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={
            strandsFilters[dataIndex]
              ? strandsFilters[dataIndex]
              : selectedKeys[0]
          }
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            strandsFilters[dataIndex] && strandsFilters[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      strandsFilters[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[strandsFilters[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  useEffect(() => {
    getModuleData(
      strandsTablePagination.current,
      strandsTablePagination.pageSize,
      strandsFilters,
      StrandsSorting
    );
  }, []);

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, strandsFilters, StrandsSorting);
  };

  const handleDelete = (record) => {
    dispatch(deleteClassCourseStrands({ id: record.id })).then(() => {
      getModuleData(
        strandsTablePagination.current,
        strandsTablePagination.pageSize,
        strandsFilters,
        StrandsSorting
      );
    });
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
    };

    try {
      await dispatch(
        updateStrandsSortFilters({ filter: strandsFilters, sorting: sorting })
      );
      getModuleData(
        1,
        strandsTablePagination.pageSize,
        strandsFilters,
        sorting
      );
    } catch (error) {
      console.log(error);
    }
  };

  const onDragEnd = ({ active, over }) => {
    if (active.id !== over?.id) {
      dispatch(updateCourseStrandsSort({ from: active.id, to: over?.id })).then(
        () => {
          getModuleData(
            strandsTablePagination.current,
            strandsTablePagination.pageSize,
            strandsFilters,
            StrandsSorting
          );
        }
      );
      // setDataSource((previous) => {
      //     const activeIndex = previous.findIndex((i) => i.key === active.id);
      //     const overIndex = previous.findIndex((i) => i.key === over?.id);
      //     return arrayMove(previous, activeIndex, overIndex);
      // });
    }
  };
  const getSwitchValue = (checked, record) => {
    dispatch(
      updateClassCourseStrands({ id: record.id, strand_value: checked ? 1 : 0 })
    );
  };
  const columns = [
    {
      title: setLocale("sr_no"),
      dataIndex: "key",
      key: "sort",
    },
    {
      title: setLocale("name"),
      dataIndex: "name",
      key: "name",
      sorter: true,
      ...getColumnSearchProps("name"),
    },
    {
      title: setLocale("Default Checked"),
      key: "default_checked",
      render: (data, record) => (
        <>
          <Switch
            checkedChildren="Yes"
            unCheckedChildren="No"
            defaultChecked={record.strand_value === 1 ? true : false}
            onChange={(checked) => getSwitchValue(checked, record)}
          />
        </>
      ),
    },
    {
      title: setLocale("operation"),
      key: "action",
      render: (data, record) => (
        <>
          {permission.includes("Delete") && (
            <Popconfirm
              title={setLocale("sure_to_delete")}
              onConfirm={(e) => handleDelete(record)}
            >
              <DeleteOutlined
                style={{ fontSize: "15px" }}
                className="text-danger"
              />{" "}
              &nbsp;
            </Popconfirm>
          )}
        </>
      ),
    },
  ];

  return (
    <>
      <div>
        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            // rowKey array
            items={classCourseStrands?.data?.map((i) => i.key) ?? []}
            strategy={verticalListSortingStrategy}
          >
            <Table
              onChange={handleTableChange}
              columns={columns}
              components={{
                body: {
                  row: Row,
                },
              }}
              loading={ClassCourseStrandsTableLoading}
              rowKey={(record) => record.id}
              dataSource={classCourseStrands?.data ?? []}
              pagination={false}
            />
            <Pagination
              style={{ margin: "16px", float: "right" }}
              current={strandsTablePagination.current}
              pageSize={strandsTablePagination.pageSize}
              total={strandsTablePagination.total}
              showTotal={(total, range) =>
                `${range[0]}-${range[1]} of ${total} Records`
              }
              pageSizeOptions={["10", "20", "50", "100", "1000"]}
              showQuickJumper
              onChange={handlePageChange}
            />
          </SortableContext>
        </DndContext>
      </div>
    </>
  );
}

export default ViewClassCourseStrandsModal;

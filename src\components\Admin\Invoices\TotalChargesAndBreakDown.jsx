import React from "react";
import { useSelector } from "react-redux";
import { Row, Col, Card, Skeleton } from 'antd';
import Chart from "react-apexcharts";
import IntlMessage from "components/util-components/IntlMessage";
import { INVOICES } from "constants/AppConstants";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function TotalChargesAndBreakDown() {
    const { dashboardData, dashboardDataLoading } = useSelector((state) => state[INVOICES]);

    const barStateForTimeFrame = {
        series: dashboardData?.detailChargeTypeInvoices?.invoice_details_count ?? [],
        options: {
            colors: ["#FFC72A", "#76CBF4", "#925EF8", "#925EA8", "#997AF8", "#425ED8", "#105AF7"],
            labels: dashboardData?.detailChargeTypeInvoices?.charge_type ?? [],
            legend: {
                show: false,
                position: 'right',
                show: false,
                offsetY: 10
            },
            responsive: [{
                breakpoint: 0, // Change to 0 for the default settings
                options: {
                    chart: {
                        width: '100%', // Set width to 100% for responsiveness
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        }
    };

    const state5 = {
        series: [{
            name: 'Receivable Amount',
            data: dashboardData?.invoicesRevenueSummary?.invoice_amount ?? []
        }, {
            name: 'Received Amount',
            data: dashboardData?.invoicesRevenueSummary?.payment_amount ?? []
        }],
        options: {
            chart: {
                stacked: false,
                toolbar: { show: false },
                zoom: { enabled: false },
            },
            colors: ["#FCA400", "#38CB89"],
            plotOptions: {
                bar: { horizontal: false },
            },
            xaxis: {
                categories: dashboardData?.invoicesRevenueSummary?.categories ?? [],
            },
            yaxis: {
                // either use the built-in decimal setting:
                decimalsInFloat: 1,

                // …or explicitly format each label:
                labels: {
                    formatter: val => val.toFixed(1)
                }
            },
            legend: {
                position: 'top',
                offsetY: 0
            },
            fill: { opacity: 1 },
            tooltip: {
                y: {
                    formatter: val => `$${val.toFixed(1)}`
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    legend: {
                        position: 'bottom',
                        offsetX: -20,
                        offsetY: 0
                    }
                }
            }]
        }
    };

    return (
        <>
            <Row gutter={16}>
                <Col xs={24} sm={24} md={15} lg={15} xl={15}>
                    <Card className="pl-2 pt-2">
                        <div className="d-flex justify-content-between">
                            <h4>{setLocale('invoices.invoices_revenue_summary')}</h4>
                        </div>
                        {dashboardDataLoading ? (
                            <Skeleton loading={true} shape="round" active size="small" />
                        ) : (
                            <Chart
                                options={state5?.options}
                                series={state5?.series}
                                height={250}
                                type="bar"
                            />
                        )}
                    </Card>
                </Col>

                <Col xs={24} sm={24} md={9} lg={9} xl={9}>
                    <Card className="p-3 invoice-card-2">
                        <div className="d-flex justify-content-center">
                            <h3>Charges BreakDown (%)</h3>
                        </div>
                        {dashboardDataLoading ? (
                            <Skeleton loading={true} shape="round" active size="small" />
                        ) : (
                            <>
                                <div className="d-flex justify-content-center">
                                    <h5>{dashboardData?.start_date ?? ''} - {dashboardData?.end_date ?? ''}</h5>
                                </div>
                                <div className="mt-1">
                                    <Chart
                                        options={barStateForTimeFrame.options}
                                        series={barStateForTimeFrame.series}
                                        height={240}
                                        type="pie"
                                    />
                                </div>
                            </>
                        )}
                    </Card>
                </Col>

            </Row>
        </>
    );
}

export default TotalChargesAndBreakDown;

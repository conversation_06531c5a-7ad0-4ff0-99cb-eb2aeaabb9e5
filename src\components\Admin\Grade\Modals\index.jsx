import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, InputNumber } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { GRADE } from "constants/AppConstants";
import {
  GradeAddDrawerStatus,
  createGrade,
  getGrade,
  onCloseError
} from "store/slices/Grade/manageGradeSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { DrawerStatus, GradeResult, GradeAddDrawer, GradeButtonAndModelLabel, GradeErrors, GradeShowMessage, GradeButtonSpinner, GradeEditData, tablePagination, sorting, filter } = useSelector(
    (state) => state[GRADE]
  );
  const onClose = () => {
    dispatch(GradeAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const minValue = Object.keys(GradeResult).length > 0 ? GradeResult.data[0].percent_to + 2 : 1;
  const onSubmit = async (formValues) => {

    if (GradeEditData && GradeEditData.id) {
      // If editing, include the id in the form values
      formValues.id = GradeEditData.id;
    }

    await dispatch(createGrade(formValues))
  };
  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(GradeEditData);
    if (Object.keys(GradeErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getGrade({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [GradeErrors]);
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>

      <Drawer title={GradeButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={GradeAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          // initialValues={{
          //   ...(Object.keys(GradeEditData).length > 0 ? { ...GradeEditData } : { percent_from: (GradeResult.data.length) ? (GradeResult.data[0].percent_to + 1) : 0 }),
          // }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={GradeShowMessage && GradeErrors.name ? "error" : ""}
                help={GradeShowMessage && GradeErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="percent_from"
                label={setLocale("grade.percent_from")}
                rules={[
                  {
                    required: true,
                    type: 'number',
                    // min: 0,
                    // max: 100,
                    number: {
                      range: '${label} must be between ${min} and ${max}',
                    },
                  },
                ]}

                validateStatus={GradeShowMessage && GradeErrors.percent_from ? "error" : ""}
                extra={GradeShowMessage && GradeErrors.percent_from}
              >
                <InputNumber disabled={true} readOnly={true} className='rounded-0' style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="percent_to"
                label={setLocale("grade.percent_to")}
                rules={[
                  {
                    required: true,
                    message: setLocale('grade.percent_to_error'),
                    type: 'number',
                    // min: minValue,
                    // max: 100,
                    // number: {
                    //   range: '${label} must be between ${min} and ${max}',
                    // },
                  },

                ]}
                validateStatus={GradeShowMessage && GradeErrors.percent_to ? "error" : ""}
                extra={GradeShowMessage && GradeErrors.percent_to}
              >
                <InputNumber className='rounded-0' style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="color" label={setLocale("Color")}
                rules={[
                  {
                    required: true,
                    message: setLocale('color is required'),
                    type: 'color'                    
                  },

                ]}
                validateStatus={GradeShowMessage && GradeErrors.color ? "error" : ""}
                extra={GradeShowMessage && GradeErrors.color}
              >
                <Input type='color' className='rounded-0' style={{ width: '100%' }} />
              </Form.Item>
            </Col>


          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={GradeButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer >
    </>
  );
};
export default Index;


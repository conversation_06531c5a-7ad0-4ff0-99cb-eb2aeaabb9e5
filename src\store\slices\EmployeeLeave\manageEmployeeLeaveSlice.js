import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  EMPLOYEE_LEAVE,
  EMPLOYEE_LEAVE_API_URL,
  GET_ALL_EMPLOYEE_LEAVE_API_URL,
  UPDATE_EMPLOYEE_LEAVE_API_URL,
  EMPLOYEE_LEAVE_BALANCE_API_URL,
  UPCOMING_LEAVES_API_URL,
  GET_EMPLOYEE_LEAVE_API_URL,
  EMPLOYEE_LEAVE_EXPORT_EXCEL_API_URL,
} from "constants/AppConstants";
import { USER_ALL_INFORMATION_ORGANIZATION_WISE } from "constants/AuthConstant";
import CommonService from "services/CommonService/CommonService";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const permission = JSON.parse(
  localStorage.getItem(USER_ALL_INFORMATION_ORGANIZATION_WISE)
);

export const initialState = {
  EmployeeLeaveErrors: {},
  permission: permission?.employee_leave ?? [],
  EmployeeLeaveShowMessage: null,
  EmployeeLeaveResult: [],
  EmployeeLeaveButtonSpinner: false,
  EmployeeLeaveTableLoading: true,
  EmployeeLeaveAddDrawer: false,
  EmployeeLeaveEditData: [],
  EmployeeLeaveButtonAndModelLabel: setLocale("employeeleave.add"),
  tablePagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  sorting: {},
  filter: {},
  ViewEmployeeLeaveData: [],
  ViewEmployeeLeaveLoader: true,
  DrawerStatus: 0,
  EmployeeLeaveBalance: null,
  EmployeeLeaveBalanceLoading: false,
  UpcomingLeaves: [],
  UpcomingLeavesLoading: false,
  EmployeeLeaveExportLoading: false,
  EmployeeLeaveExportError: null,
};

export const createEmployeeLeave = createAsyncThunk(
  "createEmployeeLeave",
  async (data,{ dispatch }) => {
    try {
      const response = await CommonService.postData(
        data,
        data.get("id") ? UPDATE_EMPLOYEE_LEAVE_API_URL : EMPLOYEE_LEAVE_API_URL
      );
      if( response && response.data) {
        // Reduce leave balance after successful application
        dispatch(
          reduceLeaveBalance({
            leave_type_id: response?.data.leave_type_id,
            count: response.data?.days || 0,
          })
        );
        return response.data;
      }
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors)); // Throw an error with the server response errors
    }
  }
);
export const applyLeave = createAsyncThunk(
  "leaves/applyLeave",
  async (formData, { dispatch, rejectWithValue }) => {
    try {
      const response = await CommonService.postData(
        formData,
        EMPLOYEE_LEAVE_API_URL
      );
      if (response && response.data) {
        // Reduce leave balance after successful application
        dispatch(
          reduceLeaveBalance({
            leave_type_id: formData.leave_type_id,
            count: response.data?.days || 0,
          })
        );
        return response.data;
      } else {
        return rejectWithValue(response?.message || "Failed to apply leave");
      }
    } catch (err) {
      return rejectWithValue(err.message || "Error applying leave");
    }
  }
);
export const getEmployeeLeave = createAsyncThunk(
  "getEmployeeLeave",
  async (data) => {
    try {
      const apiUrl = data.apiUrl || GET_EMPLOYEE_LEAVE_API_URL;
      const response = await CommonService.getAllPost(data, apiUrl);
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors));
    }
  }
);

export const viewEmployeeLeave = createAsyncThunk(
  "viewEmployeeLeave",
  async (id) => {
    try {
      const response = await CommonService.showOne(id, EMPLOYEE_LEAVE_API_URL);
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors)); // Throw an error with the server response errors
    }
  }
);

export const deleteEmployeeLeave = createAsyncThunk(
  "deleteEmployeeLeave",
  async (data) => {
    try {
      const response = await CommonService.deleteOne(
        data,
        EMPLOYEE_LEAVE_API_URL
      );
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors)); // Throw an error with the server response errors
    }
  }
);

// Add the thunk
export const getEmployeeLeaveBalance = createAsyncThunk(
  "getEmployeeLeaveBalance",
  async (data) => {
    try {
      const response = await CommonService.getAll(
        EMPLOYEE_LEAVE_BALANCE_API_URL,
        data
      );
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors));
    }
  }
);

// Add the thunk for upcoming leaves
export const getUpcomingLeaves = createAsyncThunk(
  "getUpcomingLeaves",
  async (data) => {
    try {
      const response = await CommonService.getAll(
        UPCOMING_LEAVES_API_URL,
        data
      );
      return response;
    } catch (err) {
      throw new Error(JSON.stringify(err.response.data.errors));
    }
  }
);

// Add this new async thunk after your existing thunks
export const exportEmployeeLeaveExcel = createAsyncThunk(
  "exportEmployeeLeaveExcel",
  async (data, { rejectWithValue }) => {
    try {
      const response = await CommonService.postData(
        data,
        EMPLOYEE_LEAVE_EXPORT_EXCEL_API_URL
      );
      return response;
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.errors || "Failed to export Excel"
      );
    }
  }
);

export const manageEmployeeLeaveSlice = createSlice({
  name: EMPLOYEE_LEAVE,
  initialState,
  reducers: {
    onCloseError: (state, action) => {
      state.EmployeeLeaveErrors = {};
    },
    EmployeeLeaveAddDrawerStatus: (state, action) => {
      if (action.payload.status === false) {
        state.EmployeeLeaveButtonSpinner = false;
      }
      state.EmployeeLeaveAddDrawer = action.payload.status;
      state.DrawerStatus = action.payload.errorStatus;
      state.EmployeeLeaveEditData = [];
      state.EmployeeLeaveButtonAndModelLabel = setLocale("employeeleave.add");
    },
    EmployeeLeaveEditWithDrawerStatus: (state, action) => {
      state.EmployeeLeaveAddDrawer = true;
      state.DrawerStatus = action.payload.errorStatus;
      state.EmployeeLeaveEditData = action.payload.data;
      state.EmployeeLeaveButtonAndModelLabel = setLocale("employeeleave.edit");
    },
    updateSortFilters: (state, action) => {
      state.filter = action.payload.filter;
      state.sorting = action.payload.sorting;
    },
    setColumnSearch: (state, action) => {
      state.filter = action.payload;
    },
    reduceLeaveBalance: (state, action) => {
      const { leave_type_id, count } = action.payload;
      // Ensure EmployeeLeaveBalance is always an object with data array
      if (
        state.EmployeeLeaveBalance &&
        Array.isArray(state.EmployeeLeaveBalance.data)
      ) {
        const leave = state.EmployeeLeaveBalance.data.find(
          (l) => l.leave_type_id === leave_type_id
        );
        if (leave) {
          leave.balance = Math.max(leave.balance - count, 0);
        }
      }
    },
    clearExportError: (state) => {
      state.EmployeeLeaveExportError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createEmployeeLeave.pending, (state, action) => {
        state.DrawerStatus = 0;
        state.EmployeeLeaveButtonSpinner = true;
      })
      .addCase(createEmployeeLeave.fulfilled, (state, action) => {
        state.DrawerStatus = 0;
        state.EmployeeLeaveButtonSpinner = false;
        state.EmployeeLeaveErrors = {};

        // Update the leave in EmployeeLeaveResult if it's an update
        if (action.payload && action.payload.id) {
          const updatedId = action.payload.id;
          if (Array.isArray(state.EmployeeLeaveResult?.data)) {
            const idx = state.EmployeeLeaveResult.data.findIndex(
              (leave) => leave.id === updatedId
            );
            if (idx !== -1) {
              state.EmployeeLeaveResult.data[idx] = {
                ...state.EmployeeLeaveResult.data[idx],
                ...action.payload,
              };
            }
          }
        }
      })
      .addCase(createEmployeeLeave.rejected, (state, action) => {
        state.DrawerStatus = 1;
        state.EmployeeLeaveShowMessage = true;
        state.EmployeeLeaveButtonSpinner = false;
        state.EmployeeLeaveErrors = JSON.parse(action.error.message); // Parse the error messages and store them in the state
      })
      .addCase(getEmployeeLeave.pending, (state, action) => {
        state.EmployeeLeaveButtonSpinner = true;
        state.EmployeeLeaveTableLoading = true;
      })
      .addCase(getEmployeeLeave.fulfilled, (state, action) => {
        state.EmployeeLeaveButtonSpinner = false;
        state.EmployeeLeaveTableLoading = false;
        state.EmployeeLeaveResult = action.payload;
        state.tablePagination = {
          ...state.tablePagination,
          total: action.payload.pagination.total,
          current: action.payload.pagination.current_page,
          pageSize: action.payload.pagination.per_page,
        };
      })
      .addCase(getEmployeeLeave.rejected, (state, action) => {
        state.EmployeeLeaveShowMessage = true; // Set the showMessage flag to display the errors
        state.EmployeeLeaveButtonSpinner = false;
        state.EmployeeLeaveTableLoading = false;
        state.EmployeeLeaveErrors = JSON.parse(action.error.message); // Parse the error messages and store them in the state
      })
      .addCase(viewEmployeeLeave.pending, (state, action) => {
        state.ViewEmployeeLeaveLoader = true;
        state.ViewEmployeeLeaveData = [];
      })
      .addCase(viewEmployeeLeave.fulfilled, (state, action) => {
        state.ViewEmployeeLeaveLoader = false;
        state.ViewEmployeeLeaveData = action.payload;
      })
      .addCase(viewEmployeeLeave.rejected, (state, action) => {
        state.ViewEmployeeLeaveLoader = false;
        state.ViewEmployeeLeaveData = [];
      })
      .addCase(deleteEmployeeLeave.pending, (state, action) => {
        state.EmployeeLeaveTableLoading = true;
      })
      .addCase(deleteEmployeeLeave.fulfilled, (state, action) => {
        state.EmployeeLeaveTableLoading = false;
      })
      .addCase(deleteEmployeeLeave.rejected, (state, action) => {
        state.EmployeeLeaveTableLoading = false;
      })
      .addCase(getEmployeeLeaveBalance.pending, (state) => {
        state.EmployeeLeaveBalanceLoading = true;
      })
      .addCase(getEmployeeLeaveBalance.fulfilled, (state, action) => {
        state.EmployeeLeaveBalanceLoading = false;
        state.EmployeeLeaveBalance = action.payload;
      })
      .addCase(getEmployeeLeaveBalance.rejected, (state, action) => {
        state.EmployeeLeaveBalanceLoading = false;
        state.EmployeeLeaveBalance = null;
        state.EmployeeLeaveErrors = JSON.parse(action.error.message);
      })
      // Upcoming Leaves
      .addCase(getUpcomingLeaves.pending, (state) => {
        state.UpcomingLeavesLoading = true;
      })
      .addCase(getUpcomingLeaves.fulfilled, (state, action) => {
        state.UpcomingLeavesLoading = false;
        state.UpcomingLeaves = action.payload;
      })
      .addCase(getUpcomingLeaves.rejected, (state, action) => {
        state.UpcomingLeavesLoading = false;
        state.UpcomingLeaves = [];
        state.EmployeeLeaveErrors = JSON.parse(action.error.message);
      })
      .addCase(applyLeave.pending, (state) => {
        state.EmployeeLeaveButtonSpinner = true;
      })
      .addCase(applyLeave.fulfilled, (state, action) => {
        state.EmployeeLeaveButtonSpinner = false;
        state.EmployeeLeaveShowMessage = true;
      })
      .addCase(applyLeave.rejected, (state, action) => {
        state.EmployeeLeaveButtonSpinner = false;
        state.EmployeeLeaveShowMessage = true;
        state.EmployeeLeaveErrors = action.payload || {};
      })
      .addCase(exportEmployeeLeaveExcel.pending, (state) => {
        state.EmployeeLeaveExportLoading = true;
        state.EmployeeLeaveExportError = null;
      })
      .addCase(exportEmployeeLeaveExcel.fulfilled, (state, action) => {
        state.EmployeeLeaveExportLoading = false;
        state.EmployeeLeaveExportError = null;
      })
      .addCase(exportEmployeeLeaveExcel.rejected, (state, action) => {
        state.EmployeeLeaveExportLoading = false;
        state.EmployeeLeaveExportError = action.payload;
      });
  },
});

export const {
  onCloseError,
  setColumnSearch,
  EmployeeLeaveAddDrawerStatus,
  EmployeeLeaveEditWithDrawerStatus,
  updateSortFilters,
  reduceLeaveBalance,
  clearExportError,
} = manageEmployeeLeaveSlice.actions;

export default manageEmployeeLeaveSlice.reducer;

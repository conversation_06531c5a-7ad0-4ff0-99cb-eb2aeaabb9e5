import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Form, Row, Col, Card } from "antd";
import Chart from "react-apexcharts";
// import {
//   ArrowDownOutlined
// } from '@ant-design/icons';
// import IntlMessage from "components/util-  components/IntlMessage";
// import {
//   getAttendanceDashboardReport,
//   getClassStudents
// } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import {
//     MANAGE_CLASS,
//     SCHOOL_YEAR
// } from "constants/AppConstants";

// const setLocale = (localeKey, isLocaleOn = true) =>
//     isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AttendanceStudentAttention() {
  // const [form] = Form.useForm();
  // const dispatch = useDispatch();
  const { attendanceStudentAttentions } = useSelector(
    (state) => state[ATTENDANCE_REPORT]
  );

  const columns = [
    {
      title: "Student",
      dataIndex: "student_name",
      key: "student_name",
      align: "center",
      render: (text, record) => (
        <>{record.student_name + " (" + record.student_id + ")"}</>
      ),
    },
    {
      title: "Grade",
      dataIndex: "class_name",
      key: "class_name",
      align: "center",
    },
    {
      title: "Issue",
      dataIndex: "type",
      key: "type",
      align: "center",
    },
    {
      title: "Total",
      dataIndex: "count",
      key: "count",
      align: "center",
    },
  ];

  return (
    <Card className="p-2" style={{ height: "450px" }}>
      <div className="mb-4">
        <h1 style={{ color: "#3C3C3C" }}>Requires Attention</h1>
      </div>
      <Table dataSource={attendanceStudentAttentions} columns={columns} />
    </Card>
  );
}

export default AttendanceStudentAttention;

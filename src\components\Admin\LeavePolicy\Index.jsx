import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { LEAVE_POLICY, LEAVE_TYPE } from "constants/AppConstants";
import AddLeavePolicyModal from "./Modals/index";
import {
    LeavePolicyAddDrawerStatus,
    LeavePolicyEditWithDrawerStatus,
    deleteLeavePolicy,
    getLeavePolicy,
    updateSortFilters,
    setColumnSearch,
  } from "store/slices/LeavePolicy/manageLeavePolicySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import { getLeaveType as getLeaveTypesAction} from "store/slices/LeaveType/manageLeaveTypeSlice";

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(LeavePolicyAddDrawerStatus({ errorStatus: 1, status: true }));
    const { LeavePolicyAddDrawer, LeavePolicyResult, tablePagination, sorting, filter, LeavePolicyTableLoading, permission } = useSelector(
        (state) => state[LEAVE_POLICY]
        );
    const { LeaveTypeErrors, LeaveTypeResult,ViewLeaveTypeLoader, filter: leaveTypeFilter, LeaveTypeTableLoading } = useSelector(
        (state) => state[LEAVE_TYPE]
        );
        console.log(`LeaveTypes ::`, LeaveTypeResult.data);
    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getLeavePolicy({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };
  const fetchLeaveTypes = useCallback(async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getLeaveTypesAction({
        page,
        perPage,
        filter: filterData,
        sorting: sortingData,
        showAll: false,
      })
    );
  }, [dispatch]);
  useEffect(() => {
    fetchLeaveTypes(1, 100, {}, {});
  }, [fetchLeaveTypes]);

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };

    const handleDelete = (record) => {
        dispatch(deleteLeavePolicy(record.id)).then(() => {
            getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        })
    }
    const handleUpdate = (record) => {
        dispatch(LeavePolicyEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) {
            console.log(error);
        }

    };

    const leaveTypeColumns = (LeaveTypeResult.data || []).map((type) => ({
      title: type.name,
      dataIndex: type.name,
      key: type.name,
      render: (text) => text ?? "-", // Show "-" if no value
    }));
  const dataSource = useMemo(() => {
  return (LeavePolicyResult?.data || []).map((row) => {
    const leaveObj = {};
    (row.leave_balances || []).forEach((lb) => {
      leaveObj[lb.leave_type_name] = lb.balance;
    });
    return { ...row, ...leaveObj };
  });
}, [LeavePolicyResult?.data]); //to do add designation id by addinng, 
    const columns = [
        {
            title: setLocale('Employee'),
            dataIndex: "user_name",
            key: "user_name",
            sorter: true,
            ...getColumnSearchProps('name'),
        },
        {
            title: setLocale('Designation'),
            dataIndex: "designation_name",
            key: "designation_name",
            sorter: true,
            // ...getColumnSearchProps('created_at'),
        },
        // Dynamically add leave type columns here
        ...leaveTypeColumns,
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                    {/* {permission.includes("View") && (
                        <Link to={`../../app/leave-policy_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Leave Policy')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale('leavepolicy.add')}
                            </Button>
                        )}
                    </section>
                    {LeavePolicyAddDrawer && <AddLeavePolicyModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={LeavePolicyTableLoading}
                            rowKey={record => record.id}
                            dataSource={dataSource ?? [] }
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}

                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

import React,{useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LOCATIONS } from "constants/AppConstants";
import {
    LocationsAddDrawerStatus,
    createLocations,
    getLocations,
    onCloseError
  } from "store/slices/Locations/manageLocationsSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        LocationsAddDrawer,
        LocationsButtonAndModelLabel,
        sorting,
        filter,
        LocationsErrors,
        LocationsShowMessage,
        LocationsButtonSpinner,
        LocationsEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[LOCATIONS]
    );
const onClose = () => {
    dispatch(LocationsAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (LocationsEditData && LocationsEditData.id) {
    // If editing, include the id in the form values
    formValues.id = LocationsEditData.id;
  }

  await dispatch(createLocations(formValues));
};

  useEffect(() => {
    if (Object.keys(LocationsErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getLocations({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [LocationsErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={LocationsButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={LocationsAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: LocationsEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={LocationsShowMessage && LocationsErrors.name ? "error" : ""}
                help={LocationsShowMessage && LocationsErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={LocationsButtonSpinner}
            >
              {LocationsButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton, Tag } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { CHARGES_TYPES } from "constants/AppConstants";
import AddChargesTypesModal from "./Modals/index";
import {
    ChargesTypesAddDrawerStatus,
    ChargesTypesEditWithDrawerStatus,
    deleteChargesTypes,
    getChargesTypes,
    updateSortFilters,
    setColumnSearch
} from "store/slices/ChargesTypes/manageChargesTypesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const dispatch = useDispatch();
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(ChargesTypesAddDrawerStatus({ errorStatus: 1, status: true }));
    const { ChargesTypesAddDrawer, ChargesTypesResult, tablePagination, sorting, filter, ChargesTypesTableLoading, permission } = useSelector(
        (state) => state[CHARGES_TYPES]
    );


    const getModuleData = async (page, perPage, filterData, sortingData) => {
        await dispatch(getChargesTypes({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
    }

    const handleSearch = async (confirm) => {
        confirm();
        getModuleData(1, tablePagination.pageSize, filter, sorting);
    };

    const handleReset = async (dataIndex) => {
        const { [dataIndex]: removedProperty, ...newObject } = filter;
        await dispatch(setColumnSearch(newObject));
        getModuleData(tablePagination.current, tablePagination.pageSize, newObject, sorting);
    };

    const handleOnChange = async (dataIndex, value, confirm) => {
        await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
        if (value === '') {
            confirm();
            getModuleData(tablePagination.current, tablePagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
        }
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder=""
                    autoFocus
                    value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
                    onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
                    onPressEnter={(e) => handleSearch(confirm)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={(e) => handleSearch(confirm)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(dataIndex)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => {
            if (dataIndex === 'charge_category') {
                const columnValue = dataIndex.split('.').reduce((acc, key) => acc[key], record);
                return columnValue ? columnValue.toString().toLowerCase().includes(value.toLowerCase()) : false;
            } else {
                return record[dataIndex].toString().toLowerCase().includes(value.toLowerCase());
            }
        },
        render: (text) =>
            filter[dataIndex]
                ? (
                    <Highlighter
                        highlightStyle={{
                            backgroundColor: '#ffc069',
                            padding: 0,
                        }}
                        searchWords={[filter[dataIndex]]}
                        autoEscape
                        textToHighlight={text ? text.toString() : ''}
                    />
                ) : (
                    text
                ),
    });

    useEffect(() => {
        getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
    }, []);


    const handlePageChange = (page, pageSize) => {
        getModuleData(page, pageSize, filter, sorting);
    };

    const handleDelete = (record) => {
        dispatch(deleteChargesTypes(record.id)).then(() => {
            getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
        })
    }
    const handleUpdate = (record) => {
        dispatch(ChargesTypesEditWithDrawerStatus({ errorStatus: 1, data: record }));
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
            getModuleData(1, tablePagination.pageSize, filter, sorting);
        } catch (error) {
            console.log(error);
        }

    };


    const columns = [
        {
            title: setLocale("charges_and_invoices.charge_type"),
            dataIndex: "charge_type",
            key: "charge_type",
            sorter: true,
            ...getColumnSearchProps('charge_type'),
        },
        {
            title: setLocale('charges_and_invoices.charge_category'),
            dataIndex: "charge_category",
            key: "charge_category",
            sorter: true,
            ...getColumnSearchProps('charge_category'),
        },
        {
            title: setLocale("charges_and_invoices.is_recurring"),
            dataIndex: "is_recurring",
            key: "is_recurring",
            sorter: true,
            ...getColumnSearchProps('is_recurring'),
        },
        {
            title: setLocale("charges_and_invoices.all_grades"),
            dataIndex: "all_grades",
            key: "all_grades",
            sorter: true,
            ...getColumnSearchProps('all_grades'),
        },
        {
            title: setLocale("charges_and_invoices.is_nsf"),
            dataIndex: "is_nsf",
            key: "is_nsf",
            sorter: true,
            ...getColumnSearchProps('is_nsf'),
        },
        {
            title: setLocale("charges_and_invoices.allow_garde_filter"),
            dataIndex: "allow_garde_filter",
            key: "allow_garde_filter",
            sorter: true,
            ...getColumnSearchProps('allow_garde_filter'),
        },
        {
            title: setLocale("charges_and_invoices.is_taxable"),
            dataIndex: "is_taxable",
            key: "is_taxable",
            sorter: true,
            ...getColumnSearchProps('is_taxable'),
        },
        // {
        //     title: setLocale("charges_and_invoices.tax_type_id"),
        //     dataIndex: "tax_type",
        //     key: "tax_type",
        //     sorter: true,
        //     ...getColumnSearchProps('tax_type'),
        // },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sure_to_delete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                    {/* {permission.includes("View") && (
                        <Link to={`../../app/charges-types_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('charges_and_invoices.title')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale('charges_and_invoices.add')}
                            </Button>
                        )}
                    </section>
                    {ChargesTypesAddDrawer && <AddChargesTypesModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={ChargesTypesTableLoading}
                            rowKey={record => record.id}
                            dataSource={ChargesTypesResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

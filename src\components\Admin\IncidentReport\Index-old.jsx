import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Table, Popconfirm, Pagination, Button, Input, Space, Breadcrumb, Skeleton } from 'antd';
import { DeleteOutlined, EditOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';
import { INCIDENT_REPORT } from "constants/AppConstants";
import AddIncidentReportModal from "./Modals/Add";
import {
    IncidentReportAddDrawerStatus,
    IncidentReportEditWithDrawerStatus,
    deleteIncidentReport,
    getIncidentReport,
    updateSortFilters,
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { useNavigate } from 'react-router-dom';
import { Link } from "react-router-dom";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [searchText, setSearchText] = useState('');
    const [searchedColumn, setSearchedColumn] = useState('');
    const searchInput = useRef(null);
    const handleOpenModal = () => dispatch(IncidentReportAddDrawerStatus(true));
    const { IncidentReportAddDrawer, IncidentReportResult, tablePagination, sorting, filter, IncidentReportTableLoading, permission } = useSelector(
        (state) => state[INCIDENT_REPORT]
    );
    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };
    const handleReset = (clearFilters) => {
        clearFilters();
        setSearchText('');
    };
    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div
                style={{
                    padding: 8,
                }}
                onKeyDown={(e) => e.stopPropagation()}
            >
                <Input
                    ref={searchInput}
                    placeholder={`Search ${dataIndex}`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{
                        marginBottom: 8,
                        display: 'block',
                    }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('Search')}
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(clearFilters)
                            confirm();
                        }}
                        size="small"
                        style={{
                            width: 90,
                        }}
                    >
                        {setLocale('Reset')}
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined
                style={{
                    color: filtered ? '#1677ff' : undefined,
                }}
            />
        ),
        onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{
                        backgroundColor: '#ffc069',
                        padding: 0,
                    }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    useEffect(() => {
        dispatch(getIncidentReport({ page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting }));
    }, []);


    const handlePageChange = (page, pageSize) => {
        dispatch(getIncidentReport({ page: page, perPage: pageSize, filter: filter, sorting: sorting }));
    };

    const handleDelete = (record) => {
        dispatch(deleteIncidentReport(record.id)).then(() => {
            dispatch(getIncidentReport({ page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting }));
        })
    }
    const handleUpdate = (record) => {
        navigate(`/app/edit-incident-report/${record.enc_id}`)
    }

    const handleTableChange = async (pagination, filters, sorter) => {
        const sortOrder = sorter.order;
        const sorting = {
            [sorter.field]: sortOrder === 'ascend' ? 'asc' : 'desc',
        };

        try {
            await dispatch(updateSortFilters({ filter: filters, sorting: sorting }));
            await dispatch(
                getIncidentReport({
                    page: 1,
                    perPage: tablePagination.pageSize,
                    filter: filters,
                    sorting: sorting,
                })
            );
        } catch (error) {
            console.log(error);
        }

    };


    const columns = [
        {
            title: setLocale('name'),
            dataIndex: "title",
            key: "name",
            sorter: true,
            ...getColumnSearchProps('name'),
        },
        {
            title: setLocale('createdAt'),
            dataIndex: "created_at",
            key: "created_at",
            sorter: true,
            ...getColumnSearchProps('created_at'),
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>
                    {permission.includes("Delete") && (
                        <Popconfirm title={setLocale('sureToDelete')} onConfirm={(e) => handleDelete(record)}>
                            <DeleteOutlined style={{ fontSize: '15px' }} className="text-danger" /> &nbsp;
                        </Popconfirm>
                    )}
                    {permission.includes("Update") && (
                        <EditOutlined style={{ fontSize: '15px', marginRight: '9px' }} className="text-success" onClick={(e) => handleUpdate(record)} />
                    )}
                    {/* {permission.includes("View") && (
                        <Link to={`../../app/incident-report_view/${record.enc_id}`}>
                            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                        </Link>
                    )} */}
                </>
            )
        },
    ];

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Incident Report')}</Breadcrumb.Item>
            </Breadcrumb>
            <>
                <div className="code-box">
                    <section className="code-box-demo">
                        {permission.includes("Create") && (
                            <>
                                <Link to={`../../app/add-incident-report`}>
                                    <Button
                                        className="ant-btn-round ant-btn-sm"
                                        type="primary"
                                        style={{ float: "right", margin: "5px" }}
                                    // onClick={handleOpenModal}
                                    >
                                        {setLocale('incidentReport.add')}
                                    </Button>
                                </Link>
                            </>
                        )}
                    </section>
                    {IncidentReportAddDrawer && <AddIncidentReportModal />}
                    <section className="code-box-description">
                        <Table
                            onChange={handleTableChange}
                            columns={columns}
                            loading={IncidentReportTableLoading}
                            rowKey={record => record.id}
                            dataSource={IncidentReportResult.data ?? []}
                            pagination={false}
                        />
                        <Pagination
                            style={{ margin: '16px', float: 'right' }}
                            current={tablePagination.current}
                            pageSize={tablePagination.pageSize}
                            total={tablePagination.total}
                            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
                            pageSizeOptions={['10', '20', '50', '100', '1000']}
                            showQuickJumper
                            onChange={handlePageChange}
                        />
                    </section>
                </div>
            </>
        </>
    );
}

export default Index;

import React, { useEffect, useState } from 'react';
import moment from "moment";
import { Switch, Checkbox, DatePicker, Button, Col, Drawer, Form, Input, Row, Select, Skeleton, Space, Spin } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LEAVE_REQUEST } from "constants/AppConstants";
import {
  setNewLeaveRequestDrawer,
  addNewLeaveRequest,
  // getAttendanceCode,
  getLeaveRequest
} from "store/slices/LeaveRequest/manageLeaveRequestSlice.js";
// import {
//   getAttendanceCodes,
//   getClassStudents
// } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import {
  getAbsentReason
} from "store/slices/AbsentReason/manageAbsentReasonSlice.js";
// import {
//   getManageClass,
// } from "store/slices/ManageClass/manageManageClassSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import TextArea from 'antd/lib/input/TextArea';
// import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import { MANAGE_CLASS } from "constants/AppConstants";
import { ABSENT_REASON } from "constants/AppConstants";
import { STUDENT } from "constants/student/index";
import { searchStudents } from "store/slices/Student/manageStudentSlice.js";
import debounce from "lodash/debounce";
import { USER_INFORMATION } from 'constants/AuthConstant';


const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { RangePicker } = DatePicker;
const { Option } = Select;
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
// const userOrganizations = userInformation.organizations;


const AddNewLeaveRequest = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
const [organization, setOrganization] = useState(selectedOrganization);

  // const {
  //   attendanceCodes,
  //   classStudents
  // } = useSelector((state) => state[ATTENDANCE_REPORT]);
  // const { 
  //   ManageClassResult,
  //   ManageClassTableLoading
  // } = useSelector((state) => state[MANAGE_CLASS])
  const { 
    AbsentReasonResult,
    AbsentReasonTableLoading
  } = useSelector((state) => state[ABSENT_REASON]);  
  const {
    sorting, filter,
    // AttendanceCode,
    addNewLeaveRequestDrawer,
    // LeaveRequestButtonAndModelLabel,
    LeaveRequestErrors,
    LeaveRequestShowMessage,
    LeaveRequestButtonSpinner,
    // LeaveRequestEditData,
    statuses,
    tablePagination
  } = useSelector((state) => state[LEAVE_REQUEST]);
  const { 
    searchedStudents, 
    StudentButtonSpinner 
  } = useSelector((state) => state[STUDENT]);

  const onClose = () => dispatch(setNewLeaveRequestDrawer(false));
  useEffect(() => {  
    // form.setFieldValue('is_full_day', true);  
    // dispatch(getAttendanceCodes());
    // dispatch(getManageClass(null));
    dispatch(getAbsentReason());
  }, []);

  // const getStudentByClass = (value) => {
  //     form.setFieldsValue({ student_id: null });
  //     dispatch(getClassStudents({ id: value }));
  // };

  const handleSearch = debounce(async (value) => {
    if (value) {    
      await dispatch(searchStudents({ string: value, organization_id: organization }))
    }
  }, 300);

  const onSubmit = async (values) => {
     const dateRange = values["date"];
      /** make date formate */
      values.from_date = moment(dateRange[0]).format("YYYY-MM-DD");
      values.to_date = moment(dateRange[1]).format("YYYY-MM-DD");                
      await dispatch(addNewLeaveRequest(values)).then((result) => {        
        if (result?.payload) {
          getModuleData(tablePagination.current, tablePagination.pageSize, filter, sorting);
          form.resetFields();
          onClose();
        }
      })
  }
  // const onChangeFullDay = (checked) => {        
  //   form.setFieldsValue({ is_full_day: checked });    
  // }

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };
  const getModuleData = async (page, perPage, filterData, sortingData) => {
      await dispatch(getLeaveRequest({ page: page, perPage: perPage, filter: filterData, sorting: sortingData }));
  }

  return (
    <>

      <Drawer title={setLocale('leave_request.add')} width={window.innerWidth > 800 ? "50%" : window.innerWidth - 100}
        onClose={onClose} open={addNewLeaveRequestDrawer} maskClosable={false} zIndex={1002} bodyStyle={{ paddingBottom: 80, }} >
        <Skeleton active loading={    AbsentReasonTableLoading}>
          <Form layout="vertical" onFinish={onSubmit} form={form} onFinishFailed={onFinishFailed} autoComplete="off"
            initialValues={{
              status: 1,
              attendance_code: 3
            }}>

            <Row gutter={16}>
              <Col xs={24} sm={24} md={12} lg={12} xl={12} className="px-2">
                <Form.Item name="date"
                  label={setLocale("attendance_report.date_from_to")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("attendance_report.daterange_error"),
                    },
                  ]} validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.date ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.date}>
                  <RangePicker className="w-100" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Form.Item name="class_student_ids" label={setLocale("student.label")}
                  extra={
                    <span style={{ fontSize: "12px", color: "#888", opacity: "0.9", }} >
                      {" "} For Search(First Name,Last Name, Student ID)
                    </span>
                  }
                  rules={[
                    {
                      required: true,
                      message: setLocale("student.label_error"),
                    },
                  ]} >
                  <Select showSearch placeholder="Search..."
                    mode="multiple"
                    maxTagCount={2}
                    onSearch={handleSearch}
                    notFoundContent={ StudentButtonSpinner  && <Spin size="small" /> }
                    filterOption={false} >
                    {searchedStudents ? searchedStudents.map((option) => (
                        <Option key={option.id} value={option.id}>
                          {option.label}
                        </Option>
                      )) : ""}
                  </Select>
                </Form.Item>
              </Col>
              {/* <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Form.Item name="class"
                  label={setLocale("classes.label")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("classes.label_error"),
                    },
                  ]} 
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.class ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.class}>
                  <Select  optionLabelProp="label" onChange={getStudentByClass} 
                        optionFilterProp="children" showSearch
                        filterOption={(inputValue, option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0 }>
                    {ManageClassResult ? ManageClassResult.map((clas, index) => (
                          <Option value={clas.id} key={index} label={clas.name}>
                            {clas.name}
                          </Option>
                        ))
                      : null}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Form.Item name="student_id"
                  label={setLocale("attendance_report.select_student")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("attendance_report.student_error"),
                    },
                  ]} validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.student_id ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.student_id}>
                  <Select optionLabelProp="label"
                    optionFilterProp="children" showSearch
                    filterOption={(inputValue, option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0 } >
                      { classStudents ? classStudents.map((clsStd, index) => (
                          <Option value={clsStd.students.id} key={index} label={clsStd.students.full_name} >
                            {clsStd.students.full_name + " (" + clsStd.students.student_id + ")"}
                          </Option>
                      )) : null}
                  </Select>
                </Form.Item>
              </Col> */}
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Form.Item name="attendance_code"
                  label={setLocale("leave_request.attendance_code")}
                  rules={[
                    {
                      required: true,
                      message: setLocale('leave_request.attendance_code_error'),
                    },
                  ]}
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.attendance_code ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.attendance_code} >
                  <Select disabled className='rounded-0' optionLabelProp="label"  optionFilterProp="children">
                    <Option value={3} label={setLocale('Execused Absence')} >{setLocale('Execused Absence')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
              <Form.Item
                name="absent_reason_id"
                label={setLocale('Leave Request Reason')}
                rules={[
                  {
                    required: true,
                    message: setLocale('Leave Request Reason required'),
                  },
                ]}
                validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.absent_reason_id ? "error" : ""}
                help={LeaveRequestShowMessage && LeaveRequestErrors.absent_reason_id}>
                <Select className='rounded-0' showSearch optionLabelProp="label"                 
                  optionFilterProp="children"
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  options={AbsentReasonResult.data ? AbsentReasonResult.data : null}
                />
              </Form.Item>
            </Col>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Form.Item name="status" label={'Status'}
                  rules={[
                    {
                      required: true,
                      message: setLocale('conversation_template.status_error'),
                    },
                  ]}
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.status ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.status} >
                  <Select disabled optionLabelProp="label" options={statuses ? statuses : null} />
                </Form.Item>
              </Col>
              {/* <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Form.Item name="is_full_day" label={'Mark all classes'}
                  rules={[
                    {
                      required: false,
                      message: setLocale('conversation_template.status_error'),
                    },
                  ]}
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.is_full_day ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.is_full_day} >                  
                  <Switch defaultChecked onChange={onChangeFullDay} />
                </Form.Item>
              </Col> */}
              
              <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                <Form.Item name="approver_comment" label={'Comments'}
                  rules={[
                    {
                      required: true,
                      message: 'Comments is required',
                    },
                  ]}
                  validateStatus={LeaveRequestShowMessage && LeaveRequestErrors.approver_comment ? "error" : ""}
                  help={LeaveRequestShowMessage && LeaveRequestErrors.approver_comment} >
                  <TextArea style={{ height: 120, marginBottom: 24, }} className='rounded-0' />
                </Form.Item>
              </Col>
            </Row>

            <Space>
              <Button type="primary" htmlType="submit" loading={LeaveRequestButtonSpinner} >
                {setLocale("save")}
              </Button>
              <Button onClick={onClose}>{setLocale('cancel')}</Button>
            </Space>
          </Form>
        </Skeleton>
      </Drawer>
    </>
  );
};
export default AddNewLeaveRequest;


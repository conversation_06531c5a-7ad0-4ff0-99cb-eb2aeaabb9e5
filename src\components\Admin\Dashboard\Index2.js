import React, { useEffect,useState } from 'react';
import moment from 'moment';
import { useDispatch,useSelector } from 'react-redux';
import { Statistic,message,Form,DatePicker,Select,Tooltip, Dropdown, Menu,Space,Button,Col, Row, Avatar, List,Input, Typography,Skeleton,Modal, Table, Tag, Tabs } from 'antd';
import { ExclamationCircleFilled, CloseCircleTwoTone,CheckCircleTwoTone,EllipsisOutlined ,UserOutlined,PlusOutlined,  BookOutlined, CalendarFilled, BookFilled , EyeFilled, SortAscendingOutlined, DownloadOutlined } from '@ant-design/icons';
// import { finalizedCourseGradeCategory,addStudentObtainedMarks,addCourseGradeBookCategory,getClassStudentsForGrades } from 'store/slices/gradeSystem/studentGradeBookSlice';
// import { getAllGradeBookCategory } from 'store/slices/gradeSystem/gradeSystemSlice';

import { 
  finalizedCourseGradeCategory,
  addStudentObtainedMarks,
  addCourseGradeBookCategory,
  getClassStudentsForGrades
} from "store/slices/Grade/studentGradeBookSlice.js";
import { GRADE_BOOK_CATEGORY, STUDENT_GRADE_BOOK_SLICE, MANAGE_CLASS } from "constants/AppConstants";
import { getGradeBookCategory } from "store/slices/GradeBookCategory/manageGradeBookCategorySlice.js";

import IntlMessage from 'components/util-components/IntlMessage';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;


function StudentGrades(props) {
  const { GradeBookCategoryResult } = useSelector((state) => state[GRADE_BOOK_CATEGORY])
  const { classStudentsForGrades, activeStudents } = useSelector((state) => state[STUDENT_GRADE_BOOK_SLICE])
  const { ClassCoursesData, ClassCourseEditData } = useSelector((state) => state[MANAGE_CLASS]);
  console.log(ClassCoursesData, ClassCourseEditData)

  const iconColor = { color: '#BC404F' };
  const [form] = Form.useForm();

  const { data } = props;  
  const [courseId, setCourseId] = useState(ClassCourseEditData?.course_id);

  const dispatch = useDispatch();
  const [loadStudents,setLoadStudents] = useState(true);
  const [studenMarkPercentage, setStudenMarkPercentage] = useState({});
  const [loading, setLoading] = useState(false);
  const [addNewGradeCategory, setAddNewGradeCategory] = useState(false);


  const [messageApi, contextHolder] = message.useMessage();
  const success = (msg) => {
    messageApi.open({
      type: 'success',
      content: msg,
      style: {
        marginTop: '90vh',
      },
    })
  }
  const error = (msg) => {
    messageApi.open({
      type: 'error',
      content: msg,
      style: {
        marginTop: '90vh',
      },
    })
  }


  useEffect(() => {
    /**get All GradeBook Categories  */
    dispatch(getGradeBookCategory())
    /**get All CourseGradeBookCategories with student Marks */
    dispatch(getClassStudentsForGrades( 
      { class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }))
      .then((response) => {
        setLoadStudents(false);
      });

  }, []);


 
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Overall Avg',
      dataIndex: 'avg',
      key: 'avg',
    },
    {
      title: 'Exam',
      style: {backgroundColor: "red"},
      key: 'exam',
      render: (text, record, index) => {
        const headerCellStyle = index === 2 ? { background: 'lightblue' } : {}; // Apply CSS only to the third child
        return {
          children: text,
          props: {
            style: headerCellStyle,
          },
        };
      },
      children: [
        {
          title: 'Exam 1 (100%)',
          dataIndex: 'exam1',
          key: 'exam1',
          style: { background: 'white !important' },
          className: 'grade-table-cell-red',
        },
        {
          title: 'Exam 2 (100%)',
          dataIndex: 'exam2',
          key: 'exam2',
          className: 'grade-table-cell-red',
        },
        {
          title: 'Exam 3 (100%)',
          dataIndex: 'exam3',
          key: 'exam3',
          className: 'grade-table-cell-red',
        },
        {
          title: 'Exam 4 (100%)',
          dataIndex: 'exam4',
          key: 'exam4',
          className: 'grade-table-cell-red',
        },
      ],
    },
    {
      title: 'Assignment',
      children: [
        {
          title: 'Assignment 1 (100%)',
          dataIndex: 'assign1',
          key: 'assign1',
          className: 'grade-table-cell-yellow',
        },
        {
          title: 'Assignment 2 (100%)',
          dataIndex: 'assign2',
          key: 'assign2',
          className: 'grade-table-cell-yellow',
        },
      ]
    },
    {
      title: 'Quiz',
      children: [
        {
          title: 'Quiz 1 (100%)',
          dataIndex: 'quiz1',
          key: 'quiz1',
          className: 'grade-table-cell-green',
        },
        {
          title: 'Quiz 2 (100%)',
          dataIndex: 'quiz2',
          key: 'quiz2',
          className: 'grade-table-cell-green',
        },
      ]
    },
  ];
  const dataItem = [
    {
      key: '1',
      name: 'Isac',
      avg: '24%',
      exam1: "59%",
      exam2: "34%",
      exam3: "20%",
      exam4: "48%",
      assign1: '73%',
      assign2: "30%",
      quiz1: "27%",
      quiz2:"14%",
      
    },
    {
      key: '2',
      name: 'Anthony',
      avg: "25%",
      exam1: "12%",
      exam2: "25%",
      exam3: "46%",
      exam4: "12%",
      assign1: '82%',
      assign2: "56%",
      quiz1: "82%",
      quiz2:"12%",
      
    },
    {
      key: '3',
      name: 'Samuel',
      avg: "25%",
      exam1: "59%",
      exam2: "34%",
      exam3: "20%",
      exam4: "48%",
      assign1: '73%',
      assign2: "30%",
      quiz1: "27%",
      quiz2:"14%",
      
    },
    {
      key: '4',
      name: 'Asim',
      avg: "27%",
      exam1: "12%",
      exam2: "25%",
      exam3: "46%",
      exam4: "12%",
      assign1: '82%',
      assign2: "56%",
      quiz1: "82%",
      quiz2:"12%",
      
    },
    {
      key: '1',
      name: 'sam',
      avg: "86%",
      exam1: "59%",
      exam2: "34%",
      exam3: "20%",
      exam4: "48%",
      assign1: '73%',
      assign2: "30%",
      quiz1: "27%",
      quiz2:"14%",
      
    },

  ]
  const onTabChange = (key) => {
    console.log(key);
  };
  const itemsTab = [
    {
      key: '1',
      label: 'All',
      children: '',
    },
    {
      key: '2',
      label: 'Assesment',
      children: '',
    },
    {
      key: '3',
      label: 'Quizzes',
      children: '',
    },
    {
      key: '4',
      label: 'Assignment',
      children: '',
    },
    {
      key: '3',
      label: 'Summary',
      children: '',
    },
  ];
  return (
    <>
          <Row gutter={16} className='my-3'>
            <Col xs={24} sm={24} md={12} lg={24} xl={24}>
            <div className='grade-book-select'>
            <h5 className='my-2 mx-2'>Filter: </h5> 
              <Select
                className='mx-2'
                defaultValue={{ label: <>Year</> }}
                optionLabelProp="label"
              >
                <Option value="2024" label={<>2024</>}></Option>
                <Option value="2023" label={<>2023</>}></Option>
                <Option value="2022" label={<>2022</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <>Grade level</> }}
                optionLabelProp="label"
              >
                <Option value="kg" label={<>kg</>}></Option>
                <Option value="1" label={<>1</>}></Option>
                <Option value="2" label={<>2</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <>Class</> }}
                optionLabelProp="label"
              >
                <Option value="Class 1" label={<>Class 1</>}></Option>
                <Option value="Class 2" label={<>Class 2</>}></Option>
                <Option value="Class 3" label={<>Class 3</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <>Subject</> }}
                optionLabelProp="label"
              >
                <Option value="English" label={<> English</>}></Option>
                <Option value="Science" label={<> Science</>}></Option>
                <Option value="Mathematics" label={<> Mathematics</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <>Term</> }}
                optionLabelProp="label"
              >
                <Option value="Mid" label={<> Mid</>}></Option>
                <Option value="Final" label={<> Final</>}></Option>
              </Select>
            </div>
            </Col>
          </Row>
          <hr></hr>
          <Row gutter={16} className='mt-3 mb-0'>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <div className='grade-book-select'>
            <Tabs defaultActiveKey="1" items={itemsTab} onChange={onTabChange} />
            </div>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12} xl={12} className='mt-2'>
            <div style={{display: "flex", justifyContent: "end"}}>
              <Button className='mx-2' style={{backgroundColor: "#464A53"}} size='small' >
              <PlusOutlined/> Assignment
              </Button>
              <Button className='mx-2' style={{backgroundColor: "#464A53"}} size='small' >
              <PlusOutlined/> Exam
              </Button>
              <Button className='mx-2' style={{backgroundColor: "#464A53"}} size='small' >
              <PlusOutlined/> Quiz
              </Button>
              </div>
            </Col>
          </Row>
          <hr></hr>
          <Row gutter={16} className='my-3'>
            <Col xs={24} sm={24} md={12} lg={18} xl={18}>
            <div className='grade-book-select'>
            <h5 className='my-2 mx-2'>Filter by: </h5> 
              <Select
                className='mx-2'
                defaultValue={{ label: <><CalendarFilled style={iconColor}  /> Date Range</> }}
                optionLabelProp="label"
              >
                <Option value="2022-2023" label={<><CalendarFilled style={iconColor}  /> 2022-2023</>}></Option>
                <Option value="2023-2024" label={<><CalendarFilled style={iconColor}  /> 2023-2024</>}></Option>
                <Option value="2024-2025" label={<><CalendarFilled style={iconColor}  /> 2024-2025</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <><UserOutlined style={iconColor}  /> Student</> }}
                optionLabelProp="label"
              >
                <Option value="Student 1" label={<><UserOutlined style={iconColor}  /> Student 1</>}></Option>
                <Option value="Student 2" label={<><UserOutlined style={iconColor}  /> Student 2</>}></Option>
                <Option value="Student 3" label={<><UserOutlined style={iconColor}  /> Student 3</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <><SortAscendingOutlined style={iconColor}  /> Sort by</> }}
                optionLabelProp="label"
              >
                <Option value="1" label={<><SortAscendingOutlined style={iconColor}  /> 1</>}></Option>
                <Option value="2" label={<><SortAscendingOutlined style={iconColor}  /> 2</>}></Option>
                <Option value="3" label={<><SortAscendingOutlined style={iconColor}  /> 3</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <><EyeFilled style={iconColor}  /> View as</> }}
                optionLabelProp="label"
              >
                <Option value="Excel" label={<><EyeFilled style={iconColor}  /> Excel</>}></Option>
                <Option value="PDF" label={<><EyeFilled style={iconColor}  /> PDF</>}></Option>
              </Select>
            </div>
            </Col>
            <Col xs={24} sm={24} md={12} lg={6} xl={6}>
            <div style={{display: "flex", justifyContent: "end"}}>
              <Button className='mx-2' size='small' > Compare
              </Button>
              <Button className='mx-2' size='small' >
              <DownloadOutlined /> Export CSV
              </Button>
              </div>
            </Col>
          </Row>
          <Row gutter={16} className='my-3'>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>  
          <Table className='Grade-book-table' columns={columns} dataSource={dataItem} />
          </Col>
          </Row>
    </>
  )
}

export default StudentGrades

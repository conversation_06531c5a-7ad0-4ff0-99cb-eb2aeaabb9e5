import React, { useState, useEffect } from 'react';
import { Button, Col, Drawer, Form, Input, Row, Space,Skeleton,Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { MANAGE_BUSES,MANAGE_ROUTES } from "constants/AppConstants";
import {
    ManageBusesAddDrawerStatus,
    createManageBuses,
    getManageBuses,
    onCloseError
  } from "store/slices/ManageBuses/manageManageBusesSlice.js";
import {
  getManageRoutes,
} from "store/slices/ManageRoutes/manageManageRoutesSlice.js";
import PhoneNumberInput  from "components/Admin/PhoneNumberInput";

import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
const {DrawerStatus, sorting, filter, ManageBusesAddDrawer, ManageBusesButtonAndModelLabel, ManageBusesErrors, ManageBusesShowMessage, ManageBusesButtonSpinner, ManageBusesEditData, tablePagination } = useSelector(
    (state) => state[MANAGE_BUSES]
  );
const {  ManageRoutesResult } = useSelector(
  (state) => state[MANAGE_ROUTES]
  );
const [pageLoading, setPageLoading] = useState(true);
  const onClose = () => {
    dispatch(ManageBusesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
useEffect(() => {

  const fetchData = async () => {
    try {
      await dispatch(getManageRoutes({perPage: 1000}));
      setPageLoading(false);
    } catch (error) {
      console.error('Error fetching school year data:', error);
    }
  };
    fetchData();
}, [dispatch]);
const onSubmit = async (formValues) => {

  if (ManageBusesEditData && ManageBusesEditData.id) {
    // If editing, include the id in the form values
    formValues.id = ManageBusesEditData.id;
  }

  await dispatch(createManageBuses(formValues))
};
useEffect(() => {
    if (Object.keys(ManageBusesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getManageBuses({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ManageBusesErrors]);
const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={ManageBusesButtonAndModelLabel}
        width={920}
        onClose={onClose}
        open={ManageBusesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Skeleton active loading={pageLoading}>
          <Form
            layout="vertical"
            onFinish={onSubmit}
            form={form}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            initialValues={{
              ...ManageBusesEditData,
              route_id: ManageBusesEditData && ManageBusesEditData.route_id ? ManageBusesEditData?.route_id : ManageRoutesResult?.data?.length > 0 ? ManageRoutesResult?.data[0]?.id : null,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="route_id"
                  label={setLocale('routes.label')}
                  rules={[
                    {
                      required: true,
                      message: setLocale('routes.label_error'),
                    },
                  ]}
                  validateStatus={ManageBusesShowMessage && ManageBusesErrors.route_id ? "error" : ""}
                  extra={ManageBusesShowMessage && ManageBusesErrors.route_id}
                >
                  <Select showSearch optionLabelProp="label"
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                    }
                    options={ManageRoutesResult?.data ? ManageRoutesResult.data : null}
                  />
                </Form.Item>

              </Col>
              <Col span={12}>

                <Form.Item
                  name="driver_name"
                  label={setLocale('bus.driver_name')}
                  rules={[
                    {
                      required: true,
                      message: setLocale('bus.driver_name_error'),
                    },
                  ]}
                  validateStatus={ManageBusesShowMessage && ManageBusesErrors.driver_name ? "error" : ""}
                  extra={ManageBusesShowMessage && ManageBusesErrors.driver_name}
                >
                  <Input />
                </Form.Item>
              </Col>

            </Row>
            <Row gutter={16}>
            <Col span={12}>
                <Form.Item
                  className="number-error"
                name='driver_cell'
                label={setLocale('bus.driver_cell')}
                rules={[
                  {
                    required: true,
                    message: setLocale('bus.driver_cell_error'),
                  },
                  {
                    validator: (_, value) => {
                      if (!value) {
                        return Promise.resolve();
                      }
                      const numericValue = value.replace(/\D/g, "");
                      if (numericValue.length === 10) {
                        return Promise.resolve();
                      } else {
                        return Promise.reject("Phone number must be 10 digits");
                      }
                    },
                  },
                ]}
                validateStatus={ManageBusesShowMessage && ManageBusesShowMessage.driver_cell ? "error" : ""}
                help={ManageBusesShowMessage && ManageBusesShowMessage.driver_cell}
              >
                <PhoneNumberInput />
              </Form.Item>
            </Col>

            <Col span={12}>

              <Form.Item
                name="registration_number"
                label={setLocale('bus.registration_number')}
                rules={[
                  {
                    required: true,
                    message: setLocale('bus.registration_number_error'),
                  },
                ]}
                validateStatus={ManageBusesShowMessage && ManageBusesErrors.registration_number ? "error" : ""}
                help={ManageBusesShowMessage && ManageBusesErrors.registration_number}
              >
                <Input />
              </Form.Item>
            </Col>


          </Row>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={ManageBusesButtonSpinner}
              >
                {setLocale("save")}
              </Button>
              <Button onClick={onClose}>{setLocale('cancel')}</Button>
            </Space>
          </Form>
        </Skeleton>
      </Drawer>
    </>
  );
};
export default Index;


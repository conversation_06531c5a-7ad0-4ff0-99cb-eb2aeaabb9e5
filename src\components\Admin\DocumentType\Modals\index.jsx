import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Col,
  Drawer,
  Form,
  Input,
  Row,
  Space,
  Switch,
  Select,
} from "antd";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { DOCUMENT_TYPE, COUNTRY } from "constants/AppConstants";
import {
  DocumentTypeAddDrawerStatus,
  createDocumentType,
  getDocumentType,
  getDocumentCategory,
  onCloseError,
} from "store/slices/DocumentType/manageDocumentTypeSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
  const [form] = Form.useForm();
  const [switchValue, setSwitchValue] = useState("checked");
  const [switchAllowMultipleValue, setSwitchAllowMultipleValue] =
    useState("checked");
  const [requiredSwitchValue, setRequiredSwitchValue] = useState("checked");
  const { CountryResult } = useSelector((state) => state[COUNTRY]);
  const dispatch = useDispatch();
  const {
    DocumentTypeAddDrawer,
    DocumentTypeButtonAndModelLabel,
    sorting,
    filter,
    DocumentTypeErrors,
    DocumentTypeShowMessage,
    DocumentTypeButtonSpinner,
    DocumentTypeEditData,
    DocumentCategoryLoader,
    DocumentCategoryResult,
    tablePagination,
    DrawerStatus,
  } = useSelector((state) => state[DOCUMENT_TYPE]);
  const onClose = () => {
    dispatch(DocumentTypeAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  };
  const onSubmit = async (formValues) => {
    if (DocumentTypeEditData && DocumentTypeEditData.id) {
      // If editing, include the id in the form values
      formValues.id = DocumentTypeEditData.id;
    }

    await dispatch(createDocumentType(formValues));
  };

  useEffect(() => {
    if (Object.keys(DocumentTypeErrors).length === 0 && DrawerStatus === 0) {
      dispatch(
        getDocumentType({
          page: tablePagination.current,
          perPage: tablePagination.pageSize,
          filter: filter,
          sorting: sorting,
        })
      );
    }
  }, [DocumentTypeErrors]);
  useEffect(() => {
    dispatch(getDocumentCategory());
    DocumentTypeEditData.is_active === 0
      ? setSwitchValue()
      : setSwitchValue("checked");
    DocumentTypeEditData.allow_multiple === 1
      ? setSwitchAllowMultipleValue("checked")
      : setSwitchAllowMultipleValue();
    DocumentTypeEditData.is_required === 1
      ? setRequiredSwitchValue("checked")
      : setRequiredSwitchValue();
  }, [dispatch]);
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };
  const getSwitchValue = (checked) => {
    setSwitchValue(checked);
  };
  const getRequiredSwitchValue = (checked) => {
    setRequiredSwitchValue(checked);
  };
  const getAllowMultipleSwitchValue = (checked) => {
    setSwitchAllowMultipleValue(checked);
  };

  return (
    <>
      <Drawer
        title={DocumentTypeButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={DocumentTypeAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            document_type: DocumentTypeEditData?.document_type,
            document_category_id: DocumentTypeEditData?.document_category_id,
            allow_multiple: DocumentTypeEditData?.allow_multiple,
            is_required: DocumentTypeEditData?.is_required,
            is_active: DocumentTypeEditData && DocumentTypeEditData.is_active === 1 ? DocumentTypeEditData.is_active : 1,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="document_type"
                label={setLocale("name")}
                rules={[
                  {
                    required: true,
                    message: setLocale("nameError"),
                  },
                ]}
                validateStatus={
                  DocumentTypeShowMessage && DocumentTypeErrors.document_type
                    ? "error"
                    : ""
                }
                help={
                  DocumentTypeShowMessage && DocumentTypeErrors.document_type
                }
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="document_category_id"
                label="Document Category"
                rules={[
                  {
                    required: true,
                    message: "Document Category is required",
                  },
                ]}
                validateStatus={
                  DocumentTypeShowMessage &&
                    DocumentTypeErrors.document_category_id
                    ? "error"
                    : ""
                }
                extra={
                  DocumentTypeShowMessage &&
                  DocumentTypeErrors.document_category_id
                }
              >
                <Select
                  className="rounded-0"
                  showSearch
                  optionLabelProp="label"
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "").toLowerCase().includes(input)
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  options={
                    DocumentCategoryResult ? DocumentCategoryResult : null
                  }
                />
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item
                name="is_active"
                // valuePropName={switchValue}
                label="Active"
                rules={[
                  {
                    required: false,
                    message: "Status Error",
                  },
                ]}
                validateStatus={
                  DocumentTypeShowMessage && DocumentTypeErrors.is_active
                    ? "error"
                    : ""
                }
                extra={DocumentTypeShowMessage && DocumentTypeErrors.is_active}
              >
                <Switch
                  checkedChildren={<CheckOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  onChange={getSwitchValue}
                  checked={switchValue}
                  name="is_active"
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item
                name="is_required"
                // valuePropName={switchValue}
                label="Required"
                rules={[
                  {
                    required: false,
                    message: "Required Error",
                  },
                ]}
                validateStatus={
                  DocumentTypeShowMessage && DocumentTypeErrors.is_required
                    ? "error"
                    : ""
                }
                extra={
                  DocumentTypeShowMessage && DocumentTypeErrors.is_required
                }
              >
                <Switch
                  checkedChildren={<CheckOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  onChange={getRequiredSwitchValue}
                  checked={requiredSwitchValue}
                  name="is_required"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="allow_multiple"
                // valuePropName={switchValue}
                label="Allow Multiple"
                rules={[
                  {
                    required: false,
                    message: "Status Error",
                  },
                ]}
                validateStatus={
                  DocumentTypeShowMessage && DocumentTypeErrors.allow_multiple
                    ? "error"
                    : ""
                }
                extra={
                  DocumentTypeShowMessage && DocumentTypeErrors.allow_multiple
                }
              >
                <Switch
                  checkedChildren="Yes"
                  unCheckedChildren="No"
                  onChange={getAllowMultipleSwitchValue}
                  checked={switchAllowMultipleValue}
                  name="allow_multiple"
                />
              </Form.Item>
            </Col>
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={DocumentTypeButtonSpinner}
            >
              {DocumentTypeButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale("Cancel")}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;

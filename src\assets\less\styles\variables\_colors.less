@blue-base:          #3e79f7;
@purple-base:        #a461d8;
@cyan-base:          #04d182;
@green-base:         #21B573;
@magenta-base:       #eb2f96;
@pink-base:          #eb2f96;
@red-base:           #de4436;
@orange-base:        #fa8c16;
@yellow-base:        #fadb14;
@volcano-base:       #ff6b72;
@geekblue-base:      #17bcff;
@lime-base:          #a0d911;
@gold-base:          #ffc542;

@primary-color:       @blue-base; 
@info-color:          @primary-color;
@success-color:       @cyan-base;
@processing-color:    @blue-base;
@error-color:         @volcano-base;
@highlight-color:     @volcano-base;
@warning-color:       @gold-base;
@normal-color:       #fafafb;
@white:              #fff;
@black:              #000;

@gray-dark:                 #1a3353;
@gray:                      #455560;
@gray-light:                #72849a;
@gray-lighter:              #ededed;
@gray-lightest:             #f7f7f8;

@colors-palette: {
	blue:     	  @blue-base;
	purple:       @purple-base;
	cyan:     	  @cyan-base;
	green:        @green-base;
	magenta:	  @magenta-base;
	pink:         @pink-base;
	red:          @red-base;
	orange:       @orange-base;
	yellow:       @yellow-base;
	volcano:      @volcano-base;
	geekblue:     @geekblue-base;
	lime:         @lime-base;
	gold:         @gold-base;
}

@theme-colors: {
	primary:        @primary-color;
	secondary:      @purple-base;
	success:        @success-color;
	info:           @info-color;
	warning:        @warning-color;
    danger:         @error-color;
    gray:           @gray;
	gray-light:     @gray-light;
	gray-lighter:   @gray-lighter;
	gray-lightest:  @gray-lightest;
    dark:           @gray-dark;
};


import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { message,Table,Badge,Typography,Row,Space, Col, Button  } from 'antd';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import IntlMessage from 'components/util-components/IntlMessage';
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { Text } = Typography;

const AttendanceReportSummaryView = (props) => {
    const { data,reportName } = props;
    const [filteredData, setFilteredData] = useState(data);

    const [messageApi, contextHolder] = message.useMessage();
    const success = (msg) => {
        messageApi.open({
            type: 'error',
            content: msg,
            style: { marginTop: '90vh',  },
        });
    };
    
    const exportToExcel = () => {
        if(!filteredData.length){
            success('Nothing to export!')
            return;
        }
        const keys = Object.keys(filteredData[0]); // Extract keys dynamically
        // Exclude columns 'student_id', 
        const filteredKeys = keys.filter(
            (key) =>
            key !== 'student_id'
        );
        
        const transformedData = [
            [reportName], 
            filteredKeys, // Include the header row without excluded columns
            ...filteredData.map((item) =>
            Object.values(item).filter(
                (_, index) =>
                keys[index] !== 'student_id'
            )
            ) // Extract values dynamically and skip excluded columns
        ];
    
        const ws = XLSX.utils.aoa_to_sheet(transformedData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        saveAs(blob, `${reportName}.xlsx`);
    };

    // Step 1: Get distinct values for  columns
    const distinctClasses = [...new Set(data.map(record => record.class_name))];
    const distinctAttStudents = [...new Set(data.map(record => record.student_name))];

    // Step 2: Generate filter options dynamically
    const filterOptions = distinctClasses.map(classValue => ({
        text: classValue,
        value: classValue,
    }));
    const filterOptionsForStudents = distinctAttStudents.map(value => ({
        text: value,
        value: value,
    }));

    const columns = [
        {
          title: setLocale('attendance_report.student_name'),
          dataIndex: 'student_name',
          key: 'student_name',
          filters: filterOptionsForStudents,
              onFilter: (value, record) => record.student_name.startsWith(value),
              filterSearch: true, 
              sorter: (a, b) => a.student_name.localeCompare(b.student_name),
        },
        {
            title: setLocale('classes.label'),
            dataIndex: 'class_name',
            key: 'class_name',
              filters: filterOptions,
              onFilter: (value, record) => record.class_name.startsWith(value),
              filterSearch: true,
        },
        {
            title: setLocale('attendance_report.present'),
            dataIndex: 'Present',
            key: 'Present',
        },
        {
            title: setLocale('attendance_report.unexcused_absence'),
            dataIndex: 'Unexcused_Absence',
            key:'Unexcused_Absence'
        },
        {
            title: setLocale('attendance_report.excused_absence'),
            dataIndex: 'Excused_Absence',
            key:'Excused_Absence'
        },
        {
            title: setLocale('attendance_report.late'),
            dataIndex: 'Late',
            key:'Late'
        },
        {
            title: setLocale('attendance_report.unspecified'),
            dataIndex: 'Unspecified',
            key:'Unspecified'
        },
        
    ];
    const handleTableChange = (pagination, filters, sorter, extra) => {
        // Update filteredData whenever the table's filters change
        setFilteredData(extra.currentDataSource);
      };
  return(
        <>
            {contextHolder}
            <Row gutter={4} className='my-2'>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                    <Space wrap>
                    <h4>{ reportName }</h4>
                    </Space>
                </Col>
                <Col xs={24} sm={24} md={12} lg={12} xl={12} style={{textAlign: "right"}}>
                    <Button onClick={exportToExcel}>{setLocale('attendance_report.export_to_excel')}</Button>                    
                </Col>
            </Row>
            <Table 
                dataSource={data} 
                columns={columns} 
                bordered 
                pagination={false} 
                onChange={handleTableChange}
            />
        </>
  )
};

export default AttendanceReportSummaryView;

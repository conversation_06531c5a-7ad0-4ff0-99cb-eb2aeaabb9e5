import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Row, Col, DatePicker, Card, Select, Form } from 'antd';
import {
    updateSortFilters,
    invoiceDashboardData
} from "store/slices/Invoices/manageInvoicesSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { getGradeLevel } from "store/slices/GradeLevel/manageGradeLevelSlice";
import moment from 'moment';
import { GRADE_LEVEL, INVOICES } from "constants/AppConstants";
import { USER_INFORMATION, DEFAULT_YEAR } from 'constants/AuthConstant';
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const schoolYear = JSON.parse(localStorage.getItem(DEFAULT_YEAR));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { RangePicker } = DatePicker;

function Filters() {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const { GradeLevelResult } = useSelector((state) => state[GRADE_LEVEL]);
    const { filter } = useSelector((state) => state[INVOICES]);
    const [organization, setOrganization] = useState(selectedOrganization);

    const options = [
        {
            value: 'all',
            label: 'All',
        },
        ...userOrganizations?.map(organization => ({
            value: organization.id,
            label: organization.org_name,
        })),
    ];

    const fetchData = async () => {
        try {
            const start = moment(schoolYear.start_date);
            const end = moment(schoolYear.end_date);

            form.setFieldsValue({
                dateRange: [start, end],
            });
            dispatch(updateSortFilters({
                ...filter,
                date_range: [start, end],
                grade_level: '',
                grade_name: '',
                organization_id: 'all'
            }));
            await dispatch(getGradeLevel({ perPage: 1000 }));
        } catch (error) {
            console.error('Error fetching school year data:', error);
        }
    };

    useEffect(() => {
        fetchData()
    }, []);

    useEffect(() => {
        dispatch(invoiceDashboardData(filter));
    }, [filter]);

    const onChangeOrganization = (value) => {
        setOrganization(value);
        dispatch(getGradeLevel({ filter: { organization_id: value }, perPage: 1000 }));
        dispatch(updateSortFilters({
            ...filter,
            organization_id: value
        }));
    }

    const handleChangeDate = (date) => {
        dispatch(
            updateSortFilters({
                ...filter,
                date_range: date,

            })
        );
    };


    return (
        <>
            <Card className="">
                <section className="pl-3 pr-3">
                    <Row gutter={16}>
                        <Col xs={24} sm={24} md={12} lg={24} xl={24} className="d-flex justify-content-between">
                            <Form
                                layout="horizontal"
                                form={form}
                                autoComplete="off"
                                style={{ width: '100%' }}
                            >
                                <div style={{ width: '100%', height: "50px" }}>
                                    <Row gutter={16} className="mt-1">
                                        <Col className="mt-2">
                                            <b>Filters:</b>
                                        </Col>

                                        {userOrganizations.length > 1 &&
                                            <>
                                                <Col xs={24} sm={24} md={5} lg={5} xl={5} >
                                                    <Form.Item
                                                        name="organization_id"
                                                        rules={[
                                                            {
                                                                required: false,
                                                                message: setLocale('Please Select Class'),
                                                            },
                                                        ]}
                                                    >
                                                        <Select
                                                            placeholder={setLocale('organizations.label')}
                                                            optionLabelProp="label"
                                                            defaultValue={filter.organization_id ? filter.organization_id : selectedOrganization}
                                                            onChange={onChangeOrganization}
                                                            options={options ?? []}
                                                            style={{ width: '100%', marginBottom: '6px', textAlign: 'left' }}
                                                        />
                                                    </Form.Item>
                                                </Col>
                                            </>
                                        }

                                        <Col xs={24} sm={24} md={6} lg={6} xl={6} className="px-2">
                                            <Form.Item
                                                name="dateRange"
                                            >
                                                <RangePicker onChange={handleChangeDate} className="w-100" />
                                            </Form.Item>
                                        </Col>

                                        <Col xs={24} sm={24} md={3} lg={3} xl={3}>
                                            <Form.Item
                                                name="grade_level_id"
                                            >
                                                <Select className='rounded-0' showSearch optionLabelProp="label"
                                                    allowClear
                                                    style={{ width: '100%' }}
                                                    onChange={(value, rec) => {
                                                        dispatch(updateSortFilters({
                                                            ...filter,
                                                            grade_level: value ?? '',
                                                            grade_name: rec?.grade_level ?? '',
                                                            organization_id: organization
                                                        }))
                                                    }}
                                                    placeholder={setLocale("invoices.school_grade")}
                                                    optionFilterProp="children"
                                                    value={filter?.grade_level}
                                                    filterOption={(input, option) => (
                                                        (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                                                    )}
                                                    options={GradeLevelResult?.data ?? []}
                                                />
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </div>
                            </Form>
                        </Col>
                    </Row>
                </section>
            </Card>
        </>
    );
}

export default Filters;

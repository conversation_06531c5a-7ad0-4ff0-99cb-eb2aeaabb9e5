import React from 'react';
import { <PERSON>, <PERSON><PERSON>,Col, Row, Card, Progress, Table } from 'antd';
import { RightOutlined, DownloadOutlined } from '@ant-design/icons';
import Chart from "react-apexcharts";
import {Bar} from 'react-chartjs-2';
import { COLORS } from 'constants/ChartConstant';

const { Option } = Select;


function StudentGrades(props) {

  const state = {
    series: [{
      name: 'median',
      data: [31, 40, 28, 51, 42, 109, 100]
    }, {
      name: 'passed',
      data: [11, 32, 45, 32, 34, 52, 41]
    }],
    options: {
      dataLabels: {
        enabled: false
      },
      colors: [ '#7234F5', '#C94941'],
      stroke: {
        curve: 'smooth'
      },
    },
  };

  const data = {
    labels: ['English', 'Science', 'Geography', 'Ethics', 'Math', 'French'],
    datasets: [
      {
        label: 'This Year',
        data: [65, 59, 80, 81, 56, 55],
        backgroundColor: "#8ABBFF",
        borderWidth: 0
      },
      {
        label: 'Median',
        data: [28, 48, 40, 19, 86, 27],
        backgroundColor: "#1177FF",
        borderWidth: 0
      }
    ]
  };

  const options = {
    caleShowVerticalLines: false,
    responsive: true,
    scales: {
      xAxes: [{
        categoryPercentage: 0.45,
        barPercentage: 0.70,
        display: true,
        scaleLabel: {
          display: false,
          labelString: 'Subject'
        },
        gridLines: false,
        ticks: {
          display: true,
          beginAtZero: true,
          fontSize: 13,
          padding: 10
        }
      }],
      yAxes: [{
        categoryPercentage: 0.35,
        barPercentage: 0.70,
        display: true,
        scaleLabel: {
          display: false,
          labelString: 'Value'
        },
        gridLines: {
          drawBorder: false,
          offsetGridLines: false,
          drawTicks: false,
          borderDash: [3, 4],
          zeroLineWidth: 1,
          zeroLineBorderDash: [3, 4]
        },
        ticks: {
          max: 100,                            
          stepSize: 20,
          display: true,
          beginAtZero: true,
          fontSize: 13,
          padding: 10
        }
      }]
    }
  }

  const dataSource = [
    {
      key: '1',
      name: 'Mathematics',
      median: 32,
      obtained: 64,
    },
    {
      key: '1',
      name: 'English',
      median: 60,
      obtained: 90,
    },
    {
      key: '1',
      name: 'Geography',
      median: 75,
      obtained: 85,
    },
    {
      key: '1',
      name: 'French',
      median: 70,
      obtained: 80,
    },
    {
      key: '1',
      name: 'Science',
      median: 60,
      obtained: 72,
    },
  ];
  
  const columns = [
    {
      title: '',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Median Marks',
      dataIndex: 'median',
      key: 'median',
    },
    {
      title: 'Obtained Marks',
      dataIndex: 'obtained',
      key: 'obtained',
    },
  ];

  return (
    <>
          <Row gutter={16} className='my-3 bg-white pt-4 pb-4' style={{borderRadius: 15}}>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <div className='grade-book-select'>
            <h5 className='my-2 mx-2'>Filter by: </h5> 
              <Select
                className='mx-2'
                defaultValue={{ label: <>Overall</> }}
                optionLabelProp="label"
              >
                <Option value="overall1" label={<>overall1</>}></Option>
                <Option value="overall2" label={<>overall2</>}></Option>
                <Option value="overall3" label={<>overall3</>}></Option>
              </Select>
              <Select
                className='mx-2'
                defaultValue={{ label: <>Average %</> }}
                optionLabelProp="label"
              >
                <Option value="50%" label={<>50%</>}></Option>
                <Option value="75%" label={<>75%</>}></Option>
                <Option value="100%" label={<>100%</>}></Option>
              </Select>
            </div>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <div style={{display: "flex", justifyContent: "end"}}>
              <Button className='mx-2' size='small' >
              <DownloadOutlined /> Export CSV
              </Button>
              </div>
            </Col>
          </Row>
          <Row gutter={16} className='mx-2'>
            <Col span={24}>
              <Card className='grade-top-card'>
              <h2 className='mt-2'>Tayyab</h2>
              <div className='d-flex justify-content-between mt-4'>
                <h5>Grade 7</h5>
                <h5>Go to Gradebook <RightOutlined /></h5>
              </div>
              </Card>
            </Col>
          </Row>
          <Row gutter={16} className='mx-2'>
            <Col xs={24} sm={24} md={16} lg={16} xl={16}>
              <Card className='p-3'>
                <Chart
                    options={state.options}
                    series={state.series}
                    type="area"
                    height= {300}
                  />
              </Card>
              <Card className='p-3'>
              <div>
                <Bar
                  data={data}
                  options={options}
                />
              </div>
              </Card>
              <Card className='p-3'>
                <div className='d-flex justify-content-between'>
                  <h3>Subject</h3>
                  <h3>Marks</h3>
                  <Select
                    className='mx-2'
                    defaultValue={{ label: <>Select Term</> }}
                    optionLabelProp="label"
                  >
                    <Option value="term1" label={<>Term 1</>}></Option>
                    <Option value="term2" label={<>Term 2</>}></Option>
                    <Option value="term3" label={<>Term 3</>}></Option>
                  </Select>
                </div>
                <Table dataSource={dataSource} columns={columns} />;
              </Card>
            </Col>
            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
            <Card className='p-3'>
              <div className='d-flex justify-content-between'>
              <h2>Overall Progress</h2>
              <Select
                className='mx-2'
                defaultValue={{ label: <>Select Term</> }}
                optionLabelProp="label"
              >
                <Option value="term1" label={<>Term 1</>}></Option>
                <Option value="term2" label={<>Term 2</>}></Option>
                <Option value="term3" label={<>Term 3</>}></Option>
              </Select>
              </div>
              <div className='d-flex justify-content-center grade-progress'>
                <Progress 
                  type="circle" 
                  size="large"
                  strokeWidth={12}
                  className='mt-5 mb-4'
                  strokeColor={ "#CEF6C1"} percent={70} />
              </div>
            </Card>
            <Card className='grade-strength p-3' style={{background: "#4F88D3"}}>
              <h3>Strengths</h3>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
            </Card>
            <Card className='grade-strength p-3' style={{background:"#C84343"}}>
              <h3>Weakness</h3>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
              <div className='d-flex justify-content-between'>
                <h5>Science</h5>
                <span>Lorem Ipsum</span>
              </div>
            </Card>
            </Col>
          </Row>
    </>
  )
}

export default StudentGrades

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Drawer,
  Form,
  Input,
  message,
  Row,
  Space,
  Upload,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { EMPLOYEE_LEAVE } from "constants/AppConstants";
import {
  EmployeeLeaveAddDrawerStatus,
  createEmployeeLeave,
  getEmployeeLeave,
  onCloseError,
} from "store/slices/EmployeeLeave/manageEmployeeLeaveSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { LeaveType } from "../components/ui/Select/LeaveType";
import { DownOutlined, InboxOutlined } from "@ant-design/icons";
import Dragger from "antd/lib/upload/Dragger";
import moment from "moment";

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const allowedFileTypes = [
  "image/jpg",
  "image/jpeg",
  "image/png",
  "application/pdf",
];

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [file, setFile] = useState(null);


  const {
    EmployeeLeaveAddDrawer,
    EmployeeLeaveButtonAndModelLabel,
    sorting,
    filter,
    EmployeeLeaveErrors,
    EmployeeLeaveShowMessage,
    EmployeeLeaveButtonSpinner,
    EmployeeLeaveEditData,
    tablePagination,
    DrawerStatus,
  } = useSelector((state) => state[EMPLOYEE_LEAVE]);
    const [selectedLeaveType, setSelectedLeaveType] = useState(
    EmployeeLeaveEditData?.leave_type_id || null
  );
  const onClose = () => {
    dispatch(EmployeeLeaveAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  };

  // File validation and preview logic
  const beforeUpload = (file) => {
    const isAllowedType = allowedFileTypes.includes(file.type);
    if (!isAllowedType) {
      message.error("Only JPG, JPEG, PNG, or PDF files are allowed!");
      return Upload.LIST_IGNORE;
    }
    const isLt3M = file.size / 1024 / 1024 < 3;
    if (!isLt3M) {
      message.error("File must be smaller than 3MB!");
      return Upload.LIST_IGNORE;
    }
    setFile(file); // Store file in state
    return false; // Prevent auto upload
  };

  const onRemove = () => {
    setFile(null);
  };

  const onSubmit = async (formValues) => {
    const formattedValues = {
      description: formValues.reason || "",
      from: formValues.from.format("YYYY-MM-DD"),
      to: formValues.to.format("YYYY-MM-DD"),
      leave_type_id: formValues.leave_type_id,
      status: EmployeeLeaveEditData?.status || "pending",
    };
    if (EmployeeLeaveEditData && EmployeeLeaveEditData.id) {
      formattedValues.id = EmployeeLeaveEditData.id;
    }
    const formData = new FormData();
    Object.entries(formattedValues).forEach(([key, value]) => {
      formData.append(key, value);
    });
    if (file) {
      formData.append("documents", file);
    }

    await dispatch(createEmployeeLeave(formData));
  };

  useEffect(() => {
    if (Object.keys(EmployeeLeaveErrors).length === 0 && DrawerStatus === 0) {
      dispatch(
        getEmployeeLeave({
          page: tablePagination.current,
          perPage: tablePagination.pageSize,
          filter: filter,
          sorting: sorting,
        })
      );
    }
  }, [EmployeeLeaveErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };
  const dateFormat = "YYYY-MM-DD";
  const draggerProps = {
    name: "file",
    multiple: false,
    beforeUpload,
    onRemove,
    fileList: file ? [file] : [],
    showUploadList: true,
  };

  // Function to disable past dates except for leave type 2
  const disabledDate = (current) => {
    // If leave type is 2, allow all dates (no restrictions)
    if (selectedLeaveType === 2) {
      return false;
    }
    // For other leave types, disable dates before today
    return current && current < moment().startOf('day');
  };

  // Handle leave type change
  const handleLeaveTypeChange = (value) => {
    setSelectedLeaveType(value);
  };

  return (
    <>
      <Drawer
        title={EmployeeLeaveButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={EmployeeLeaveAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            leave_type_id: EmployeeLeaveEditData?.leave_type_id || undefined,
            from: EmployeeLeaveEditData?.from
              ? moment(EmployeeLeaveEditData.from)
              : null,
            to: EmployeeLeaveEditData?.to
              ? moment(EmployeeLeaveEditData.to)
              : null,
            reason: EmployeeLeaveEditData?.description || "",
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="leave_type_id"
                disabled={true}
                label={setLocale("Leave Type")}
                rules={[
                  {
                    required: true,
                    message: setLocale("Please select leave type"),
                  },
                ]}
              >
                <LeaveType 
                  disabled={EmployeeLeaveEditData.length} 
                  onChange={handleLeaveTypeChange}
                />
              </Form.Item>
              {/* <Form.Item
                name="name"
                label={setLocale("name")}
                rules={[
                  {
                    required: true,
                    message: setLocale("nameError"),
                  },
                ]}
                validateStatus={
                  EmployeeLeaveShowMessage && EmployeeLeaveErrors.name
                    ? "error"
                    : ""
                }
                help={EmployeeLeaveShowMessage && EmployeeLeaveErrors.name}
              >
                <Input />
              </Form.Item> */}
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="from"
                label="Start Date"
                rules={[
                  { required: true, message: "Please select start date" },
                ]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  format={dateFormat}
                  suffixIcon={<DownOutlined />}
                  disabledDate={disabledDate}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="to"
                label="End Date"
                rules={[{ required: true, message: "Please select end date" }]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  format={dateFormat}
                  suffixIcon={<DownOutlined />}
                  disabledDate={disabledDate}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="reason"
            label="Reason for Leave"
            rules={[
              { required: true, message: "Please enter reason for leave" },
            ]}
          >
            <Input.TextArea rows={2} placeholder="Describe" />
          </Form.Item>
          <Form.Item label="Upload Document">
            <Dragger {...draggerProps}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">Drag and Drop</p>
              <p className="ant-upload-hint">JPG, PNG, PDF (max. 3MB)</p>
            </Dragger>
            {EmployeeLeaveEditData?.documents && EmployeeLeaveEditData?.documents != "null" &&(
              <div style={{ marginTop: 8 }}>
                <b>Attachment:</b>{" "}
                <a
                  href={EmployeeLeaveEditData.documents}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ wordBreak: "break-all" }}
                >
                  {EmployeeLeaveEditData.documents.split("/").pop()}
                </a>
              </div>
            )}
          </Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={EmployeeLeaveButtonSpinner}
            >
              {EmployeeLeaveButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale("Cancel")}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};

export default Index;

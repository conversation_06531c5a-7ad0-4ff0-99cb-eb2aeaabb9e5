import { Select } from "antd";
import React from "react";
const STATUS_OPTIONS = [
  { value: "Approved", label: "Approved" },
  { value: "Rejected", label: "Rejected" },
  { value: "Pending", label: "Pending" },
];
export const Status = ({ onChange }) => {
  const { Option } = Select;
  return (
    <Select placeholder="Status" style={{ width: 120 }} onChange={onChange}>
      {STATUS_OPTIONS.map((status) => (
        <Option key={status.value} value={status.value}>
          {status.label}
        </Option>
      ))}{" "}
    </Select>
  );
};

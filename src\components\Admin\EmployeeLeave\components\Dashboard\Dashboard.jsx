import { Calendar, Card, Col, Row, Skeleton, Statistic } from "antd";
import { EMPLOYEE_LEAVE } from "constants/AppConstants";
import React, { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getEmployeeLeaveBalance,
  getUpcomingLeaves,
} from "store/slices/EmployeeLeave/manageEmployeeLeaveSlice";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";
import "./Dashboard.css";

export const Dashboard = ({ setView }) => {
  function onPanelChange(value, mode) {
    console.log(value, mode);
  }
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    EmployeeLeaveBalance,
    EmployeeLeaveBalanceLoading,
    UpcomingLeaves,
  } = useSelector((state) => state[EMPLOYEE_LEAVE]);
  useEffect(() => {
    dispatch(getEmployeeLeaveBalance());
    dispatch(getUpcomingLeaves());
  }, [dispatch]);
  const markedDates = useMemo(() => {
    const allDates = new Set();
    if (Array.isArray(UpcomingLeaves?.data) && UpcomingLeaves.data.length > 0) {
      UpcomingLeaves.data.forEach((leave) => {
        let current = dayjs(leave.from);
        const end = dayjs(leave.to);
        while (current.isBefore(end) || current.isSame(end, "day")) {
          allDates.add(current.format("YYYY-MM-DD"));
          current = current.add(1, "day");
        }
      });
    }
    return allDates;
  }, [UpcomingLeaves]);
  const dateCellRender = (value) => {
    const formattedDate = value.format("YYYY-MM-DD");
    if (markedDates.has(formattedDate)) {
      return (
        <div className="leave-date-marker">
          {value.date()}
        </div>
      );
    }
    return null;
  };
  return (
    <>
      <Row gutter={16}>
        {EmployeeLeaveBalanceLoading
          ? // Show 5 skeleton cards as a placeholder
            Array.from({ length: 5 }).map((_, idx) => (
              <Col xs={24} sm={24} md={4} key={idx}>
                <Card>
                  <Skeleton active paragraph={false} />
                </Card>
              </Col>
            ))
          : Array.isArray(EmployeeLeaveBalance?.data) &&
            EmployeeLeaveBalance?.data.map((leave) => (
              <Col xs={24} sm={24} md={4} key={leave.leave_type_id}>
                <Card
                className="cursor-pointer"
                  hoverable
                  onClick={() => {
                    navigate(`?leave_type=${leave.leave_type_id}`);
                    setView && setView("table"); // Set view to table if function provided
                  }}
                >
                  <Statistic
                    title={leave.leave_type_name + " Leaves"}
                    value={leave.balance}
                    className="font-weight-semibold"
                  />
                </Card>
              </Col>
            ))}
      </Row>
      <div>
        <h4>Upcoming Leaves :</h4>
      </div>
      <Row gutter={16} className="mt-4">
        <Col xs={24} sm={24} md={12} lg={12} xl={10}>
          <Card title="">
            <Calendar
              fullscreen={false}
              onPanelChange={onPanelChange}
              dateCellRender={dateCellRender}
            />
          </Card>
        </Col>
      </Row>
    </>
  );
};

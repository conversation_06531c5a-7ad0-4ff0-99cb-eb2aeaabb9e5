import React, { useState, useRef } from "react";
import { useSelector } from "react-redux";
import {
    Row,
    Col,
    Card,
    Skeleton,
    Modal,
    Table,
    Input,
    Space,
    Button
} from "antd";
import { INVOICES } from "constants/AppConstants";
import { SearchOutlined } from "@ant-design/icons";
import <PERSON><PERSON><PERSON> from "react-highlight-words";
import moment from "moment";

function InvoiceAndPaymentStates() {
    const { dashboardData, dashboardDataLoading } = useSelector((state) => state[INVOICES]);

    // ── Modal state ─────────────────────────────────────────────────────────────
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState(null); // "overdue" | "receivable"

    // ── Column-search state ────────────────────────────────────────────────────
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const searchInput = useRef(null);

    // common helper for per-column search
    const getColumnSearchProps = (dataIndexOrIndices) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder="Filter..."
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => {
                        confirm();
                        setSearchText(selectedKeys[0]);
                        setSearchedColumn(
                            Array.isArray(dataIndexOrIndices)
                                ? dataIndexOrIndices.join(",")
                                : dataIndexOrIndices
                        );
                    }}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => {
                            confirm();
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(
                                Array.isArray(dataIndexOrIndices)
                                    ? dataIndexOrIndices.join(",")
                                    : dataIndexOrIndices
                            );
                        }}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters();
                            setSearchText("");
                            setSearchedColumn("");
                            confirm();
                        }}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) => {
            const keys = Array.isArray(dataIndexOrIndices)
                ? dataIndexOrIndices
                : [dataIndexOrIndices];
            return keys.some((key) => {
                const cell = record[key];
                return (
                    cell != null &&
                    cell.toString().toLowerCase().includes(value.toLowerCase())
                );
            });
        },
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text) =>
            (Array.isArray(dataIndexOrIndices)
                ? dataIndexOrIndices.includes(searchedColumn)
                : searchedColumn === dataIndexOrIndices) ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text?.toString() || ""}
                />
            ) : (
                text
            ),
    });

    // ── Column definitions ─────────────────────────────────────────────────────
    const columns = [
        {
            title: "Student",
            dataIndex: "student_id",
            key: "student_id",
            ...getColumnSearchProps(["full_name", "student_id"]),
            render: (student_id, record) =>
                `${record.full_name} (${student_id})`,
            sorter: (a, b) => a.student_id.localeCompare(b.student_id),
        },
        {
            title: "Grade Level",
            dataIndex: "grade_level",
            key: "grade_level",
            ...getColumnSearchProps("grade_level"),
            sorter: (a, b) => +a.grade_level - +b.grade_level,
        },
        {
            title: "Batch #",
            dataIndex: "batch_no",
            key: "batch_no",
            ...getColumnSearchProps("batch_no"),
            sorter: (a, b) => a.batch_no.localeCompare(b.batch_no),
        },
        {
            title: "Invoice #",
            dataIndex: "invoice_no",
            key: "invoice_no",
            ...getColumnSearchProps("invoice_no"),
            sorter: (a, b) => a.invoice_no.localeCompare(b.invoice_no),
        },
        {
            title: "Due Date",
            dataIndex: "due_date",
            key: "due_date",
            ...getColumnSearchProps("due_date"),
            sorter: (a, b) => new Date(a.due_date) - new Date(b.due_date),
            render: (d) => moment(d).format("YYYY-MM-DD"),
        },
        {
            title: "Status",
            dataIndex: "invoice_status_code",
            key: "invoice_status_code",
            filters: [
                { text: "Paid", value: "Paid" },
                { text: "Pending", value: "Pending" },
                { text: "Partially Paid", value: "Partially Paid" },
            ],
            onFilter: (value, record) => record.invoice_status_code === value,
            sorter: (a, b) =>
                a.invoice_status_code.localeCompare(b.invoice_status_code),
        },
    ];

    // ── When a card is clicked…──────────────────────────────────────────────────
    const openModal = (type) => {
        setModalType(type);
        setModalVisible(true);
    };

    // ── Filter out the right subset ────────────────────────────────────────────
    let modalData = [];
    if (modalType === "overdue") {
        modalData = dashboardData?.overDueInvoices?.data ?? [];
    } else if (modalType === "receivable") {
        modalData = dashboardData?.receivableInvoices?.data ?? [];
    }

    return (
        <>
            <Row gutter={16} className="invoice-dashboard d-flex justify-content-between gap-2">
                <Col xs={24} sm={24} md={4} lg={4} xl={4} className="w-full">
                    <Card className="p-3">
                        <div className="">
                            <h3 className="">Total Revenue</h3>
                            <span className="divided divide-4"></span>
                            {dashboardDataLoading ? (
                                <Skeleton.Input loading={true} shape="round" active size="small" />
                            ) : (
                                <h1> $ {dashboardData?.invoicesDueDateCount?.totalRevenue ?? 0} </h1>
                            )}
                        </div>
                    </Card>
                </Col>

                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                    <Card className="p-3">
                        <Row>
                            <Col xs={24} sm={24} md={14} lg={14} xl={14}>
                                <div className="">
                                    <h3 className="">Total Payments</h3>
                                    <span className="divided divide-1"></span>
                                    {dashboardDataLoading ? (
                                        <Skeleton.Input loading={true} shape="round" active size="small" />
                                    ) : (
                                        <h1> $ {dashboardData?.invoicesDueDateCount?.totalPayment ?? 0} </h1>
                                    )}
                                </div>
                            </Col>
                            <Col xs={24} sm={24} md={10} lg={10} xl={10}>
                                <div className="">
                                    <div className="compare-percent font-size-xs">Applied Payments</div>
                                    {dashboardDataLoading ? (
                                        <Skeleton.Input loading={true} shape="round" active size="small" />
                                    ) : (
                                        '$ ' + dashboardData?.invoicesDueDateCount?.appliedPayment ?? 0
                                    )}
                                    <div className="compare-percent font-size-xs">Unapplied Payments</div>
                                    {dashboardDataLoading ? (
                                        <Skeleton.Input loading={true} shape="round" active size="small" />
                                    ) : (
                                        '$ ' + dashboardData?.invoicesDueDateCount?.unAppliedPayment ?? 0
                                    )}
                                </div>
                            </Col>
                        </Row>
                    </Card>
                </Col>

                <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                    <Card className="p-3">
                        <div className="">
                            <h3 className="">Balance</h3>
                            <span className="divided divide-4"></span>
                            {dashboardDataLoading ? (
                                <Skeleton.Input loading={true} shape="round" active size="small" />
                            ) : (
                                <h1> $ {dashboardData?.invoicesDueDateCount?.balance ?? 0} </h1>
                            )}
                        </div>
                    </Card>
                </Col>

                <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                    <Card className="p-3"
                        hoverable
                        onClick={() => openModal("overdue")}
                    >
                        <div className="">
                            <h3 className="">Overdue Invoices</h3>
                            <span className="divided divide-2"></span>
                            {dashboardDataLoading ? (
                                <Skeleton.Input loading={true} shape="round" active size="small" />
                            ) : (
                                <h1> {dashboardData?.invoicesDueDateCount?.invoicesDueDateCount ?? 0} </h1>
                            )}

                        </div>
                    </Card>
                </Col>
                <Col xs={24} sm={24} md={4} lg={4} xl={4}>
                    <Card className="p-3"
                        hoverable
                        onClick={() => openModal("receivable")}
                    >
                        <div className="">
                            <h3 className="">Receivable Invoices</h3>
                            <span className="divided divide-3"></span>
                            {dashboardDataLoading ? (
                                <Skeleton.Input loading={true} shape="round" active size="small" />
                            ) : (
                                <h1> {dashboardData?.invoicesDueDateCount?.totalPendingInvoicesCount ?? 0} </h1>
                            )}
                        </div>
                    </Card>
                </Col>

            </Row>
            <Modal
                title={
                    modalType === "overdue"
                        ? "Overdue Invoices"
                        : "Receivable Invoices"
                }
                visible={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={null}
                width="80%"
            >
                <Table
                    rowKey="invoice_no"
                    dataSource={modalData}
                    columns={columns}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        pageSizeOptions: ["5", "10", "20", "50"],
                    }}
                />
            </Modal>
        </>
    );
}

export default InvoiceAndPaymentStates;

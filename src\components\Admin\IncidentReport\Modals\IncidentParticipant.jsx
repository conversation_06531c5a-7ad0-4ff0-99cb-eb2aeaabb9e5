import React, { useEffect, useState } from 'react';
import { Button, Col, Form, Input, Row, Space, Table, Tag } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { INCIDENT_REPORT } from "constants/AppConstants";
import {
  IncidentReportAddDrawerStatus,
  createIncidentReport,
  setIncidentParticipantFormStatus,
  getIncidentReport
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import IncidentParticipantForm from "./IncidentParticipantForm";
import IntlMessage from "components/util-components/IntlMessage";
import { PlusOutlined } from '@ant-design/icons';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();


const IncidentParticipant = () => {
  const dispatch = useDispatch();
  const {
    IncidentParticipantFormStatus,
  } = useSelector(
    (state) => state[INCIDENT_REPORT]
  );

  const onClose = () => dispatch(IncidentReportAddDrawerStatus(false));
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <a>{text}</a>,
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a>Delete</a>
        </Space>
      ),
    },
  ];
  const data = [
    {
      key: '1',
      name: 'John Brown',
      age: 32,
      address: 'New York No. 1 Lake Park',
    },
    {
      key: '2',
      name: 'Jim Green',
      age: 42,
      address: 'London No. 1 Lake Park',
    },
    {
      key: '3',
      name: 'Joe Black',
      age: 32,
      address: 'Sydney No. 1 Lake Park',
    },
  ];
  const handleOpenModal = () => dispatch(setIncidentParticipantFormStatus(true));

  return (
    <>
      {IncidentParticipantFormStatus && <IncidentParticipantForm />}
      <Col span={24} style={{ textAlign: "right" }}>
        <Button
          type="success"
          onClick={handleOpenModal}
        >
          <PlusOutlined />
        </Button>
      </Col>
      <Table columns={columns} dataSource={data} />
      <Space>
        <Button onClick={onClose}>{setLocale('Cancel')}</Button>
      </Space>
    </>
  );
};
export default IncidentParticipant;


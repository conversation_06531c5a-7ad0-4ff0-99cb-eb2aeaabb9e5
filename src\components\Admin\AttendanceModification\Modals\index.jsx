import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ATTENDANCE_MODIFICATION } from "constants/AppConstants";
import {
    AttendanceModificationAddDrawerStatus,
    createAttendanceModification,
    getAttendanceModification
  } from "store/slices/AttendanceModification/manageAttendanceModificationSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const { AttendanceModificationAddDrawer, AttendanceModificationButtonAndModelLabel, AttendanceModificationErrors, AttendanceModificationShowMessage, AttendanceModificationButtonSpinner, AttendanceModificationEditData, tablePagination } = useSelector(
        (state) => state[ATTENDANCE_MODIFICATION]
      );
const onClose = () => dispatch(AttendanceModificationAddDrawerStatus(false));

const onSubmit = async (formValues) => {

  if (AttendanceModificationEditData && AttendanceModificationEditData.id) {
    // If editing, include the id in the form values
    formValues.id = AttendanceModificationEditData.id;
  }

  await dispatch(createAttendanceModification(formValues))
    .then(() => {
      if (Object.keys(AttendanceModificationErrors).length == 0) {
        dispatch(getAttendanceModification({
          page: tablePagination.current,
          perPage: tablePagination.pageSize
        }));
        // form.resetFields();
      }
    })
    .catch((error) => {
      // Handle delete error
      console.error("Error deleting module:", error);
    });
};

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={AttendanceModificationButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={AttendanceModificationAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: AttendanceModificationEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={AttendanceModificationShowMessage && AttendanceModificationErrors.name ? "error" : ""}
                help={AttendanceModificationShowMessage && AttendanceModificationErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={AttendanceModificationButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useEffect, useState } from 'react';
import { Select, Button, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ASSESSMENT_LEVEL } from "constants/assessment-levels/index.js";
import {
  AssessmentLevelAddDrawerStatus,
  createAssessmentLevel,
  getAssessmentLevel,
  onCloseError
} from "store/slices/AssessmentLevel/manageAssessmentLevelSlice.js";
import { EditorState, ContentState, convertToRaw, convertFromHTML } from 'draft-js';
import { Editor } from 'react-draft-wysiwyg';
import '../../../../../node_modules/react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import draftToHtml from 'draftjs-to-html';
import { GRADE_LEVEL } from "constants/AppConstants";
import IntlMessage from "components/util-components/IntlMessage";
// import { STUDENT_GRADING } from "constants/student-grading/index";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

// const { TextArea } = Input;
const { Option } = Select

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {
    AssessmentLevelAddDrawer,
    AssessmentLevelButtonAndModelLabel,
    sorting,
    filter,
    AssessmentLevelErrors,
    AssessmentLevelShowMessage,
    AssessmentLevelButtonSpinner,
    AssessmentLevelEditData,
    tablePagination,
    DrawerStatus
  } = useSelector((state) => state[ASSESSMENT_LEVEL]);
  // const {
  //   preLoadGradingData,
  // } = useSelector((state) => state[STUDENT_GRADING])
  const {
    GradeLevelResult
  } = useSelector((state) => state[GRADE_LEVEL]);

  const [editorState, setEditorState] = useState(EditorState.createEmpty());
  const onClose = () => {
    dispatch(AssessmentLevelAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {
    const content = draftToHtml(convertToRaw(editorState.getCurrentContent()));
    const fineContent = content.replace(/("|\r\n|\n|\r)/gm, "");
    formValues.description = fineContent;
    if (AssessmentLevelEditData && AssessmentLevelEditData.id) {
      formValues.id = AssessmentLevelEditData.id;
    }
    await dispatch(createAssessmentLevel(formValues));
  }
  useEffect(() => {
    form.setFieldValue("show_comments", 1);
    form.setFieldValue("category", 'personal');

    if (AssessmentLevelEditData) {
      form.setFieldsValue(AssessmentLevelEditData);
      const blocksFromHTML = convertFromHTML(AssessmentLevelEditData.description || '');
      const contentState = ContentState.createFromBlockArray(
        blocksFromHTML.contentBlocks,
        blocksFromHTML.entityMap
      )
      setEditorState(EditorState.createWithContent(contentState));
    }
  }, [AssessmentLevelEditData])


  useEffect(() => {
    if (Object.keys(AssessmentLevelErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getAssessmentLevel({
        page: tablePagination.current, perPage: tablePagination.pageSize, filter: filter, sorting: sorting
      }));
      setEditorState(EditorState.createEmpty());
      onClose();
    }
  }, [AssessmentLevelErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };
  const handleEditorChange = (newEditorState) => {
    setEditorState(newEditorState);
  }

  return (
    <>

      <Drawer title={AssessmentLevelButtonAndModelLabel} width={window.innerWidth > 800 ? "55%" : window.innerWidth - 100}
        onClose={onClose} open={AssessmentLevelAddDrawer} maskClosable={false} zIndex={1002} bodyStyle={{ paddingBottom: 80, }} >
        <Form layout="vertical" onFinish={onSubmit} form={form} onFinishFailed={onFinishFailed} autoComplete="off" >
          <Row gutter={16}>
            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <Form.Item name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  }]}
                validateStatus={AssessmentLevelShowMessage && AssessmentLevelErrors.name ? "error" : ""}
                help={AssessmentLevelShowMessage && AssessmentLevelErrors.name}>
                <Input />
              </Form.Item>
            </Col>
            {/* <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <Form.Item name="code"
                label={'Code'}
                rules={[
                  {
                    required: true,
                    message: 'Code is required',
                  } ]}
                  validateStatus={AssessmentLevelShowMessage && AssessmentLevelErrors.code ? "error" : ""}
                  help={AssessmentLevelShowMessage && AssessmentLevelErrors.code}>
                <Input />
              </Form.Item>
            </Col> */}
            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <Form.Item className="mx-2" name="grade_level_id" label={setLocale("studentgrading.grade_level")}
                rules={[{ required: true, message: 'Grade level is required' }]}
                validateStatus={AssessmentLevelShowMessage && AssessmentLevelErrors.organization_grade_level_id ? "error" : ""}
                help={AssessmentLevelShowMessage && AssessmentLevelErrors.organization_grade_level_id}>
                <Select optionLabelProp="label" showSearch allowClear optionFilterProp="children"
                  maxTagCount={2}
                  mode="multiple"
                  filterOption={(input, option) => (
                    (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                  )} placeholder={setLocale("studentgrading.grade_level")} >
                  {GradeLevelResult?.data?.map((grade, index) => {
                    return (
                      <Option key={index} value={grade.id} label={grade.grade_level}>
                        {grade.grade_level}
                      </Option>
                    )
                  })
                  }
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <Form.Item className="mx-2" name="show_comments" label={setLocale("Show comments")}
                rules={[{ required: true, message: 'Show comments is required' }]}
                validateStatus={AssessmentLevelShowMessage && AssessmentLevelErrors.show_comments ? "error" : ""}
                help={AssessmentLevelShowMessage && AssessmentLevelErrors.show_comments}>
                <Select optionLabelProp="label" placeholder={setLocale("Show comments")} >                 
                    <Option key={1} value={1} label={'Yes'}> Yes </Option>
                    <Option key={2} value={0} label={'No'}> No </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <Form.Item className="mx-2" name="category" label={setLocale("Category")}
                rules={[{ required: true, message: 'Category is required' }]}
                validateStatus={AssessmentLevelShowMessage && AssessmentLevelErrors.category ? "error" : ""}
                help={AssessmentLevelShowMessage && AssessmentLevelErrors.category}>
                <Select optionLabelProp="label" placeholder={setLocale("Category")} >                 
                    <Option key={1} value={'personal'} label={'Personal'}> Personal </Option>
                    <Option key={2} value={'core'} label={'Core'}> Core </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Form.Item
                name="description"
                label="Description"
                rules={[
                  {
                    required: true,
                    message: 'Description is required',
                  },
                ]}
                validateStatus={AssessmentLevelShowMessage && AssessmentLevelErrors.description ? "error" : ""}
                help={AssessmentLevelShowMessage && AssessmentLevelErrors.description}>
                <Editor
                  editorState={editorState} onEditorStateChange={handleEditorChange}
                  wrapperClassName="wrapper-class"
                  editorStyle={{ height: '200px', borderBottom: '1px solid #4d5b75', borderRight: '1px solid #4d5b75', borderLeft: '1px solid #4d5b75' }}
                  editorClassName="editor-class" toolbarClassName="toolbar-class" />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button type="primary" htmlType="submit" loading={AssessmentLevelButtonSpinner} >
              {AssessmentLevelButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  Modal,
  Popconfirm,
  Pagination,
  Button,
  Input,
  Space,
  Breadcrumb,
  Skeleton,
  Row,
  Col,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  EyeOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { DOCUMENT_STUDENT } from "constants/AppConstants";
import { env } from "configs/EnvironmentConfig"

import AddDocumentStudentModal from "./Modals/index";
import {
  DocumentStudentAddDrawerStatus,
  DocumentStudentEditWithDrawerStatus,
  deleteDocumentStudent,
  getDocumentStudent,
  updateSortFilters,
  setColumnSearch,
  getStudentDocuments,
} from "store/slices/DocumentStudent/manageDocumentStudentSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import OrganizationSelect from "../OrganizationDropdown/OrganizationSelect";
import { Link } from "react-router-dom";
import { USER_INFORMATION } from "constants/AuthConstant";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

function Index() {
  const dispatch = useDispatch();
  const searchInput = useRef(null);
  const handleOpenModal = () =>
    dispatch(DocumentStudentAddDrawerStatus({ errorStatus: 1, status: true }));
  const {
    DocumentStudentAddDrawer,
    DocumentStudentResult,
    tablePagination,
    sorting,
    filter,
    DocumentStudentTableLoading,
    permission,
    getStudentDocumentsData,
    getStudentDocumentsLoader,
  } = useSelector((state) => state[DOCUMENT_STUDENT]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(false);
  const handelModal = (data, record) => {
    setIsModalOpen(data);
    setSelectedRecord(record)
    dispatch(getStudentDocuments({ student_id: record.student_id }));
  };
  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getDocumentStudent({
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
      })
    );
  };

  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, tablePagination.pageSize, filter, sorting);
  };

  const handleModalSearch = async (confirm) => {
    confirm();
    await dispatch(
      getStudentDocuments({
        page: 1,
        perPage: tablePagination.pageSize,
        filter: filter,
        sorting: sorting,
        student_id: selectedRecord.student_id
      })
    );
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      newObject,
      sorting
    );
  };

  const handleModalReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    dispatch(getStudentDocuments({
      page: tablePagination.current,
      perPage: tablePagination.pageSize,
      filter: newObject,
      sorting: sorting,
      student_id: selectedRecord.student_id
    }))
  };


  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === "") {
      confirm();
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        { ...filter, [dataIndex]: value },
        sorting
      );
    }
  };
  const handleModalOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === "") {
      confirm();
      getStudentDocuments({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: { ...filter, [dataIndex]: value },
        sorting: sorting,
        student_id: selectedRecord.student_id
      })
    }
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  const getColumnSearchModalProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleModalOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleModalSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleModalSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleModalReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  useEffect(() => {
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      filter,
      sorting
    );
  }, []);

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, filter, sorting);
  };

  const handleDelete = (record) => {
    dispatch(deleteDocumentStudent(record.id)).then(() => {
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        filter,
        sorting
      );
    });
  };
  const handleView = (record) => {
    console.log(record, 'rexview');
  };
  const handleUpdate = (record) => {
    dispatch(
      DocumentStudentEditWithDrawerStatus({ errorStatus: 1, data: record })
    );
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
    };

    try {
      await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
      getModuleData(1, tablePagination.pageSize, filter, sorting);
    } catch (error) {
      console.log(error);
    }
  };
  const studentDocumentsColumns = [
    {
      title: "Document Type",
      dataIndex: "document_type",
      key: "document_type",
      sorter: true,
      ...getColumnSearchModalProps("document_type"),
    }, {
      title: "Comments",
      dataIndex: "comments",
      key: "comments",
      sorter: true,
      ...getColumnSearchModalProps("comments"),
    }, {
      title: "Operation",
      key: "action",
      render: (data, record) => (
        <>
          {permission.includes("Delete") && (
            <Popconfirm
              title={setLocale("sure_to_delete")}
              onConfirm={(e) => handleDelete(record)}
            >
              <DeleteOutlined
                style={{ fontSize: "15px" }}
                className="text-danger"
              />{" "}
              &nbsp;
            </Popconfirm>
          )}
          <Button
            type="link"
            href={env.FILE_ENDPOINT_URL + record?.file_path}
            target="_blank"
            rel="noopener noreferrer"
            icon={<DownloadOutlined />}
          />
        </>
      ),

    },
  ];
  const columns = [
    {
      title: "Student Name",
      dataIndex: "student_full_name",
      key: "student_full_name",
      sorter: true,
      ...getColumnSearchProps("student_full_name"),
      render: (text, record) => (
        <>
          <Link to={`/app/student_view/${record.student_enc_id}`}>{text}</Link>
        </>
      ),
    },
    {
      title: "Student ID",
      dataIndex: "student_student_id",
      key: "student_student_id",
      sorter: true,
      ...getColumnSearchProps("student_student_id"),
    },
    {
      title: "Grade",
      dataIndex: "grade",
      key: "grade",
      sorter: true,
      ...getColumnSearchProps("grade"),
    },
    // {
    //   title: "Document Type",
    //   dataIndex: "document_type",
    //   key: "document_type",
    //   sorter: true,
    //   ...getColumnSearchProps("document_type"),
    // },
    // {
    //   title: "File Name",
    //   dataIndex: "file_name",
    //   key: "file_name",
    //   sorter: true,
    //   ...getColumnSearchProps("file_name"),
    // },
    {
      title: setLocale("operation"),
      key: "action",
      render: (data, record) => (
        <>
          {/* {permission.includes("Delete") && (
            <Popconfirm
              title={setLocale("sure_to_delete")}
              onConfirm={(e) => handleDelete(record)}
            >
              <DeleteOutlined
                style={{ fontSize: "15px" }}
                className="text-danger"
              />{" "}
              &nbsp;
            </Popconfirm>
          )} */}
          {permission.includes("Update") && (
            <EditOutlined
              style={{ fontSize: "15px", marginRight: "9px" }}
              className="text-success"
              onClick={(e) => handleUpdate(record)}
            />
          )}
          {permission.includes("View") && (
            <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" onClick={(e) => handelModal(true, record)} />
          )}
        </>
      ),
    },
  ];

  return (
    <>
      <Row gutter={16}>
        <Col xs={24} sm={24} md={12} lg={12}>
          <Breadcrumb className="my-2 mx-2">
            <Breadcrumb.Item>
              <Link to="/app/default">{setLocale("home")}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{setLocale("Student Document")}</Breadcrumb.Item>
          </Breadcrumb>
        </Col>
        <Col xs={24} sm={24} md={12} lg={12} style={{ textAlign: 'right' }}>
          <OrganizationSelect
            updateSortFilters={updateSortFilters}
            filter={filter}
            sorting={sorting}
            tablePagination={tablePagination}
            getModuleData={getModuleData}
          />
        </Col>
      </Row>

      <>
        <div className="code-box">
          <section className="code-box-demo">
            {permission.includes("Create") && (
              <Button
                className="ant-btn-round ant-btn-sm"
                type="primary"
                style={{ float: "right", margin: "5px" }}
                onClick={handleOpenModal}
              >
                {setLocale("Add Student Documents")}
              </Button>
            )}
          </section>
          {DocumentStudentAddDrawer && <AddDocumentStudentModal />}
          <section className="code-box-description">
            <Table
              onChange={handleTableChange}
              columns={columns}
              loading={DocumentStudentTableLoading}
              rowKey={(record) => record.id}
              dataSource={DocumentStudentResult.data ?? []}
              pagination={false}
            />
            <Pagination
              style={{ margin: "16px", float: "right" }}
              current={tablePagination.current}
              pageSize={tablePagination.pageSize}
              total={tablePagination.total}
              showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} Records`}
              pageSizeOptions={["10", "20", "50", "100", "1000"]}
              showQuickJumper
              onChange={handlePageChange}
            />
          </section>
        </div>
        <Modal
          title={getStudentDocumentsData.length > 0 ? getStudentDocumentsData[0].student_full_name + " Documents" : "Student Documents"}
          open={isModalOpen}
          // onOk={() => handelModal(false, "")}
          onCancel={() => handelModal(false, "")}
          footer={false}
          width={600}>
          <Table
            dataSource={getStudentDocumentsData}
            columns={studentDocumentsColumns}
            pagination={false}
          />
        </Modal>
      </>
    </>
  );
}

export default Index;

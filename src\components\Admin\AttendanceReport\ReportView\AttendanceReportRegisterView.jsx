import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { message,Table,Row,Space, Col, Button  } from 'antd';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import IntlMessage from 'components/util-components/IntlMessage';
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const AttendanceReportRegisterView = (props) => {
  const { data,reportName } = props;
  const [filteredData, setFilteredData] = useState(data);

  const [messageApi, contextHolder] = message.useMessage();
  const success = (msg) => {
      messageApi.open({
          type: 'error',
          content: msg,
          style: { marginTop: '90vh',  },
      });
  };

  const exportToExcel = () => {
      if(!filteredData.length){
          success('Nothing to export!')
          return;
      }
      const keys = Object.keys(filteredData[0]); // Extract keys dynamically
      const transformedData = [
        [reportName], 
        keys, // Include the header row
        ...filteredData.map((item) => Object.values(item)) // Extract values dynamically
      ];

      const ws = XLSX.utils.aoa_to_sheet(transformedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, `${reportName}.xlsx`);
  };

  const distinctAttStudents = [...new Set(data.map(record => record.student_name))];
  const filterOptionsForStudents = distinctAttStudents.map(value => ({
      text: value,
      value: value,
  }));
  // Extract the keys from the first item to use as column headers
  const columns = data.length > 0 ? Object.keys(data[0]) : null;
  // Define the fixed column
  const fixedColumn = {
    title: setLocale("attendance_report.student_name"),
    dataIndex: "student_name",
    key: "student_name",
    fixed: "left", // Fix the column to the left side
    width: 200, // Set a specific width for the column
    filters: filterOptionsForStudents,
      onFilter: (value, record) => record.student_name.startsWith(value),
      filterSearch: true, 
      sorter: (a, b) => a.student_name.localeCompare(b.student_name),
  };

  // Generate the remaining dynamic columns
  const dynamicColumns = columns? columns.filter((column) => column !== "student_name")
    .map((column) => ({
      title: column,
      dataIndex: column,
      key: column,
    })):[];
    const handleTableChange = (pagination, filters, sorter, extra) => {
      // Update filteredData whenever the table's filters change
      setFilteredData(extra.currentDataSource);
    };
  return(
    <>
    {contextHolder}
      <Row gutter={4} className='my-2'>
          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
              <Space wrap>
              <h4>{ reportName }</h4>
              </Space>
          </Col>
          <Col xs={24} sm={24} md={12} lg={12} xl={12} style={{textAlign: "right"}}>
              <Button type='primary'  onClick={exportToExcel}>{setLocale('attendance_report.export_to_excel')}</Button>                    
          </Col>
      </Row>   
      <Table
        pagination={false}
        dataSource={data}
        columns={[fixedColumn, ...dynamicColumns]}
        scroll={{ x: "max-content" }} // Enable horizontal scrolling if needed
        onChange={handleTableChange}
      />
    </>
  )
};

export default AttendanceReportRegisterView;

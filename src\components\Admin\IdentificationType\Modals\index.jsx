import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Switch } from "antd";
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from "react-redux";
import { IDENTIFICATION_TYPE } from "constants/AppConstants";
import {
    IdentificationTypeAddDrawerStatus,
    createIdentificationType,
    getIdentificationType,
    onCloseError
  } from "store/slices/IdentificationType/manageIdentificationTypeSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {DrawerStatus,filter, sorting, IdentificationTypeAddDrawer, IdentificationTypeButtonAndModelLabel, IdentificationTypeErrors, IdentificationTypeShowMessage, IdentificationTypeButtonSpinner, IdentificationTypeEditData, tablePagination } = useSelector(
        (state) => state[IDENTIFICATION_TYPE]
      );
const [switchValue, setSwitchValue] = useState('checked');
const onClose = () => {
    dispatch(IdentificationTypeAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (IdentificationTypeEditData && IdentificationTypeEditData.id) {
    // If editing, include the id in the form values
    formValues.id = IdentificationTypeEditData.id;
    formValues.is_default = IdentificationTypeEditData.is_default == 1 ? true : false;
  }

  await dispatch(createIdentificationType(formValues))
};

useEffect(() => {
    if (Object.keys(IdentificationTypeErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getIdentificationType({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [IdentificationTypeErrors]);

useEffect(() => {
  IdentificationTypeEditData.is_default === 1 ? setSwitchValue('checked') : setSwitchValue();
}, [dispatch]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

const getSwitchValue = (checked) => {
  setSwitchValue(checked);
};

  return (
    <>

      <Drawer
        title={IdentificationTypeButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={IdentificationTypeAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            type: IdentificationTypeEditData?.type,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="type"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={IdentificationTypeShowMessage && IdentificationTypeErrors.type ? "error" : ""}
                help={IdentificationTypeShowMessage && IdentificationTypeErrors.type}
              >
                <Input />
              </Form.Item>
            </Col>
            {!(IdentificationTypeEditData && IdentificationTypeEditData.id) &&
            <Col span={12}>
              <Form.Item
                name="is_default"
                // valuePropName={switchValue}
                label="Is Default"
                rules={[
                  {
                    required: false,
                    message: "Status Error",
                  },
                ]}
                validateStatus={IdentificationTypeShowMessage && IdentificationTypeErrors.is_default ? "error" : ""}
                    extra={IdentificationTypeShowMessage && IdentificationTypeErrors.is_default}


              >
                <Switch
                  checkedChildren={<CheckOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  onChange={getSwitchValue}
                  checked={switchValue}
                  name="is_default"
                />
              </Form.Item>
            </Col>
            }

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={IdentificationTypeButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


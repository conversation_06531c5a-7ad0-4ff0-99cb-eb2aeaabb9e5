import React, { useEffect } from 'react';
import { useSelector, useDispatch } from "react-redux";
import { Row, Col, Form, Button, Input, notification } from 'antd';
import { markAssignment } from "store/slices/Assignments/assignmentSlice"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import IntlMessage from "components/util-components/IntlMessage"
import AssigneeRubrics from './AssigneeRubrics';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function SaveMarks() {
  const { singleAssignment, obtainMarksLoad,errors,showMessage,singleAssignee } = useSelector((state) => state[ASSIGNMENT_SLICE]);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [api, contextHolder] = notification.useNotification();
  const openNotificationWithIcon = (type, msg) => {
      api[type]({ message: msg })
  }

  useEffect(() => {
    
  }, [])

  const saveMarks = async (values) => {
    const obtaibMarks = parseFloat(values.marks_obtained)
    const totalMarks = parseFloat(singleAssignment.total_marks)
    if (obtaibMarks > totalMarks) {
      openNotificationWithIcon('error', `Obtain marks should be less or equal to total marks. Total marks is: ${totalMarks}`)
      return false
  }

  if (obtaibMarks < 0) {
      openNotificationWithIcon('error', `Obtain marks should be greater or equal to 0`)
      return false
  }
    values.id = singleAssignee.id
    await dispatch(markAssignment(values))
    // if(!errors){
    //   singleAssignee.marks_obtained = values.marks_obtained
    // }
  }
  return (
    <>
      {contextHolder}
      { 
        singleAssignee?.assignee_rubrics.length ? 
          <AssigneeRubrics></AssigneeRubrics> 
        :
        <Form layout="vertical" onFinish={saveMarks} form={form} encType="multipart/form-data" >
          <Row>
          <Col  xs={24} sm={24} md={24} lg={24}>          
            <Form.Item initialValue={singleAssignee?.marks_obtained}
              name="marks_obtained"
              label={setLocale('assignment.obtainMarks')}
              rules={[
                {
                  required: true,
                  message: setLocale('assignment.obtainMarksError'),
                },
              ]}
              validateStatus={showMessage && errors.marks_obtained ? "error" : ""}
                      extra={showMessage && errors.marks_obtained}  >
              <Input className='rounded-0' />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Form.Item  label="">
                  <Button className='rounded-0' loading={obtainMarksLoad} type="primary" htmlType="submit">
                      { setLocale('save') }
                  </Button>
              </Form.Item>
          </Col>
          </Row>
        </Form>             
      }
    </>
  )
}

export default SaveMarks;
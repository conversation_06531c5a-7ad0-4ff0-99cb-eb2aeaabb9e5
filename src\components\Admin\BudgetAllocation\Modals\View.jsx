import React, { useState, useEffect } from "react";
import { Modal,InputNumber,Drawer, Descriptions, Table, Tooltip } from "antd";
import { useSelector, useDispatch } from "react-redux";
// import { useParams, useNavigate, Link } from "react-router-dom";
import {
    EditOutlined,
    // ContainerOutlined
} from "@ant-design/icons";

import {
    setAllocatedBudgetView,
    updateBudgetItem
} from "store/slices/BudgetAllocation/manageBudgetAllocationSlice.js";
// import Flex from "components/shared-components/Flex";
import IntlMessage from "components/util-components/IntlMessage"
import { BUDGET_ALLOCATION } from "constants/AppConstants";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function View() {
    const dispatch = useDispatch();
    // const navigate = useNavigate();
    // const params = useParams();
    const { 
        permission,
        allocatedBudgetView, 
        allocatedBudgetViewDrawer,
        BudgetAllocationButtonSpinner
    } = useSelector((state) => state[BUDGET_ALLOCATION]);


    useEffect(() => {

    }, []);
    const onClose = () => {
        dispatch(setAllocatedBudgetView({ status: false, data: [] }));        
    };

    const columns = [
        {
            title: setLocale('budgetallocation.account_heads'),
            // dataIndex: "account_head_code",
            key: "account_head_code",
            render: (data, record) => ( 
                <>  
                <Tooltip title= {record?.account_head}>
                    {record?.account_head_code}
                </Tooltip>                  
                </>
            )
        },
        {
            title: setLocale('budgetallocation.total_allocated'),
            dataIndex: "total_allocated",
            key: "total_allocated",
            editable: true,
        },
        {
            title: setLocale('budgetallocation.total_consumed'),
            dataIndex: "consumed_amount",
            key: "consumed_amount",
        },
        {
            title: setLocale('budgetallocation.pending_amount'),
            dataIndex: "pending_amount",
            key: "pending_amount",
        },
        {
            title: setLocale('operation'),
            key: "action",
            render: (data, record) => (
                <>                    
                    {permission.includes("Update") && ( 
                        <EditOutlined style={{ fontSize: '15px' }} className="text-primary" onClick={(e) => handleEdit(record)}/>                        
                    )}
                </>
            )
        }   
    ]

    const [selectedRecord, setSelectedRecord] = useState(null)
    const [openEditModal, setOpenEditModal] = useState(false )
    const [selectedAmount, setSelectedAmount] = useState('')
    const handleEdit = (record) => {
        setSelectedRecord(record)
        setOpenEditModal(true)
        setSelectedAmount(record.total_allocated)                
    }
    const handleOk = async (e) => {
        const data = {
            id: selectedRecord?.id,
            total_allocated: selectedAmount
        }
        await dispatch(updateBudgetItem(data))
        setOpenEditModal(false)
    }
    const handleCancel = (e) => {
        setSelectedAmount('')
        setOpenEditModal(false)
    }
    return (
        <>
            <Drawer
                title={setLocale('budgetallocation.detail-view')}
                width={window.innerWidth > 800 ? "80%" : window.innerWidth - 100}
                onClose={onClose}
                open={allocatedBudgetViewDrawer}
                maskClosable={false}
                zIndex={1002}
                bodyStyle={{
                    paddingBottom: 80,
                }} >
                    <Descriptions column={4} title={''} layout="vertical" bordered>
                        <Descriptions.Item label={setLocale('budgetallocation.userName')}>
                            {allocatedBudgetView?.user_name}
                        </Descriptions.Item>
                        <Descriptions.Item label={setLocale('budgetallocation.gradeLevel')}>
                            {allocatedBudgetView?.grade_level}
                        </Descriptions.Item>
                        <Descriptions.Item label={setLocale('budgetallocation.class')}>                
                            {allocatedBudgetView?.class}
                        </Descriptions.Item>
                        <Descriptions.Item label={setLocale('budgetallocation.createdBy')}>                
                            {allocatedBudgetView?.created_by}
                        </Descriptions.Item>
                        <Descriptions.Item label={setLocale('budgetallocation.total_allocated')}>                
                            {allocatedBudgetView?.total_allocated}
                        </Descriptions.Item>
                        <Descriptions.Item label={setLocale('budgetallocation.total_consumed')}>                
                            {allocatedBudgetView?.total_consumed}
                        </Descriptions.Item>
                        <Descriptions.Item label={setLocale('budgetallocation.createdAt')}>                
                            {allocatedBudgetView?.created_at}
                        </Descriptions.Item>                        
                    </Descriptions>

                    <Table                        
                        columns={columns}
                        rowKey={record => record.id}
                        dataSource={allocatedBudgetView?.budget_details ?? []}
                        pagination={false}
                    />
            </Drawer>

            <Modal title={setLocale('budgetallocation.update')} 
                    confirmLoading={BudgetAllocationButtonSpinner} 
                    okText={setLocale('save')} 
                    open={openEditModal} 
                    onOk={handleOk} 
                    onCancel={handleCancel}
                    zIndex={1100}>
                {setLocale('budgetallocation.total_allocated')}
                <br />
                <InputNumber 
                    style={{ width: '50%' }}
                    value={selectedAmount} 
                    placeholder={setLocale('budgetallocation.total_allocated')}
                    onChange={(e) => { setSelectedAmount(e) }} />                  
            </Modal>
        </>
    );
}
export default View;

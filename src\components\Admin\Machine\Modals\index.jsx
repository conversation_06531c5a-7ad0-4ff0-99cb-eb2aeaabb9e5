import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { MACHINE, WIFI_ROUTERS } from "constants/AppConstants";
import {
  MachineAddDrawerStatus,
  createMachine,
  getMachine,
  onCloseError,
  generateUniqueCode
} from "store/slices/Machine/manageMachineSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import {
  LoadingOutlined
} from '@ant-design/icons';

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {
    MachineAddDrawer,
    MachineButtonAndModelLabel,
    sorting,
    filter,
    MachineErrors,
    MachineShowMessage,
    MachineButtonSpinner,
    MachineEditData,
    tablePagination,
    DrawerStatus,
    UniqueCodeGeneratorButtonSpinner,
    UniqueCodeResult
  } = useSelector(
    (state) => state[MACHINE]
  );

  const {
    WifiRoutersResult,
  } = useSelector(
    (state) => state[WIFI_ROUTERS]
  );

  const onClose = () => {
    dispatch(MachineAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {
    if (MachineEditData && MachineEditData.id) {
      // If editing, include the id in the form values
      formValues.id = MachineEditData.id;
    }

    await dispatch(createMachine(formValues));
  };

  useEffect(() => {
    if (Object.keys(MachineErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getMachine({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [MachineErrors]);

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const handleGenerateUniqueCode = () => {
    dispatch(generateUniqueCode());
  };

  useEffect(() => {
    if (UniqueCodeResult) {
      form.setFieldsValue({
        mac_address: UniqueCodeResult
      });
    }
  }, [UniqueCodeResult]);

  return (
    <>
      <Drawer
        title={MachineButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={MachineAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: MachineEditData?.name,
            mac_address: MachineEditData?.mac_address,
            wifi_router: MachineEditData?.wifi_routers?.map(router => router.id) || [],
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={MachineShowMessage && MachineErrors.name ? "error" : ""}
                help={MachineShowMessage && MachineErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                name="mac_address"
                label={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{setLocale('machine.macAddress')}</span>
                    {/* <Button type="link" danger onClick={(e) => handleGenerateUniqueCode()}>
                      Generate Unique Code
                    </Button> */}
                  </div>
                }
                rules={[
                  {
                    required: true,
                    message: setLocale('machine.macAddressError'),
                  },
                ]}
                validateStatus={MachineShowMessage && MachineErrors.mac_address ? "error" : ""}
                help={MachineShowMessage && MachineErrors.mac_address}
              >
                <Input
                  suffix={UniqueCodeGeneratorButtonSpinner && <LoadingOutlined />}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="wifi_router"
                label={setLocale('wifi_router.label')}
                rules={[
                  {
                    required: true,
                    message: setLocale('wifi_router.routerError'),
                  },
                ]}
                validateStatus={MachineShowMessage && MachineErrors.wifi_router ? "error" : ""}
                help={MachineShowMessage && MachineErrors.wifi_router}
              >
                <Select
                  className='rounded-0'
                  showSearch
                  optionLabelProp="label"
                  placeholder={setLocale('wifi_router.label')}
                  allowClear
                  mode="multiple"
                  maxTagCount={2}
                  optionFilterProp="children"
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  // onChange={gradeLevelOnChange}
                  options={
                    WifiRoutersResult.data.map(g => ({
                      value: g.id,
                      label: g.name,
                    })) || []}
                />
              </Form.Item>
            </Col>
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={MachineButtonSpinner}
            >
              {MachineButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


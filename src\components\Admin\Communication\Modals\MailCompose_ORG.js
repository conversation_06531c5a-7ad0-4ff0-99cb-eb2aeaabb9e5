import React, { useState, useEffect } from 'react'
import { Form, Input, Button, message,Skeleton,Select } from 'antd';
import { useDispatch, useSelector } from "react-redux";
import ReactQuill from 'react-quill';
import { useNavigate } from 'react-router-dom';
import { MANAGE_CLASS,CONVERSATION_TEMPLATES,CONVERSATION_TYPES,COMMUNICATION } from "constants/AppConstants";
import { getManageClass } from "store/slices/ManageClass/manageManageClassSlice";
import { getConversationTemplates } from 'store/slices/ConversationTemplates/manageConversationTemplatesSlice';
import { getConversationTypes } from 'store/slices/ConversationTypes/manageConversationTypesSlice';
import { sentCommunicationMail } from "store/slices/Communication/manageCommunicationSlice";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const MailCompose = () => {
	const dispatch = useDispatch();
	const [form] = Form.useForm();
	const { ManageClassResult } = useSelector( (state) => state[MANAGE_CLASS] );
    const { ConversationTemplatesResult } = useSelector((state) => state[CONVERSATION_TEMPLATES]);
    const { ConversationTypesResult } = useSelector((state) => state[CONVERSATION_TYPES]);
    const { CommunicationButtonSpinner } = useSelector((state) => state[COMMUNICATION]);

	const [pageLoading, setPageLoading] = useState(true);

	const navigate = useNavigate()

	const modules = {
		toolbar: [
			[{ header: [1, 2, false] }],
			['bold', 'italic', 'underline'],
			['image', 'code-block']
		],
	}

	const back = () => {
		navigate(-1);
	}

	const onSubmit = async (formValues) => {

		await dispatch(sentCommunicationMail(formValues))
		.then(() => {
			
		})
		.catch((error) => {
		// Handle delete error
		console.error("Error deleting module:", error);
		});

		// message.success('Email has been sent');
		// navigate('/app/communication/inbox');
	}
	
	useEffect(() => {

		const fetchData = async () => {
		  try {
			// dispatch(getManageClass(null))
			await dispatch(getConversationTemplates({ perPage: 1000 }));
			await dispatch(getConversationTypes({ perPage: 1000 }));
			dispatch(getManageClass(null)).then((result) => {
				setPageLoading(false)
			  });
	  
		  } catch (error) {
			console.error('Error fetching school year data:', error);
		  }
		};
		  fetchData();
	  }, []);

	return (
		<div className="mail-compose">
			<h4 className="mb-4">New Message</h4>
			<Skeleton active loading={pageLoading}>
				<Form 
					name="nest-messages" 
					onFinish={onSubmit}
            		form={form}
				>
					
					{/* <Form.Item name={['mail', 'cc']} >
						<Input placeholder="Cc:" />
					</Form.Item> */}
					<Form.Item name={['mail', 'subject']} >
						<Input placeholder="Subject:" />
					</Form.Item>
					<Form.Item
						name={["mail","class_id"]}
						// label={setLocale('classes.label')}
						rules={[
						{
							required: true,
							message: setLocale('classes.label_error'),
						},
						]}
					>
						<Select className='rounded-0' showSearch optionLabelProp="label"
						placeholder={setLocale('classes.label')}
						allowClear
						mode="multiple"  // Set mode to "multiple" even for single selections
						optionFilterProp="children"
						filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
						filterSort={(optionA, optionB) =>
							(optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
						}
						options={ManageClassResult ? ManageClassResult : null}
						/>
					</Form.Item>
					<Form.Item
						name={['mail', 'conversation_template_id']}
						rules={[
							{
								required: true,
								message: setLocale('conversation_template.conversation_template_error'),
							},
						]}>
						<Select className='rounded-0' showSearch optionLabelProp="label"
							placeholder={setLocale('conversation_template.conversation_template')}
							allowClear
							optionFilterProp="children"
							filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
							filterSort={(optionA, optionB) =>
								(optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
							}
							options={ConversationTemplatesResult?.data ?? []}

						/>
					</Form.Item>
					<Form.Item
						name={['mail', 'conversation_type_id']}
						rules={[
							{
								required: true,
								message: setLocale('conversation_types.label_error'),
							},
						]}>
						<Select className='rounded-0' showSearch optionLabelProp="label"
							placeholder={setLocale('conversation_types.label')}
							allowClear
							optionFilterProp="children"
							filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
							filterSort={(optionA, optionB) =>
								(optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
							}
							options={ConversationTypesResult?.data ?? []}
						/>
					</Form.Item>
					<Form.Item name={['mail', 'content']}>
						<ReactQuill theme="snow" modules={modules} />
					</Form.Item>
					<Form.Item>
						<div className="mt-5 text-right">
							{/* <Button type="link" className="mr-2">
								Save Darft
							</Button> */}
							<Button className="mr-2" onClick={back}>
								Discard
							</Button>
							<Button type="primary" htmlType="submit" loading={CommunicationButtonSpinner}>
								Send
							</Button>
						</div>
					</Form.Item>
				</Form>
			</Skeleton>
		</div>
	)
}

export default MailCompose

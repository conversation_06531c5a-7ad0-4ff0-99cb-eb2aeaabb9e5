import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Tooltip,
  notification,
  Select,
  Button,
  Col, Row, Input,
  Skeleton, Modal,
  Table,
  Radio,
} from 'antd';
import {
  CheckCircleFilled,
  ExclamationCircleFilled,
  UserOutlined, PlusOutlined, CalendarFilled,
  BookFilled, EyeFilled, SortAscendingOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import {
  finalizedCourseGradeCategory,
  addStudentObtainedMarks,
  getClassStudentsForGrades,
} from "store/slices/Grade/studentGradeBookSlice.js";
import {
  getGradeBookCategory,
  setSelectedCategory,
  setAddNewGradeCategoryModal,
} from "store/slices/GradeBookCategory/manageGradeBookCategorySlice.js";
import {
  getSchoolTerm
} from "store/slices/SchoolTerm/manageSchoolTermSlice.js";
import { GRADE_BOOK_CATEGORY, STUDENT_GRADE_BOOK_SLICE, MANAGE_CLASS, SCHOOL_TERM } from "constants/AppConstants";
import IntlMessage from 'components/util-components/IntlMessage';
import { gradeCellBackgroundColorClass } from 'components/composeable'
import AddNewCategory from './AddNewCategory';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Option } = Select;
const { confirm } = Modal;
const { Column, ColumnGroup } = Table;

function StudentGradesTable() {
  const dispatch = useDispatch()
  const [api, contextHolder] = notification.useNotification();
  const openNotificationWithIcon = (type, msg) => {
    api[type]({ message: msg })
  };

  const { 
    ClassCourseEditData 
  } = useSelector((state) => state[MANAGE_CLASS])
  const {
    GradeBookCategoryResult,
    addNewGradeCategoryModal,
  } = useSelector((state) => state[GRADE_BOOK_CATEGORY])
  const {
    studentGradeHeader,
    studentGradeBody,
    loadStudentGrades,
    courseGradeAssignments
  } = useSelector((state) => state[STUDENT_GRADE_BOOK_SLICE])
  const { 
    SchoolTermResult
  } = useSelector((state) => state[SCHOOL_TERM])
  const classCourse = { class_id: ClassCourseEditData?.manage_class_id, course_id: ClassCourseEditData?.course_id }

  // const headerColumns =  studentGradeHeader.map(column => {
  //     return {
  //         title: column,
  //         dataIndex: column,
  //         key: column,   
  //         render: (text, record, rowIndex) => {
  //           const cell = record[column];
  //           const isReadOnly = cell.status === 'Finalized';
  //           const markObtained = isReadOnly ? 
  //             <Tooltip title={ cell.grade }> {cell.marks_obtained } </Tooltip> : 
  //             <Input defaultValue={cell.marks_obtained} key={rowIndex}
  //               onBlur={(e) => handleStudentMarkChange(e, record, column)}
  //               style={{ border: 'none', backgroundColor: 'transparent', width: '70px'}} 
  //             />;
  //           return (
  //             <span className={ isReadOnly ? gradeCellBackgroundColorClass(cell.grade) : ''}>
  //               { column === 'Name' || column === 'Total' || column === 'Percentage' ? text : markObtained }
  //             </span>
  //           );
  //         }
  //       }
  // })
  const iconColor = { color: '#BC404F' }

  useEffect(() => {    
    dispatch(getSchoolTerm())
    dispatch(getClassStudentsForGrades(classCourse))
    dispatch(getGradeBookCategory())
    // dispatch(viewStudentGradeDetail({class_student_id:3}))
  }, [])
  
  

  /**change Student Obtained Marks */
  const handleStudentMarkChange = (e, record, studentGradeKey, column) => {
    
    const cell = record[studentGradeKey][column]    
    const obtaibMarks = parseFloat(e.target.value)
    const courseGradeBookCategoryId = cell.course_grade_book_category_id
    const classStudentId = cell.class_student_id
    const totalMarks = cell.total_marks
    const previousObtainedMarks = cell.marks_obtained

    const saveObtainedMarks = {
      totalMarks,
      obtaibMarks,
      classStudentId,
      courseGradeBookCategoryId
    }
    if (obtaibMarks > totalMarks) {
      openNotificationWithIcon('error', `Obtain marks should be less or equal to total marks. Total marks is: ${totalMarks}`)
      return false
    }
    if (obtaibMarks && previousObtainedMarks !== obtaibMarks) {
      dispatch(addStudentObtainedMarks(saveObtainedMarks))
    }
  }
  const finalizedHandler = async (category) => {
    confirm({
      title: setLocale('sureToFinalize'),
      icon: <ExclamationCircleFilled />,
      zIndex: 1011,
      async onOk() {
        const id = category.course_grade_book_category_id;
        let status = 'Finalized';
        await dispatch(finalizedCourseGradeCategory({ id, status }))
        await dispatch(getClassStudentsForGrades(classCourse))
      },
      onCancel() { },
    });
  }
  const openCategoryGradeModal = (data, category) => {
    dispatch(setSelectedCategory(category))
    dispatch(setAddNewGradeCategoryModal(data))
  }

  const shortAssignmentName = (assignmentName) => {
    if (assignmentName.length > 5) {
      return assignmentName.slice(0, 5) + '...'
    }
    return assignmentName
  }

  const [selectedType, setSelectedAssignmentType] = useState('all')
  const [selectedTerm, setSelectedTerm] = useState('all')
  const assignmentTypeChange = (e) => {
   setSelectedAssignmentType(e.target.value)
   const postData = {...classCourse, type_id: e.target.value, term_id: selectedTerm }   
    dispatch(getClassStudentsForGrades(postData))   
  }

  const termChange = (e) => {
    setSelectedTerm(e)
    const postData = {...classCourse, term_id: e, type_id: selectedType }
    dispatch(getClassStudentsForGrades(postData))
  }

  const schoolTerms = { id: 'all', label: 'All' };
  const schoolTermsOptions = [schoolTerms, ...(SchoolTermResult?.data || [])];


  return (
    <>
      {contextHolder}
     
      <Select defaultValue={selectedTerm} optionLabelProp="label" onChange={termChange} allowClear>
      <Option key={`all`} value={`all`} label={`All`}>All</Option>
        {SchoolTermResult?.data?.map((term, index) => {
          return (
            <Option key={index} value={term.id} label={term.label}>
              {term.label}
            </Option>
          )
        })}
      </Select>
      <Radio.Group onChange={assignmentTypeChange} value={selectedType} optionType="button"
        buttonStyle="solid">
        <Radio value={`all`}>All</Radio>
        { GradeBookCategoryResult.map((category, index) => {
          return (
            <Radio value={category.id}>{category.name}</Radio>
            )
          })
        }
      </Radio.Group>
      {
        GradeBookCategoryResult.map((category, index) => {
          return (
            <Button.Group size='small' key={index}>
              <Button size='small'  onClick={() => { openCategoryGradeModal(true, category) }} >
                <PlusOutlined /> {category.name}
              </Button>
            </Button.Group>
          )
        })
      }
      <Row gutter={16} className='my-3'>
        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <div className='grade-book-select'>
            <h5 className='my-2 mx-2'>Filter by: </h5>
            <Select
              className='mx-2'
              defaultValue={{ label: <><CalendarFilled style={iconColor} /> Date Range</> }}
              optionLabelProp="label"
            >
              <Option value="2022-2023" label={<><CalendarFilled style={iconColor} /> 2022-2023</>}></Option>
              <Option value="2023-2024" label={<><CalendarFilled style={iconColor} /> 2023-2024</>}></Option>
              <Option value="2024-2025" label={<><CalendarFilled style={iconColor} /> 2024-2025</>}></Option>
            </Select>
            <Select
              className='mx-2'
              defaultValue={{ label: <><BookFilled style={iconColor} /> Subject</> }}
              optionLabelProp="label"
            >
              <Option value="English" label={<><BookFilled style={iconColor} /> English</>}></Option>
              <Option value="Mathematics" label={<><BookFilled style={iconColor} /> Mathematics</>}></Option>
              <Option value="Science" label={<><BookFilled style={iconColor} /> Science</>}></Option>
            </Select>
            <Select
              className='mx-2'
              defaultValue={{ label: <><UserOutlined style={iconColor} /> Student</> }}
              optionLabelProp="label"
            >
              <Option value="Student 1" label={<><UserOutlined style={iconColor} /> Student 1</>}></Option>
              <Option value="Student 2" label={<><UserOutlined style={iconColor} /> Student 2</>}></Option>
              <Option value="Student 3" label={<><UserOutlined style={iconColor} /> Student 3</>}></Option>
            </Select>
          </div>
        </Col>
        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <div style={{ display: "flex", justifyContent: "end" }}>
            <Select
              className='mx-2'
              defaultValue={{ label: <><SortAscendingOutlined style={iconColor} /> Sort by</> }}
              optionLabelProp="label" >
              <Option value="1" label={<><SortAscendingOutlined style={iconColor} /> 1</>}></Option>
              <Option value="2" label={<><SortAscendingOutlined style={iconColor} /> 2</>}></Option>
              <Option value="3" label={<><SortAscendingOutlined style={iconColor} /> 3</>}></Option>
            </Select>
            <Select
              className='mx-2'
              defaultValue={{ label: <><EyeFilled style={iconColor} /> View as</> }}
              optionLabelProp="label" >
              <Option value="Excel" label={<><EyeFilled style={iconColor} /> Excel</>}></Option>
              <Option value="PDF" label={<><EyeFilled style={iconColor} /> PDF</>}></Option>
            </Select>
            <Button size='small' >
              <DownloadOutlined /> Export CSV
            </Button>
          </div>
        </Col>
      </Row>
      <Row gutter={16} className='my-3'>
        {
          loadStudentGrades ? <><Skeleton active /><Skeleton active /></> :
            <>
              <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Table dataSource={studentGradeBody}
                pagination={false}
                summary={(pageData) => {
                  return (
                    <>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <b>{setLocale("studentgrading.studentgrading")}</b>
                        </Table.Summary.Cell>
                        { courseGradeAssignments?.map((grade, index) =>
                        <Table.Summary.Cell index={index+1}>   
                          {
                            grade.status === 'Finalized' ? 
                            <><Tooltip title={grade.status}>
                              <CheckCircleFilled style={{ color: 'green' }} />
                            </Tooltip> </> : 
                            <><Button type="primary" onClick={() => finalizedHandler(grade)}> <CheckCircleFilled /> Finalize </Button></>
                          }                           
                          </Table.Summary.Cell>)
                        }                          
                      </Table.Summary.Row>                     
                    </>
                  )
                }} >
                  <Column title="Name" dataIndex="Name" key="Name" />
                    {studentGradeHeader?.map((record, key) => {
                      const studentGradeKey = Object.keys(record)[0];
                      return (
                        <ColumnGroup title={studentGradeKey} key={studentGradeKey}>
                        {record[studentGradeKey].map((assessmentKey) => (
                            <Column
                              title={
                                <Tooltip title={assessmentKey}>{shortAssignmentName(assessmentKey)}</Tooltip>
                                }
                              key={`${studentGradeKey}_${assessmentKey}`}
                              render={(text, record, rowIndex) => {
                                const cell = record[studentGradeKey][assessmentKey];
                                const isReadOnly = cell.status === 'Finalized';
                                const markObtained = isReadOnly ? (
                                  <Tooltip title={cell.grade}>{cell.marks_obtained}</Tooltip>
                                ) : (
                                  <Input
                                    defaultValue={cell.marks_obtained}
                                    onBlur={(e) => handleStudentMarkChange(e, record, studentGradeKey, assessmentKey)}
                                    style={{ border: 'none', backgroundColor: 'transparent', width: '70px' }}
                                  />
                                );
                                return (
                                  <span className={isReadOnly ? gradeCellBackgroundColorClass(cell.grade) : ''}>
                                    { markObtained }
                                  </span>
                                );
                              }}
                            />                          
                            ))}
                        </ColumnGroup>
                      );
                    })}
                    {/* <Column title="Total" dataIndex="Total" key="Total" /> */}
                    <Column title="Percentage" dataIndex="Percentage" key="Percentage" />                 
                </Table>
              </Col>             
            </>
        }
      </Row>

      {addNewGradeCategoryModal ?
        <AddNewCategory /> : null
      }
    </>
  )
}

export default StudentGradesTable

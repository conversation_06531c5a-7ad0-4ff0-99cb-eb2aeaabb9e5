import React,{useEffect} from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Space, Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { 
  BUDGET_ALLOCATION, 
  SCHOOL_YEAR, 
  PROCUREMENT_ITEM,
  NAME_OF_COMMON_SLICE
} from "constants/AppConstants";
import {
  getClassesByGrade
} from "store/slices/PurchaseRequests/managePurchaseRequestsSlice.js";
import {
    BudgetAllocationAddDrawerStatus,
    createBudgetAllocation,    
    onCloseError,
    getTeacherGradeCourses,
    fetchTeacherAllocatedBudget,
    setTeacherAllocatedBudget,
    setAllocatedBudgetFilter
} from "store/slices/BudgetAllocation/manageBudgetAllocationSlice.js";
import {
  getSchoolYear,
  setDefaultYear
} from "store/slices/SchoolYear/manageSchoolYearSlice.js";
import {
  getProcurementItem,
} from "store/slices/ProcurementItem/manageProcurementItemSlice.js";
import { 
  getOrganizationGradeLevel,
  getTeachersHaveUser
} from "store/slices/commonSlice";
import { USER_INFORMATION, DEFAULT_YEAR } from "constants/AuthConstant";
import {     
  PURCHASE_REQUESTS,    
} from "constants/purchase-requests/index.js";

import IntlMessage from "components/util-components/IntlMessage";
import BudgetAccountHeads from './BudgetAccountHeads';
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const defaultYear = JSON.parse(localStorage.getItem(DEFAULT_YEAR));

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        BudgetAllocationAddDrawer,
        BudgetAllocationButtonAndModelLabel,
        sorting,
        filter,
        BudgetAllocationErrors,
        BudgetAllocationShowMessage,
        BudgetAllocationButtonSpinner,
        BudgetAllocationEditData,
        teacherOrganizationGradeLevels,
        teacherAllocatedBudget
    } = useSelector((state) => state[BUDGET_ALLOCATION]);
    const {     
      gradeClasses,
      gradeClassesLoader
    } = useSelector((state) => state[PURCHASE_REQUESTS]);
    const {
      SchoolYearResult,
      defaultSchoolYear
    } = useSelector((state) => state[SCHOOL_YEAR])
    const { 
      ProcurementItemResult 
    } = useSelector((state) => state[PROCUREMENT_ITEM]);
    const { 
      OrganizationGradeLevelResult,
      teachersHavingUser
    } = useSelector((state) => state[NAME_OF_COMMON_SLICE]);

const onClose = () => {
    dispatch(BudgetAllocationAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}


useEffect(() => {        
  // dispatch(getOrganizationGradeLevel({ perPage: 1000 }));
  dispatch(getTeachersHaveUser());
  // dispatch(getProcurementItem({ perPage: 1000 }));
  form.setFieldsValue({
    school_year_id: defaultSchoolYear,   
  })    
}, []);
  


const getGradeClasses = (value) => {
  dispatch(setTeacherAllocatedBudget([]));
  form.setFieldsValue({ manage_class_id: null });
  dispatch(getClassesByGrade({ organization_grade_level_id: value }));     
}
const getTeacherCourses = (value) => {
  dispatch(setTeacherAllocatedBudget([]));
  form.setFieldsValue({ manage_class_id: null });
  form.setFieldsValue({ organization_grade_level_id: null });
  dispatch(getTeacherGradeCourses({ user_id: value }));
}
const getTeacherAllocatedBudget = (values) => {  
  dispatch(setAllocatedBudgetFilter(values));
  dispatch(fetchTeacherAllocatedBudget(values));
}
const onChangeSchoolYear = (value) => {
  dispatch(setTeacherAllocatedBudget([]));
}
  return (
    <>

      <Drawer
        title={BudgetAllocationButtonAndModelLabel}
        width={window.innerWidth > 800 ? "80%" : window.innerWidth - 100}
        onClose={onClose}
        open={BudgetAllocationAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{ paddingBottom: 80 }}>
        <Form layout="vertical" onFinish={getTeacherAllocatedBudget} form={form} autoComplete="off" >
          <Row gutter={16}>
            <Col xs={24} sm={24} md={4} lg={4} xl={4}>
              <Form.Item name="school_year_id"
                label={setLocale('budgetallocation.school_year')}
                rules={[
                  {
                    required: true,
                    message: setLocale('budgetallocation.school_year_required'),
                  },
                ]}
                validateStatus={BudgetAllocationShowMessage && BudgetAllocationErrors.school_year_id ? "error" : ""}
                help={BudgetAllocationShowMessage && BudgetAllocationErrors.school_year_id}>
               <Select onChange={onChangeSchoolYear} defaultValue={defaultSchoolYear} options={SchoolYearResult?.data ?? []} />
              </Form.Item>
            </Col>
            <Col sx={24} sm={24} md={6} lg={6}>            
                <Form.Item
                    name='user_id'
                    label={setLocale('budgetallocation.teacher')}
                    rules={[{ required: true, message: setLocale('budgetallocation.teacherError') }]}
                    validateStatus={BudgetAllocationShowMessage && BudgetAllocationErrors?.user_id ? "error" : ""}
                    help={BudgetAllocationShowMessage && BudgetAllocationErrors?.user_id}>
                    <Select className='rounded-0' showSearch optionLabelProp="label"
                        allowClear
                        optionFilterProp="children"
                        filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                        options={teachersHavingUser ?? []}
                        onChange={getTeacherCourses}
                    />
                </Form.Item>                
            </Col>
            <Col xs={24} sm={24} md={4} lg={4}>
                <Form.Item
                    name="organization_grade_level_id"
                    label={setLocale('purchaserequests.gradeLevel')}
                    rules={[
                        {
                            required: true,
                            message: setLocale('purchaserequests.gradeLevelError'),
                        },
                    ]}
                    validateStatus={BudgetAllocationShowMessage && BudgetAllocationErrors.organization_grade_level_id ? "error" : ""}
                    help={BudgetAllocationShowMessage && BudgetAllocationErrors.organization_grade_level_id}
                >
                    <Select className='rounded-0' showSearch optionLabelProp="label"
                        onChange={getGradeClasses}                        
                        allowClear
                        optionFilterProp="children"
                        filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                        options={teacherOrganizationGradeLevels ?? []}
                    />
                </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={4} lg={4}>
              <Form.Item
                  name="manage_class_id"
                  label={setLocale('purchaserequests.class')}
                  rules={[
                      {
                          required: true,
                          message: setLocale('purchaserequests.classError'),
                      },
                  ]}
                  validateStatus={BudgetAllocationShowMessage && BudgetAllocationErrors.manage_class_id ? "error" : ""}
                  help={BudgetAllocationShowMessage && BudgetAllocationErrors.manage_class_id}
              >
                  <Select className='rounded-0' 
                      loading={gradeClassesLoader}
                      showSearch 
                      optionLabelProp="label"
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input)}
                      options={gradeClasses ?? []}
                  />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={4} lg={4} className='mt-1'>
              <Button loading={BudgetAllocationButtonSpinner} type="primary" className='mt-4' htmlType="submit">
                {setLocale('budgetallocation.search')}
              </Button>
            </Col>
          </Row>         
        </Form>
        <BudgetAccountHeads />
      </Drawer>
    </>
  );
};
export default Index;


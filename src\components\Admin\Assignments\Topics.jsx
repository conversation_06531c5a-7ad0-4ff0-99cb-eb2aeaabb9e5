import React, { useEffect, useState, useRef } from "react";
import { Input, Space, Button, Table, Popconfirm, Pagination } from "antd";
import { DeleteOutlined, EditOutlined, SearchOutlined } from "@ant-design/icons";
import AddTopicModal from "./Modals/AddTopicModal";
import { useSelector, useDispatch } from "react-redux";
import {
  setOpenModal,
  getAllTopics,
  deleteTopic,
  setEditData,
  updateSortFilters,
  setColumnSearch
} from "store/slices/Topics/topicSlice";
import { MANAGE_CLASS } from 'constants/AppConstants'
import IntlMessage from "components/util-components/IntlMessage";
import { isJSON } from "components/composeable"
import Highlighter from "react-highlight-words";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Topics() {
  const {
    topics,
    loading,
    openTopicModal, tableLoading,
    topicPagination,
    filter,
    sorting
  } = useSelector((state) => state.topicSlice);
  const {
    ClassCourseEditData,
    ViewManageClassData, userRoles, teacherData,
  } = useSelector(state => state[MANAGE_CLASS]);

  const [canEdit, setCanEdit] = useState(true);

  const dispatch = useDispatch();
  const searchInput = useRef(null);
  const getModuleData = async (page, perPage, filterData, sortingData) => {
    // if (!Object.keys(filterData).length){
    filterData = {
      // ...filterData, 
      course_id: ClassCourseEditData.course_id,
      organization_grade_level_id: ViewManageClassData.organization_grade_level_id
      // }
    }
    await dispatch(getAllTopics({ page: page, perPage: perPage, filter: isJSON(filterData), sorting: isJSON(sortingData) }));
  }

  useEffect(() => {
    if (userRoles && teacherData?.to_date) {
      const today = new Date();
      const toDate = new Date(teacherData.to_date);

      // If toDate is before today, editing is not allowed
      if (toDate < today) {
        setCanEdit(false);
      } else {
        setCanEdit(true);
      }
    }
  }, [teacherData]);

  useEffect(() => {
    dispatch(updateSortFilters({
      filter: {
        course_id: ClassCourseEditData.course_id,
        organization_grade_level_id: ViewManageClassData.organization_grade_level_id
      }, sorting: {}
    }));
    getModuleData(topicPagination.current, topicPagination.pageSize, filter, sorting)
  }, []);

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, filter, sorting);
  }

  /**add new Topic */
  const addTopic = () => {
    dispatch(setOpenModal())
  }
  /** update data  */
  const handleEdit = (record) => {
    dispatch(setEditData(record));
  };
  /**delete created Topics */
  const handleDelete = async (id) => {
    await dispatch(deleteTopic(id)).then((response) => {
      getModuleData(topicPagination.current, topicPagination.pageSize, filter, sorting);
    })
  };
  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) => handleOnChange(dataIndex, (e.target.value ? e.target.value : ''), confirm)}
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: 'block',
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('search')}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex)
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale('reset')}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filter[dataIndex] && filter[dataIndex] !== '' ? '#1677ff' : undefined,
        }}
      />
    ),
    onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex]
        ? (
          <Highlighter
            highlightStyle={{
              backgroundColor: '#ffc069',
              padding: 0,
            }}
            searchWords={[filter[dataIndex]]}
            autoEscape
            textToHighlight={text ? text.toString() : ''}
          />
        ) : (
          text
        ),
  })
  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, topicPagination.pageSize, filter, sorting);
  }
  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(topicPagination.current, topicPagination.pageSize, newObject, sorting);
  }
  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === '') {
      confirm();
      getModuleData(topicPagination.current, topicPagination.pageSize, { ...filter, [dataIndex]: value }, sorting);
    }
  }
  const columns = [
    {
      title: setLocale("name"),
      dataIndex: "name",
      key: "name",
      ...getColumnSearchProps("name"),
      sorter: true,
    }, {
      title: 'Course',
      dataIndex: "course",
      key: "course",
    }, {
      title: 'Grade Level',
      dataIndex: "grade_level",
      key: "grade_level",
    },
    {
      title: setLocale("operation"),
      dataIndex: "operation",
      render: (data, record) => (
        canEdit ? (
          <Space>
            <Popconfirm
              title={setLocale("sure_to_delete")}
              onConfirm={() => handleDelete(record.id)}
              okText={setLocale("Yes")}
              cancelText={setLocale("No")}
            >
              <Button type="link" danger>
                <DeleteOutlined className="text-danger" />
              </Button>
            </Popconfirm>
            <Button type="link" onClick={() => handleEdit(record)}>
              <EditOutlined className="text-success" />
            </Button>
          </Space>
        ) : null
      ),
    }

  ];
  return (
    <>
      <div className="code-box">
        <section className="code-box-demo">
          {canEdit &&
            <Button className='ant-btn ant-btn-sm float-right' type="primary" onClick={() => { addTopic() }}>
              Add Topic
            </Button>
          }

          <Table
            loading={tableLoading}
            dataSource={topics}
            columns={columns}
            rowKey={(record) => record.id}
            pagination={false}
          />
          <Pagination
            style={{ margin: '16px', float: 'right' }}
            current={topicPagination.current}
            pageSize={topicPagination.pageSize}
            total={topicPagination.total}
            onChange={handlePageChange}
          />
          {openTopicModal ? <AddTopicModal ></AddTopicModal> : null}
        </section>
      </div>
    </>
  );
}

export default Topics;

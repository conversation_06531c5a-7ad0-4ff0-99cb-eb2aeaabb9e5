import React, { useEffect, useState } from 'react';
import { Button, Col, Form, Input, Row, Space, Select, Divider, DatePicker, Skeleton } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { INCIDENT_ACTION_CODE, INCIDENT_REPORT, SCHOOL_YEAR } from "constants/AppConstants";
import {
  getIncidentActions,
  saveIncidentAction,
  saveParticipantAction,
  viewIncidentReport,
} from "store/slices/IncidentReport/manageIncidentReportSlice.js";
import {
  getIncidentActioncode,
} from "store/slices/IncidentActionCode/manageIncidentActionCodeSlice.js";
import { useParams } from "react-router-dom";
import IntlMessage from "components/util-components/IntlMessage";
import { DATE_FORMAT_YYYY_MM_DD } from "constants/DateConstant";
import moment from 'moment';
import 'moment-timezone';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;
const { RangePicker } = DatePicker;


const IncidentActionForm = ({ submitForm }) => {
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const params = useParams();
  const {
    IncidentParticipantFormStatus,
    ViewIncidentReportData,
    otherParticipant,
    participantData,
    IncidentReportButtonAndModelLabel,
    ParticipantActionButtonSpinner,
    incidentActions,
    ActionButtonSpinner,
    IncidentReportEditData,
  } = useSelector(
    (state) => state[INCIDENT_REPORT]
  );
  const { SchoolYearResult } = useSelector((state) => state[SCHOOL_YEAR]);

  const [hideFields, setHideFields] = useState(true);

  const disabledDate = (current) => {
    // Get the current date
    const currentDate = moment();

    const validSchoolYears = SchoolYearResult?.data?.filter(item => {
      const start = moment(item.start_date);
      const end = moment(item.end_date);
      return currentDate >= start && currentDate <= end;
    });

    // Check if current date is within the valid school years
    return !validSchoolYears.some(item => {
      const start = moment(item.start_date);
      // const end = moment(item.end_date);
      const end = currentDate;
      return current >= start && current <= end;
    });
  };

  const { IncidentActioncodeResult } = useSelector(
    (state) => state[INCIDENT_ACTION_CODE]
  );

  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };


  const fetchData = async () => {
    await dispatch(getIncidentActions({ id: params ? params?.id : null })).then((result) => {
      let incidentActionIds = result?.payload?.map((item) => item.incident_action_code_id);
      incidentActionIds = [...new Set(incidentActionIds)];
      dispatch(getIncidentActioncode({ incidentActionIds: incidentActionIds }));
    });
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleFormSubmit = async (formValues) => {
    formValues.participantData = participantData;
    formValues.other = otherParticipant;
    if (IncidentReportEditData && IncidentReportEditData.id) {
      // If editing, include the id in the form values
      formValues.incident_id = IncidentReportEditData.id;
    }

    await dispatch(saveParticipantAction(formValues))
  };

  const durationCode = [
    {
      label: '1 Day',
      value: '1'
    },
    {
      label: '2 Days',
      value: '2'
    },
    {
      label: '3 Days',
      value: '3'
    },
    {
      label: '4 Days',
      value: '4'
    },
    {
      label: '5 Day',
      value: '5'
    },
    {
      label: '6 Days',
      value: '6'
    },
    {
      label: '7 Days',
      value: '7'
    },
    {
      label: '8 Days',
      value: '8'
    },
    {
      label: '9 Day',
      value: '9'
    },
    {
      label: '10 Days',
      value: '10'
    },
    {
      label: '11 Days',
      value: '11'
    },
    {
      label: '12 Days',
      value: '12'
    },
  ];

  const handleAddAction = async (formValues) => {
    if (IncidentReportEditData && IncidentReportEditData.id) {
      // If editing, include the id in the form values
      formValues.incident_id = IncidentReportEditData.id;
    }
    await dispatch(saveIncidentAction(formValues)).then((result) => {
      form2.resetFields();
      setHideFields(true);
      fetchData();
    });
  }


  const handleActionCodeChange = (value) => {
    const data = IncidentActioncodeResult?.data.filter(item => item.id === value)
    if (data && data[0]?.effects_attendance === 1) {
      setHideFields(false);
    } else {
      setHideFields(true);
    }
  }
  return (
    <>
      <Row gutter={16}>
        <Col span={24} style={{ textAlign: "center" }}>
          <h2>
            What action was taken for this incident?
          </h2>
        </Col>
        <Col span={24}>

          <Skeleton active loading={loading}>
            <Row gutter={16}>
              <Col span={12}>

                <Form
                  layout="vertical"
                  onFinish={handleAddAction}
                  form={form2}
                  onFinishFailed={onFinishFailed}
                  autoComplete="off"
                >

                  <Row gutter={16}>

                    <Col span={24}>
                      <h4>{setLocale("incidentReport.add-incident-action")}</h4>
                    </Col>

                    <Col span={16}>
                      <Form.Item
                        name="incident_action_code_id"
                        label={setLocale('incidentReport.incident_action_code_id')}
                        rules={[
                          {
                            required: true,
                            message: setLocale('incidentReport.incident_action_code_idError'),
                          },
                        ]}
                      // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                      // help={IncidentReportShowMessage && IncidentReportErrors.name}
                      >
                        <Select
                          className="rounded-0"
                          showSearch
                          optionLabelProp="label"
                          allowClear
                          onChange={(record) => handleActionCodeChange(record)}
                          optionFilterProp="children"
                          filterOption={(input, option) => (
                            (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                          )}
                          filterSort={(optionA, optionB) =>
                            (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                          }
                          options={IncidentActioncodeResult?.data ?? []}

                        />
                      </Form.Item>
                    </Col>

                    {!hideFields &&
                      <>
                        <Col span={8}>
                          <Form.Item
                            name="duration_code"
                            label={setLocale('incidentReport.duration_code')}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.duration_codeError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >
                            <Select
                              className="rounded-0"
                              showSearch
                              optionLabelProp="label"
                              allowClear
                              optionFilterProp="children"
                              filterOption={(input, option) => (
                                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                              )}
                              filterSort={(optionA, optionB) =>
                                (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                              }
                              options={durationCode ?? []}

                            />
                          </Form.Item>
                        </Col>

                        <Col span={16}>
                          <Form.Item
                            name="date_from_to"
                            label={setLocale('incidentReport.date_from_to')}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.date_from_toError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >

                            <DatePicker
                              disabledDate={disabledDate}
                              className="rounded-0 w-100"
                              format={DATE_FORMAT_YYYY_MM_DD}
                            />
                            {/* <RangePicker
                              disabledDate={disabledDate}
                              format={DATE_FORMAT_YYYY_MM_DD}
                              style={{ width: '100%' }}
                            /> */}
                          </Form.Item>

                        </Col>
                        {/* <Col span={8}>
                        <Form.Item
                          name="date_from"
                          label={setLocale('incidentReport.date_from')}
                          rules={[
                            {
                              required: true,
                              message: setLocale('incidentReport.date_fromError'),
                            },
                          ]}
                        // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                        // help={IncidentReportShowMessage && IncidentReportErrors.name}
                        >
                          <DatePicker
                            disabledDate={disabledDate}
                            format={DATE_FORMAT_YYYY_MM_DD} className="rounded-0 w-100" />
                        </Form.Item>
                      </Col>

                      <Col span={8}>
                        <Form.Item
                          name="date_to"
                          label={setLocale('incidentReport.date_to')}
                          rules={[
                            {
                              required: true,
                              message: setLocale('incidentReport.date_toError'),
                            },
                          ]}
                        // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                        // help={IncidentReportShowMessage && IncidentReportErrors.name}
                        >
                          <DatePicker
                            disabledDate={disabledDate}
                            format={DATE_FORMAT_YYYY_MM_DD} className="rounded-0 w-100" />
                        </Form.Item>
                      </Col> */}
                      </>
                    }

                    <Col span={8}>
                      <Form.Item
                        name="action_resolution_date"
                        label={setLocale('incidentReport.action_resolution_date')}
                        rules={[
                          {
                            required: true,
                            message: setLocale('incidentReport.action_resolution_dateError'),
                          },
                        ]}
                      // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                      // help={IncidentReportShowMessage && IncidentReportErrors.name}
                      >
                        <DatePicker
                          disabledDate={disabledDate}
                          format={DATE_FORMAT_YYYY_MM_DD} className="rounded-0 w-100" />
                      </Form.Item>
                    </Col>

                    <Col span={24}>
                      <Form.Item
                        name="action_detail"
                        label={setLocale('incidentReport.action_detail')}
                        rules={[
                          {
                            required: false,
                            message: setLocale('incidentReport.action_detailError'),
                          },
                        ]}
                      // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                      // help={IncidentReportShowMessage && IncidentReportErrors.name}
                      >
                        <TextArea rows={4}></TextArea>
                      </Form.Item>
                    </Col>

                  </Row>

                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={ActionButtonSpinner}
                    >
                      {setLocale('incidentReport.add_action')}
                    </Button>
                  </Space>
                </Form>

              </Col>

              <Col span={12}>

                <Form
                  layout="vertical"
                  onFinish={handleFormSubmit}
                  form={form}
                  onFinishFailed={onFinishFailed}
                  // onValuesChange={handleFormChange}
                  autoComplete="off"
                  initialValues={{
                    name: IncidentReportEditData?.name,
                  }}
                >

                  <div>
                    <h4>{setLocale("Selected Participants")}</h4>
                  </div>
                  <br></br>
                  <Row gutter={16}>
                    {participantData?.staff && Object.keys(participantData?.staff).length > 0 && (
                      <Divider orientation="left" plain>
                        <strong>{setLocale('incidentReport.staff')}</strong>
                      </Divider>
                    )}
                    {participantData?.staff && Object.keys(participantData?.staff).length > 0 && participantData.staff.map((item, index) => (
                      <React.Fragment key={item.user_id}>
                        <Col span={8}>
                          <div style={{ width: "50%", float: "left", textAlign: "left" }}>
                            {item.name}
                          </div>
                        </Col>
                        <Col span={8}>
                          <div style={{ width: "50%", float: "left", textAlign: "left" }}>
                            {item?.participant_role}
                          </div>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={"staff_participant_action_" + item.user_id}
                            initialValue={item?.incident_action_id ?? ''}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.selectActionError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >
                            <Select
                              placeholder={setLocale('incidentReport.selectAction')}
                              className="rounded-0"
                              showSearch
                              optionLabelProp="label"
                              value={item?.incident_action_id ?? ''}
                              allowClear
                              optionFilterProp="children"
                              filterOption={(input, option) => (
                                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                              )}
                              filterSort={(optionA, optionB) =>
                                (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                              }
                              options={incidentActions ?? []}

                            />
                          </Form.Item>
                        </Col>
                      </React.Fragment>
                    ))}

                    {participantData?.student && Object.keys(participantData?.student).length > 0 && (
                      <>
                        <Divider orientation="left" plain>
                          <strong>{setLocale('incidentReport.students')}</strong>
                        </Divider>
                      </>
                    )}
                    {participantData?.student && Object.keys(participantData?.student).length > 0 && participantData.student.map((item, index) => (
                      <React.Fragment key={item.student_id}>
                        <Col span={8}>
                          <div style={{ float: "left", textAlign: "left" }}>
                            {item.name}
                          </div>
                        </Col>
                        <Col span={8}>
                          <div style={{ width: "50%", float: "left", textAlign: "left" }}>
                            {item?.participant_role}
                          </div>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            initialValue={item?.incident_action_id ?? ''}
                            name={"student_participant_action_" + item.student_id}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.selectActionError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >
                            <Select
                              className="rounded-0"
                              placeholder={setLocale('incidentReport.selectAction')}
                              value={item?.incident_action_id ?? ''}
                              showSearch
                              optionLabelProp="label"
                              allowClear
                              optionFilterProp="children"
                              filterOption={(input, option) => (
                                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                              )}
                              filterSort={(optionA, optionB) =>
                                (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                              }
                              options={incidentActions ?? []}

                            />
                          </Form.Item>
                        </Col>
                      </React.Fragment>
                    ))}

                    {Object.keys(otherParticipant).length > 0 && (
                      <>
                        <Divider orientation="left" plain>
                          <strong>{setLocale('incidentReport.other')}</strong>
                        </Divider>
                      </>
                    )}
                    {Object.keys(otherParticipant).length > 0 && otherParticipant?.map((item, index) => (
                      <React.Fragment key={index}>
                        <Col span={8}>
                          <div style={{ float: "left", textAlign: "left" }}>
                            {item.others_first_name} {item.others_last_name}
                          </div>
                        </Col>
                        <Col span={8}>
                          <div style={{ width: "50%", float: "left", textAlign: "left" }}>
                            {item?.participant_role}
                          </div>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={"other_participant_action_" + index}
                            initialValue={item?.incident_action_id ?? ''}
                            rules={[
                              {
                                required: true,
                                message: setLocale('incidentReport.selectActionError'),
                              },
                            ]}
                          // validateStatus={IncidentReportShowMessage && IncidentReportErrors.name ? "error" : ""}
                          // help={IncidentReportShowMessage && IncidentReportErrors.name}
                          >
                            <Select
                              className="rounded-0"
                              placeholder={setLocale('incidentReport.selectAction')}
                              showSearch
                              optionLabelProp="label"
                              value={item?.incident_action_id ?? ''}
                              allowClear
                              optionFilterProp="children"
                              filterOption={(input, option) => (
                                (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                              )}
                              filterSort={(optionA, optionB) =>
                                (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                              }
                              options={incidentActions ?? []}

                            />
                          </Form.Item>
                        </Col>
                      </React.Fragment>
                    ))}

                  </Row>

                  {
                    (participantData?.staff && Object.keys(participantData?.staff).length > 0) ||
                      (participantData?.student && Object.keys(participantData?.student).length > 0) ||
                      (Object.keys(otherParticipant).length > 0) ? (
                      <>
                        <Space>
                          <Button
                            type="primary"
                            htmlType="submit"
                            loading={ParticipantActionButtonSpinner}
                          >
                            {IncidentReportButtonAndModelLabel}
                          </Button>
                        </Space>
                      </>
                    ) : ''
                  }
                </Form>
              </Col>

            </Row>

          </Skeleton>

        </Col>
      </Row>
    </>
  );
};
export default IncidentActionForm;


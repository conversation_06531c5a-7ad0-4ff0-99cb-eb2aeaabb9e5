import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  Popconfirm,
  Pagination,
  Button,
  Input,
  Space,
  Breadcrumb,
  Skeleton,
  Select,
  Tooltip,
  Badge,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { CLASS_WISE, SCHOOL_YEAR } from "constants/AppConstants";
import { env } from "configs/EnvironmentConfig"
import AddClasswiseModal from "./Modals/index";
import {
  ClasswiseAddDrawerStatus,
  ClasswiseEditWithDrawerStatus,
  deleteClasswise,
  getClasswise,
  updateSortFilters,
  setColumnSearch,
  exportStudentsClassWise,
} from "store/slices/ClassWise/manageClassWiseSlice";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import { USER_INFORMATION } from "../../../constants/AuthConstant";
import AvatarStatus from "components/composeable/AvatarStatus";

const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const schoolYear = userInformation.school_year;

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
  const dispatch = useDispatch();
  const searchInput = useRef(null);

  const { defaultSchoolYear } = useSelector((state) => state[SCHOOL_YEAR]);

  const [selectedColumns, setSelectedColumns] = useState([
    {
      value: "full_name",
      label: "Student Name",
    },
    {
      value: "guardian_name",
      label: "Guardian Name",
    },
    {
      value: "guardian_cell_phone",
      label: "Guardian Cell Phone",
    },
    {
      value: "class_name",
      label: "Class",
    },
    {
      value: "allergies",
      label: "Allergies",
    },
  ]);
  const [columnOptions, setColumnOptions] = useState([]);

  const handleOpenModal = () =>
    dispatch(ClasswiseAddDrawerStatus({ errorStatus: 1, status: true }));
  const {
    ClasswiseAddDrawer,
    ClasswiseResult,
    tablePagination,
    sorting,
    filter,
    ClasswiseTableLoading,
    exportButtonLoader,
    permission,
  } = useSelector((state) => state[CLASS_WISE]);

  const getModuleData = async (page, perPage, filterData, sortingData) => {
    console.log(defaultSchoolYear);
    await dispatch(
      getClasswise({
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
        organization_id: selectedOrganization,
        schoolYear: defaultSchoolYear ? defaultSchoolYear : schoolYear.id,
      })
    );
  };

  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, tablePagination.pageSize, filter, sorting);
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      newObject,
      sorting
    );
  };

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === "") {
      confirm();
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        { ...filter, [dataIndex]: value },
        sorting
      );
    }
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  useEffect(() => {
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      filter,
      sorting
    );
    setColumnOptions(columns_options);
  }, []);

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, filter, sorting);
  };

  const handleDelete = (record) => {
    dispatch(deleteClasswise(record.id)).then(() => {
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        filter,
        sorting
      );
    });
  };

  const handleUpdate = (record) => {
    dispatch(ClasswiseEditWithDrawerStatus({ errorStatus: 1, data: record }));
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
    };

    try {
      await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
      getModuleData(1, tablePagination.pageSize, filter, sorting);
    } catch (error) {
      console.log(error);
    }
  };

  const handleExport = () => {
    dispatch(exportStudentsClassWise({ selectedColumns: selectedColumns }));
  };

  const onColumnsChange = (value, opions) => {
    setSelectedColumns(opions);
  };

  const columns_options = [
    {
      value: "full_name",
      label: "Student Name",
    },
    {
      value: "class_name",
      label: "Class",
    },
    {
      value: "grade",
      label: "Grade",
    },
    {
      value: "student_email",
      label: "Student Email",
    },
    {
      value: "birth_date",
      label: "Date of Birth",
    },
    {
      value: "place_of_birth",
      label: "Place of Birth",
    },
    {
      value: "gender",
      label: "Gender",
    },
    {
      value: "nationality",
      label: "Nationality",
    },
    {
      value: "guardian_name",
      label: "Guardian Name",
    },
    {
      value: "guardian_address_1",
      label: "Guardian Address 1",
    },
    {
      value: "guardian_address_2",
      label: "Guardian Address 2",
    },
    {
      value: "guardian_postal_code",
      label: "Guardian Postal Code",
    },
    {
      value: "guardian_home_phone",
      label: "Guardian Home Phone",
    },
    {
      value: "guardian_work_phone",
      label: "Guardian Work Phone",
    },
    {
      value: "guardian_cell_phone",
      label: "Guardian Cell Phone",
    },
    {
      value: "allergies",
      label: "Allergies",
    },
    {
      value: "medical_conditions",
      label: "Medical Conditions",
    },
    {
      value: "family_dr_name",
      label: "Family Doctor",
    },
    {
      value: "health_card_number",
      label: "Health Card",
    },
    {
      value: "family_dr_phone",
      label: "Doctor Phone",
    },
    {
      value: "dentist_name",
      label: "Dentist",
    },
    {
      value: "dentist_phone",
      label: "Dentist Phone",
    },
    {
      value: "dietary_needs",
      label: "Dietary Needs",
    },
    {
      value: "other_information",
      label: "Other Info",
    },
    {
      value: "student_id",
      label: "Student Id",
    },
    {
      value: "provincial_student_number",
      label: "ASN/OEN",
    },
    {
      value: "legal_status",
      label: "Legal Status",
    },
    {
      value: "home_phone",
      label: "Mother's Cell",
    },
    {
      value: "work_phone",
      label: "Father's Cell",
    },
    {
      value: "cell_phone",
      label: "Emergency Contact",
    },
    {
      value: "profile_picture",
      label: "Profile Picture",
    },
    {
      value: "status_date",
      label: "Enrollment Date",
    },
  ];

  const noSearchColumns = [
    "guardian_home_phone",
    "guardian_work_phone",
    "guardian_cell_phone",
    "family_dr_phone",
    "dentist_phone",
    "home_phone",
    "work_phone",
    "cell_phone",
    "profile_picture"
  ];

  const columns = [
    ...selectedColumns.map((col) => {
      let columnConfig = {
        title: setLocale(col.label),
        dataIndex: col.value,
        key: col.value,
        sorter: true,
      };

      if (!noSearchColumns.includes(col.value)) {
        columnConfig = {
          ...columnConfig,
          ...getColumnSearchProps(col.value),
        };
      }

      if (col.value === "full_name") {
        columnConfig.render = (data, record) => (
          <div key={record.id}>
            <AvatarStatus
              src={
                record.profile_picture
                  ? env.FILE_ENDPOINT_URL + record.profile_picture
                  : env.img
              }
              name={
                // <Tooltip title={record.full_name} color="green" key="green">
                <Badge
                  count={record.full_name}
                  style={{ backgroundColor: "#108ee9" }}
                />
                // </Tooltip>
              }
              subTitle={""}
            />
          </div>
        );
      }

      return columnConfig;
    }),
  ];

  return (
    <>
      <Breadcrumb className="my-2 mx-2">
        <Breadcrumb.Item>
          <Link to="/app/default">{setLocale("home")}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{setLocale("Class Wise")}</Breadcrumb.Item>
      </Breadcrumb>
      <>
        <div className="code-box">
          <section className="code-box-demo">
            {/* {permission.includes("Create") && (
                            <Button
                                className="ant-btn-round ant-btn-sm"
                                type="primary"
                                style={{ float: "right", margin: "5px" }}
                                onClick={handleOpenModal}
                            >
                                {setLocale('classwise.add')}
                            </Button>
                        )} */}
            <Button
              className="ant-btn-round ant-btn-sm"
              loading={exportButtonLoader}
              onClick={handleExport}
              type="primary"
              style={{ float: "right", margin: "5px" }}
            >
              {setLocale("Get Excel")}
            </Button>
            <Select
              className="rounded-0 float-right"
              style={{ width: "250px" }}
              showSearch
              maxTagCount={1}
              optionLabelProp="label"
              placeholder={setLocale("Columns")}
              allowClear
              mode="multiple"
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? "").toLowerCase().includes(input)
              }
              filterSort={(optionA, optionB) =>
                (optionA?.label ?? "")
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? "").toLowerCase())
              }
              value={selectedColumns}
              onChange={onColumnsChange}
              options={
                columnOptions.map((clm) => ({
                  value: clm.value,
                  label: clm.label,
                })) || []
              }
            ></Select>
          </section>
          {ClasswiseAddDrawer && <AddClasswiseModal />}
          <section className="code-box-description">
            <Table
              onChange={handleTableChange}
              columns={columns}
              loading={ClasswiseTableLoading}
              rowKey={(record) => record.id}
              dataSource={ClasswiseResult.data ?? []}
              pagination={false}
              style={{
                overflowX: "scroll",
              }}
            />
            <Pagination
              style={{ margin: "16px", float: "right" }}
              current={tablePagination.current}
              pageSize={tablePagination.pageSize}
              total={tablePagination.total}
              showTotal={(total, range) =>
                `${range[0]}-${range[1]} of ${total} Records`
              }
              pageSizeOptions={["10", "20", "50", "100", "1000"]}
              showQuickJumper
              onChange={handlePageChange}
            />
          </section>
        </div>
      </>
    </>
  );
}

export default Index;

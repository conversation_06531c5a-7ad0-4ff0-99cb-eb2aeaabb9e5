import React, { useState, useEffect } from "react";
import MailData from "assets/data/mail.data.json";
import { ReplySVG } from "assets/svg/icon";
import { labels, getFileType } from "./MailLabels";
import AvatarStatus from "components/shared-components/AvatarStatus";
import { Tooltip, Skeleton, Popconfirm } from "antd";
import {
  LeftCircleOutlined,
  StarOutlined,
  DeleteOutlined,
  StarFilled,
  DownloadOutlined,
  MailOutlined,
} from "@ant-design/icons";
import { useParams, useNavigate } from "react-router-dom";
import CustomIcon from "components/util-components/CustomIcon";
import { useDispatch, useSelector } from "react-redux";
import { COMMUNICATION } from "constants/AppConstants";
import {
  getCommunicationMail,
  deleteCommunicationMail,
} from "store/slices/Communication/manageCommunicationSlice";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const MaiDetail = () => {
  const dispatch = useDispatch();
  const [detail, setDetail] = useState({});
  const [starred, setStarred] = useState(false);
  const [attachment, setAttachment] = useState([]);
  const { getCommunicationMailResult } = useSelector(
    (state) => state[COMMUNICATION]
  );
  const [pageLoading, setPageLoading] = useState(true);

  const params = useParams();
  const navigate = useNavigate();

  const tick = () => {
    setStarred(!starred);
  };

  useEffect(() => {
    fetchData();
  }, [dispatch]);

  const fetchData = async () => {
    try {
      await dispatch(
        getCommunicationMail({
          category: params.category,
          id: params.id,
          detail: 1,
        })
      ).then((resp) => {
        const { category, id } = params;
        const currentId = parseInt(id);
        let data = [];
        if (labels.includes(category)) {
          data = getCommunicationMailResult.inbox.filter(
            (elm) => elm.id === currentId
          );
        } else {
          let response = resp.payload?.data;
          data = response[category];
        }
        const res = data[0];
        setDetail(res);
        setStarred(res.starred);
        setAttachment(res.attachment);
        setPageLoading(false);
      });
    } catch (error) {
      console.error("Error fetching school year data:", error);
    }
  };

  const handleDeleteMail = (id) => {
    dispatch(deleteCommunicationMail(id)).then((resp) => {
      navigate(-1);
    });
  };

  const back = () => {
    navigate(-1);
  };

  const { id, name, avatar, title, date, to, content, reason } = detail;

  return (
    <Skeleton active loading={pageLoading}>
      <div className="mail-detail">
        <div className="d-lg-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center mb-3">
            <div
              className="font-size-md mr-3"
              onClick={() => {
                back();
              }}
            >
              <LeftCircleOutlined className="mail-detail-action-icon font-size-md ml-0" />
            </div>
            <AvatarStatus
              src={avatar}
              name={name}
              subTitle={
                <>
                  <MailOutlined /> {" " + to}
                </>
              }
            />
          </div>
          <div className="mail-detail-action mb-3">
            <span className="mr-2 font-size-md">{date}</span>
            {/* <Tooltip title="Reply">
						<CustomIcon className="mail-detail-action-icon" svg={ReplySVG} />
					</Tooltip>
					<Tooltip title="Star" onClick={() => { tick() }}>
						{starred ? <StarFilled className="mail-detail-action-icon star checked" /> : <StarOutlined className="mail-detail-action-icon star" />}
					</Tooltip> */}
            {attachment.length > 0 ? (
              <Tooltip title="Download Attachment">
                <DownloadOutlined className="mail-detail-action-icon" />
              </Tooltip>
            ) : null}
            {/* <Popconfirm
              title={setLocale("sure_to_delete_email")}
              onConfirm={(e) => handleDeleteMail(id)}
            >
              <DeleteOutlined className="mail-detail-action-icon" />
            </Popconfirm> */}
            {/* <Tooltip title="Delete">
						<DeleteOutlined className="mail-detail-action-icon" onClick={() => handleDeleteMail(id)} />
					</Tooltip> */}
          </div>
        </div>
        <div className="mail-detail-content">
          <h3 className="mb-4">{title}</h3>
          <div dangerouslySetInnerHTML={{ __html: content }} />
          {/* <div>
            <h3 className="mt-4">{reason}</h3>
          </div> */}
          <div className="mail-detail-attactment">
            {attachment.map((elm, i) => (
              <div
                className="mail-detail-attactment-item"
                key={`attachment-file-${i}`}
              >
                <span>{getFileType(elm.type)}</span>
                <div className="ml-2">
                  <div>{elm.file}</div>
                  <div className="text-muted font-size-sm">{elm.size}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Skeleton>
  );
};

export default MaiDetail;

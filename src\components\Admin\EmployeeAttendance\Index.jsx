import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Table,
  Popconfirm,
  Pagination,
  Button,
  Input,
  Space,
  Breadcrumb,
  Badge,
  Tooltip,
  Skeleton,
  Select,
  DatePicker,
  Row,
  Col,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { EMPLOYEE_ATTENDANCE } from "constants/AppConstants";
import AddEmployeeAttendanceModal from "./Modals/index";
import {
  EmployeeAttendanceAddDrawerStatus,
  EmployeeAttendanceEditWithDrawerStatus,
  deleteEmployeeAttendance,
  getEmployeeAttendance,
  updateSortFilters,
  setColumnSearch,
  // getAllUsers,
  exportEmployeeAttendance
} from "store/slices/EmployeeAttendance/manageEmployeeAttendanceSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { RangePicker } = DatePicker;

function Index() {
  const dispatch = useDispatch();
  const searchInput = useRef(null);
  const handleOpenModal = () =>
    dispatch(
      EmployeeAttendanceAddDrawerStatus({ errorStatus: 1, status: true })
    );
  const [attendanceOptions, setAttendanceOptions] = useState([
    { label: "Marked", value: "marked" },
    { label: "Unmarked", value: "unmarked" },
  ]);
  const [selectedAttendanceOptions, setSelectedAttendanceOptions] =
    useState("marked");
  const {
    EmployeeAttendanceAddDrawer,
    EmployeeAttendanceResult,
    tablePagination,
    sorting,
    filter,
    EmployeeAttendanceTableLoading,
    permission,
    ExportExcelAttendanceButtonSpinner
  } = useSelector((state) => state[EMPLOYEE_ATTENDANCE]);

  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getEmployeeAttendance({
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
      })
    );
  };

  const handleSearch = async (confirm) => {
    confirm();
    getModuleData(1, tablePagination.pageSize, filter, sorting);
  };

  const handleReset = async (dataIndex) => {
    const { [dataIndex]: removedProperty, ...newObject } = filter;
    await dispatch(setColumnSearch(newObject));
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      newObject,
      sorting
    );
  };

  const handleOnChange = async (dataIndex, value, confirm) => {
    await dispatch(setColumnSearch({ ...filter, [dataIndex]: value }));
    if (value === "") {
      confirm();
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        { ...filter, [dataIndex]: value },
        sorting
      );
    }
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder=""
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e.target.value ? e.target.value : "",
              confirm
            )
          }
          onPressEnter={(e) => handleSearch(confirm)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  const getColumnSearchDropDownProps = (dataIndex, options) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Select
          ref={searchInput}
          placeholder={setLocale("Select")}
          autoFocus
          value={filter[dataIndex] ? filter[dataIndex] : selectedKeys[0]}
          onChange={(e) =>
            handleOnChange(
              dataIndex,
              e,
              confirm
            )
          }
          options={options}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={(e) => handleSearch(confirm)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && handleReset(dataIndex);
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {setLocale("reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color:
            filter[dataIndex] && filter[dataIndex] !== ""
              ? "#1677ff"
              : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    render: (text) =>
      filter[dataIndex] ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: "#ffc069",
            padding: 0,
          }}
          searchWords={[filter[dataIndex]]}
          autoEscape
          textToHighlight={text ? text.toString() : ""}
        />
      ) : (
        text
      ),
  });

  useEffect(() => {
    // dispatch(getAllUsers());
    getModuleData(
      tablePagination.current,
      tablePagination.pageSize,
      { ...filter, attendance_option: selectedAttendanceOptions },
      sorting
    );
  }, []);

  const handlePageChange = (page, pageSize) => {
    getModuleData(page, pageSize, filter, sorting);
  };

  const handleDelete = (record) => {
    dispatch(deleteEmployeeAttendance(record.id)).then(() => {
      getModuleData(
        tablePagination.current,
        tablePagination.pageSize,
        filter,
        sorting
      );
    });
  };

  const handleUpdate = (record) => {
    dispatch(
      EmployeeAttendanceEditWithDrawerStatus({ errorStatus: 1, data: record })
    );
  };

  const handleTableChange = async (pagination, filters, sorter) => {
    const sortOrder = sorter.order;
    const sorting = {
      [sorter.field]: sortOrder === "ascend" ? "asc" : "desc",
    };

    try {
      await dispatch(updateSortFilters({ filter: filter, sorting: sorting }));
      getModuleData(1, tablePagination.pageSize, filter, sorting);
    } catch (error) {
      console.log(error);
    }
  };

  const attendanceOptionOnChange = async (value) => {
    setSelectedAttendanceOptions(value);
    await dispatch(
      updateSortFilters({
        filter: { attendance_option: value },
        sorting: {},
      })
    );
    // await getModuleData(1, tablePagination.pageSize, filter, sorting);
  };

  const onAttendanceDateChange = async (value, dateString) => {
    await dispatch(
      updateSortFilters({
        filter: { ...filter, attendance_date: dateString },
        sorting: sorting,
      })
    );
    // await getModuleData(1, tablePagination.pageSize, filter, sorting);
  };

  const onExportDateChange = async (value, dateString) => {
    await dispatch(
      updateSortFilters({
        filter: { ...filter, export_date: dateString },
        sorting: sorting,
      })
    );
  };

  const handleExport = async () => {
    await dispatch(exportEmployeeAttendance({
      filter: filter,
      sorting: sorting
    }));
  }

  const columns = [
    ...(selectedAttendanceOptions === "marked"
      ? [
        {
          title: setLocale("name"),
          dataIndex: "name",
          key: "name",
          sorter: true,
          ...getColumnSearchProps("name"),
        },
        {
          title: setLocale("status"),
          dataIndex: "status",
          key: "status",
          sorter: true,
          ...getColumnSearchDropDownProps("status", [
            {
              label: "Checked In",
              value: 'checked-in',
            },
            {
              label: "Checked Out",
              value: 'checked-out',
            },
          ]),
          render: (data, record) => (
            <Badge
              className="site-badge-count-109"
              count={record.status === 1 ? "Checked In" : "Checked Out"}
              style={{
                backgroundColor: record.status === 1 ? "#52c41a" : "",
              }}
            />
          ),
        },
        {
          title: setLocale("Attendance Time"),
          dataIndex: "att_date_time",
          key: "att_date_time",
          sorter: true,
          ...getColumnSearchProps("att_date_time"),
        },
        {
          title: setLocale("Source"),
          dataIndex: "machine_name",
          key: "machine_name",
          sorter: true,
          ...getColumnSearchProps("machine_name"),
          render: (data, record) => (
            <Tooltip
              title={
                record.machine_mac_address
                  ? record.machine_mac_address
                  : "Manual"
              }
              color="green"
              key="green"
            >
              <Badge
                count={record.machine_name ? record.machine_name : "Manual"}
                style={{ backgroundColor: "#108ee9" }}
              />
            </Tooltip>
          ),
        },
        {
          title: setLocale("operation"),
          key: "action",
          render: (data, record) => (
            <>
              {permission.includes("Delete") && (
                <Popconfirm
                  title={setLocale("sure_to_delete")}
                  onConfirm={(e) => handleDelete(record)}
                >
                  <DeleteOutlined
                    style={{ fontSize: "15px" }}
                    className="text-danger"
                  />{" "}
                  &nbsp;
                </Popconfirm>
              )}
              {permission.includes("Update") && (
                <EditOutlined
                  style={{ fontSize: "15px", marginRight: "9px" }}
                  className="text-success"
                  onClick={(e) => handleUpdate(record)}
                />
              )}
              {/* {permission.includes("View") && (
                            <Link to={`../../app/employee-attendance_view/${record.enc_id}`}>
                                <EyeOutlined style={{ fontSize: '15px' }} className="text-primary" />
                            </Link>
                        )} */}
            </>
          ),
        },
      ]
      : [
        {
          title: setLocale("name"),
          dataIndex: "name",
          key: "name",
          sorter: true,
          ...getColumnSearchProps("name"),
        },
        {
          title: setLocale("status"),
          dataIndex: "status",
          key: "status",
          sorter: true,
          ...getColumnSearchProps("status"),
          render: (data, record) => (
            <Badge className="site-badge-count-109" count="Not Marked" />
          ),
        },
        {
          title: setLocale("email"),
          dataIndex: "email",
          key: "email",
          sorter: true,
          ...getColumnSearchProps("email"),
        },
        {
          title: setLocale("address.cell_phone"),
          dataIndex: "cell_number",
          key: "cell_number",
          sorter: true,
          ...getColumnSearchProps("cell_number"),
        },
      ]),
  ];

  return (
    <>
      <Breadcrumb className="my-2 mx-2">
        <Breadcrumb.Item>
          <Link to="/app/default">{setLocale("home")}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{setLocale("Employee Attendance")}</Breadcrumb.Item>
      </Breadcrumb>
      <>
        <div className="code-box">
          <section className="code-box-demo">
            <Row gutter={8}>
              <Col sm={24} md={6}>
                <RangePicker
                  className="rounded-0 float-right mr-2"
                  onChange={onExportDateChange}
                  style={{ width: "100%" }}
                />
              </Col>
              <Col sm={24} md={6}>
                <Button
                  className="ant-btn-round ant-btn-sm"
                  type="primary"
                  onClick={handleExport}
                  loading={ExportExcelAttendanceButtonSpinner}
                >
                  {setLocale("studentchargessummary.generate_excel")}
                </Button>
              </Col>
              <Col sm={24} md={3}>
                <DatePicker
                  className="rounded-0 float-right mr-2"
                  onChange={onAttendanceDateChange}
                />
              </Col>
              <Col sm={24} md={3}>
                <Select
                  className="rounded-0 float-right w-100"
                  showSearch
                  defaultValue={"marked"}
                  optionLabelProp="label"
                  placeholder={setLocale("Attendance")}
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "").toLowerCase().includes(input)
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  onChange={attendanceOptionOnChange}
                  options={attendanceOptions || []}
                />
              </Col>
              <Col sm={24} md={6}>
                {permission.includes("Create") && (
                  <Button
                    className="ant-btn-round ant-btn-sm"
                    type="primary"
                    style={{ float: "right" }}
                    onClick={handleOpenModal}
                  >
                    {setLocale("employeeattendance.add")}
                  </Button>
                )}
              </Col>
            </Row>
          </section>
          {EmployeeAttendanceAddDrawer && <AddEmployeeAttendanceModal />}
          <section className="code-box-description">
            <Table
              onChange={handleTableChange}
              columns={columns}
              loading={EmployeeAttendanceTableLoading}
              rowKey={(record) => record.id}
              dataSource={EmployeeAttendanceResult.data ?? []}
              pagination={false}
            />
            <Pagination
              style={{ margin: "16px", float: "right" }}
              current={tablePagination.current}
              pageSize={tablePagination.pageSize}
              total={tablePagination.total}
              showTotal={(total, range) =>
                `${range[0]}-${range[1]} of ${total} Records`
              }
              pageSizeOptions={["10", "20", "50", "100", "1000"]}
              showQuickJumper
              onChange={handlePageChange}
            />
          </section>
        </div>
      </>
    </>
  );
}

export default Index;

import React from "react";
import { Route, Routes } from "react-router-dom";
import MailItem from "./MailItem";
import MailDetail from "./MaiDetail";
import MailCompose from "./MailCompose";
import ConversationItem from "./ConversationItem";

export const MailContent = () => {
  return (
    <Routes>
      <Route path="compose" element={<MailCompose />} />
      <Route path=":category/:id" element={<MailDetail />} />
      <Route path="conversation/:category/:id" element={<MailItem />} />
      <Route path="conversation/:category" element={<ConversationItem />} />
    </Routes>
  );
};

export default MailContent;

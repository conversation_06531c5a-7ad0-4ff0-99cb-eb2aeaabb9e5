import moment from "moment";
import React, { useState, useEffect } from "react";
import {
  Breadcrumb,
  Empty,
  Modal,
  Space,
  Input,
  Tooltip,
  Table,
  Badge,
  Typography,
  Avatar,
  Tag,
  Row,
  Form,
  Col,
  Button,
  Card,
  DatePicker,
  Select,
  Spin,
} from "antd";
import { useSelector, useDispatch } from "react-redux";
import { EditOutlined, UserOutlined } from "@ant-design/icons";
import {
  getAttendanceForModification,
  getAttendanceCodes,
  modifyBulkAtt,
} from "store/slices/AttendanceManagement/attendanceSlice";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";
import { env } from "configs/EnvironmentConfig"
import debounce from "lodash/debounce";
import { STUDENT } from "constants/student/index";
import { ATTENDANCE_SLICE } from "constants/attendance/index";
import { MANAGE_CLASS } from "constants/AppConstants";
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
import { getManageClass } from "store/slices/ManageClass/manageManageClassSlice.js";
import {
  getClassStudents,
  updateSortFilters,
} from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { searchStudents } from "store/slices/Student/manageStudentSlice.js";
import OrganizationSelect from "../OrganizationDropdown/OrganizationSelect";
import { USER_INFORMATION } from "constants/AuthConstant";
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { TextArea } = Input;
const { Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const getColor = (color) => {
  switch (color) {
    case "Present":
      return "green";
    case "Unexcused Absence":
      return "red";
    case "Excused Absence":
      return "red";
    case "expulsion":
      return "red";
    case "Late":
      return "gold";
    default:
      return "gray";
  }
};

function Index() {
  const dispatch = useDispatch();
  const [form] = Form.useForm();

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [attBulkModalOpen, setAttBulkModalOpen] = useState(false);
  const [attModalStatus, setAttModalStatus] = useState(1);
  const [attModalComment, setAttModalComment] = useState("");
  const [formData, setFormData] = useState();
  const [attModalOpen, setAttModalOpen] = useState(false);

  const [attId, setAttId] = useState(null);
  const [filterBy, setFilterBy] = useState("by_student");
  const [isSearch, setIsSearch] = useState(false);
  const [organization, setOrganization] = useState(selectedOrganization);

  /** get Att for modification */
  /** fetch Data from store for display  */
  const {
    attendanceForModification,
    attendanceCodes,
    AttendanceButtonSpinner,
    AttendanceLoading,
  } = useSelector((state) => state[ATTENDANCE_SLICE]);
  const { ManageClassResult } = useSelector((state) => state[MANAGE_CLASS]);

  const { classStudents, filter, sorting, tablePagination } = useSelector(
    (state) => state[ATTENDANCE_REPORT]
  );
  const { searchedStudents, StudentButtonSpinner } = useSelector(
    (state) => state[STUDENT]
  );

  /**mark option in dropdown */
  let markAttOptions = [];
  markAttOptions = attendanceCodes
    ? attendanceCodes.map((code) => {
      let color = getColor(code.attendance_code);
      return {
        value: code.id,
        label: (
          <Tag color={color} style={{ width: "100%" }}>
            {code.attendance_code}
          </Tag>
        ),
      };
    })
    : [];

  const handleSearch = debounce(async (value) => {
    if (value) {
      // Fetch data from the database based on the search value
      await dispatch(
        searchStudents({ string: value, organization_id: organization })
      );
    }
  }, 300);

  useEffect(() => {
    dispatch(getAttendanceCodes());
    dispatch(getManageClass(null));
  }, []);

  /**get Class Students by ClassId */
  const getStudentByClass = async (value) => {
    await dispatch(getClassStudents({ id: value }));
    form.setFieldsValue({ student_id: [] });
  };

  const onFinish = async (values) => {
    setIsSearch(true);
    const dateRange = values["date"];
    /** make date formate */
    values.from_date = moment(dateRange[0]).format("YYYY-MM-DD");
    values.end_date = moment(dateRange[1]).format("YYYY-MM-DD");
    values.att_modify_by = filterBy;
    values.organization_id = filter?.organization_id ?? selectedOrganization;
    setFormData(values);
    /**call function */
    await getAttForModify(values);
  };
  /**get attendance for modification */
  const getAttForModify = async (attData) => {
    await dispatch(getAttendanceForModification(attData));
    setAttBulkModalOpen(false);
    setAttModalOpen(false);
    setSelectedRowKeys([]);
  };
  //Att modification Function
  const attModificationFun = async (attData) => {
    await dispatch(modifyBulkAtt(attData));
    await getAttForModify(formData);
  };

  // modify Bulk Attendance of students
  const modifyBulkStudentAttendance = () => {
    const modifyAtt = {
      attendance_code_id: attModalStatus,
      comments: attModalComment,
      attendance_ids: selectedRowKeys,
    };
    attModificationFun(modifyAtt);
  };
  /** open model for modification of Single Student */
  const attModel = (record) => {
    const attCode = record.overde_attendance_code_id
      ? record.overde_attendance_code_id
      : record.attendance_code_id;
    setAttModalOpen(true);
    setAttModalStatus(attCode);
    setAttId(record.key);
  };
  /**single Attendance modification  */
  const singleAttModify = () => {
    const modifyAtt = {
      attendance_code_id: attModalStatus,
      comments: attModalComment,
      attendance_ids: [attId],
    };
    attModificationFun(modifyAtt);
  };

  const columns = [
    {
      title: setLocale("name"),
      key: "student",
      fixed: "left",
      render: (record) => (
        <>
          <Avatar
            src={
              record.avatarUrl ? (
                env.FILE_ENDPOINT_URL + record.avatarUrl
              ) : (
                <Avatar icon={<UserOutlined />} />
              )
            }
          />
          <Link
            to={`/app/student_view/${record.enc_id}`}
            style={{ marginLeft: "10px" }}
          >
            {record.student + " (" + record.student_id + ")"}
          </Link>
        </>
      ),
    },
    {
      title: setLocale("classes.label"),
      dataIndex: "class",
      key: "class",
    },
    {
      title: setLocale("attendance.attendance_status"),
      key: "attendance_code",
      render: (record) => (
        <span>
          {record?.overde_attendance_code ? (
            <>
              <Badge status="processing" />
              <Text type="warning">{record?.attendance_code}</Text>
              <br />
              <Tag color={getColor(record.overde_attendance_code)}>
                {record.overde_attendance_code.toUpperCase()}
              </Tag>
            </>
          ) : (
            <>
              <Tag color={getColor(record.attendance_code)}>
                {record.attendance_code.toUpperCase()}
              </Tag>
            </>
          )}
        </span>
      ),
    },
    {
      title: setLocale("attendance.attendance_date"),
      dataIndex: "attendance_date",
      key: "attendance_date",
    },
    {
      title: setLocale("attendance.created_by"),
      key: "created_by",
      render: (record) => (
        <>
          {record.created_by}
        </>
      ),
    },
    {
      title: setLocale("Comments"),
      key: "comments",
      render: (record) => (
        <>
          {record.comments && record.comments}
        </>
      ),
    },
    {
      title: setLocale("attendance.created_date"),
      dataIndex: "created_at",
      key: "created_at",
    },
    {
      title: setLocale("attendance.updated_by"),
      key: "updated_by",
      render: (record) => (
        <>
          {record.updated_by}
        </>
      ),
    },
    // {
    //   title: setLocale("Comments"),
    //   key: "comments",
    //   render: (record) => (
    //     <>
    //     { record.overde_comments && record.overde_comments }        
    //     </>
    //   ),
    // },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <Badge status="processing" />
          <span style={{ marginLeft: 8 }}>{setLocale('Comments')}</span>
        </span>
      ),
      key: "comments",
      render: (record) => (
        <>
          {record.overde_comments ? (
            <Tooltip title={record.overde_comments}>
              <span
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "150px",
                  display: "inline-block",
                  cursor: "pointer",
                }}
              >
                {record.overde_comments}
              </span>
            </Tooltip>
          ) : (
            "-"
          )}
        </>
      ),
    },

    {
      title: setLocale("attendance.updated_date"),
      key: "updated_at",
      render: (record) => <>{record.updated_by ? record.updated_at : null}</>,
    },
    {
      title: setLocale("operation"),
      key: "action",
      fixed: "right",
      onCell: (record) => ({
        onClick: () => {
          attModel(record);
        },
      }),
      render: (record) => (
        <>
          <a>
            <EditOutlined />
          </a>
        </>
      ),
    },
  ];

  /** select multiple students for bluk attendance  */
  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const onChangeOrganization = (value) => {
    setOrganization(value);
    dispatch(getManageClass({ organization_id: value })).then((result) => {
      // setPageLoad(false)
    });
  };

  return (
    <>
      <Breadcrumb className="my-2 mx-2">
        <Breadcrumb.Item>
          <Link to="/app/default">{setLocale("home")}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{setLocale("attendance.title")}</Breadcrumb.Item>
      </Breadcrumb>
      <Row>
        <Col xs={24} sm={24} md={24} lg={24}>
          <Card
            style={{ textAlign: "left" }}
            type="inner"
            title={setLocale("attendance.title")}
          >
            <Form
              layout="vertical"
              onFinish={onFinish}
              form={form}
              initialValues={{
                att_modify_by: "by_student",
                date: [moment(), moment()],
              }}
            >
              <div>
                <Row gutter={16}>
                  <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                    <Row gutter={16}>
                      <Col xs={24} sm={24} md={6} lg={4} xl={4}>
                        <Form.Item label={setLocale("attendance.filter")} name="att_modify_by" >
                          <Select onChange={(e) => { setFilterBy(e); }} optionLabelProp="label" >
                            <Option value={"by_student"} label={setLocale("attendance.by_student")} >
                              {setLocale("attendance.by_student")}
                            </Option>
                            <Option value={"by_class"} label={setLocale("attendance.by_class")} >
                              {setLocale("attendance.by_class")}
                            </Option>
                            <Option value={"by_status"} label={setLocale("attendance.by_status")} >
                              {setLocale("attendance.by_status")}
                            </Option>
                          </Select>
                        </Form.Item>
                      </Col>

                      {userOrganizations.length > 1 && (
                        <>
                          <Col xs={24} sm={24} md={6} lg={4} xl={4}>
                            <Form.Item
                              name="organization_id"
                              label={setLocale("Organization")}
                              rules={[
                                {
                                  required: false,
                                  message: setLocale("Please Select Class"),
                                },
                              ]}
                            >
                              <OrganizationSelect
                                updateSortFilters={updateSortFilters}
                                onChange={onChangeOrganization}
                                filter={filter}
                                sorting={sorting}
                                tablePagination={tablePagination}
                                getModuleData={onFinish}
                                runUseEffect={false}
                                dropStyle={true}
                              />
                            </Form.Item>
                          </Col>
                        </>
                      )}

                      <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                        <Form.Item
                          name="date"
                          label={setLocale("attendance.date_from")}
                          rules={[
                            {
                              required: true,
                              message: setLocale(
                                "attendance.select_date_range"
                              ),
                            },
                          ]}
                        >
                          <RangePicker
                            format="YYYY-MM-DD"
                            defaultValue={[moment(), moment()]}
                            style={{ width: "100%" }}
                          />
                        </Form.Item>
                      </Col>

                      {filterBy === "by_class" && (
                        <>
                          <Col xs={24} sm={24} md={6} lg={5} xl={5}>
                            <Form.Item name="class"
                              label={setLocale("classes.label")}
                              rules={[
                                {
                                  required: true,
                                  message: setLocale("classes.label_error"),
                                },
                              ]}
                            >
                              <Select onChange={getStudentByClass} optionLabelProp="label" >
                                {ManageClassResult
                                  ? ManageClassResult.map((clas, index) => (
                                    <Option
                                      value={clas.id}
                                      key={index}
                                      label={clas.name}
                                    >
                                      {clas.name}
                                    </Option>
                                  ))
                                  : null}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={24} md={6} lg={5} xl={5}>
                            <Form.Item
                              name="student_id"
                              label={setLocale("student.label")}
                              rules={[
                                {
                                  required: false,
                                  message: setLocale("student.label_error"),
                                },
                              ]}
                            >
                              <Select
                                mode="multiple"
                                optionLabelProp="label"
                                filterOption={(inputValue, option) =>
                                  option.label
                                    .toLowerCase()
                                    .indexOf(inputValue.toLowerCase()) >= 0
                                }
                              >
                                {classStudents
                                  ? classStudents.map((clsStd, index) => (
                                    <Option
                                      value={clsStd.students.id}
                                      key={index}
                                      label={clsStd.students.full_name}
                                    >
                                      {clsStd.students.full_name +
                                        " (" +
                                        clsStd.students.student_id +
                                        ")"}
                                    </Option>
                                  ))
                                  : null}
                              </Select>
                            </Form.Item>
                          </Col>
                        </>
                      )}
                      {filterBy === "by_student" && (
                        <>
                          <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                            <Form.Item
                              name="class_student_id"
                              label={setLocale("student.label")}
                              extra={
                                <span
                                  style={{
                                    fontSize: "12px",
                                    color: "#888",
                                    opacity: "0.9",
                                  }}
                                >
                                  {" "}
                                  For Search(First Name,Last Name, Student ID)
                                </span>
                              }
                              rules={[
                                {
                                  required: true,
                                  message: setLocale("student.label_error"),
                                },
                              ]}
                            >
                              <Select
                                showSearch
                                placeholder="Search..."
                                onSearch={handleSearch}
                                notFoundContent={
                                  StudentButtonSpinner ? (
                                    <Spin size="small" />
                                  ) : null
                                }
                                filterOption={false}
                              >
                                {searchedStudents
                                  ? searchedStudents.map((option) => (
                                    <Option key={option.id} value={option.id}>
                                      {option.label}
                                    </Option>
                                  ))
                                  : ""}
                              </Select>
                            </Form.Item>
                          </Col>
                        </>
                      )}

                      {filterBy === "by_status" && (
                        <>
                          <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                            <Form.Item name="attendance_code_id" label={setLocale("student.attendance_code")}
                              rules={[
                                {
                                  required: true,
                                  message: setLocale("required_error"),
                                },
                              ]} >
                              <Select placeholder={setLocale("student.attendance_code")}>
                                {attendanceCodes && attendanceCodes.map((option) => (
                                  <Option key={option.id} value={option.id}>
                                    {option.attendance_code}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                        </>
                      )}

                      <Col xs={24} sm={24} md={6} lg={4} xl={4}>
                        <Form.Item label=" ">
                          <Button
                            loading={AttendanceButtonSpinner}
                            disabled={AttendanceButtonSpinner}
                            type="primary"
                            htmlType="submit"
                          >
                            {setLocale("attendance.get_attendance")}
                          </Button>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>
                </Row>
              </div>
            </Form>

            {isSearch ? (
              <>
                {selectedRowKeys.length ? (
                  <Button
                    onClick={() => {
                      setAttBulkModalOpen(true);
                    }}
                  >
                    {setLocale("attendance.bulk_action")}
                  </Button>
                ) : null}

                <Table
                  size="small"
                  pagination={false}
                  loading={AttendanceLoading}
                  rowSelection={rowSelection}
                  showHeader
                  scroll={{ x: "max-content" }}
                  columns={columns}
                  dataSource={
                    attendanceForModification.length
                      ? attendanceForModification
                      : []
                  }
                />
              </>
            ) : null}
          </Card>
        </Col>
      </Row>

      {attBulkModalOpen ? (
        <Modal
          title={setLocale("attendance.bulk_attendance_modification")}
          confirmLoading={AttendanceButtonSpinner}
          okText={setLocale("save")}
          open={attBulkModalOpen}
          onOk={modifyBulkStudentAttendance}
          onCancel={() => {
            setAttBulkModalOpen(false);
          }}
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Select
              size={"small"}
              value={attModalStatus}
              style={{ width: "50%" }}
              onChange={(e) => {
                setAttModalStatus(e);
              }}
              options={markAttOptions}
            />
            <TextArea
              rows={3}
              placeholder="comments"
              onChange={(e) => {
                setAttModalComment(e.target.value);
              }}
            />
          </Space>
        </Modal>
      ) : null}

      {/* Modal For Att modification and add some comments */}
      {attModalOpen ? (
        <Modal
          title={setLocale("attendance.single_attendance_modification")}
          confirmLoading={AttendanceButtonSpinner}
          okText={setLocale("save")}
          open={attModalOpen}
          onOk={singleAttModify}
          onCancel={() => {
            setAttModalOpen(false);
          }}
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Select
              size={"small"}
              value={attModalStatus}
              style={{ width: "50%" }}
              onChange={(e) => {
                setAttModalStatus(e);
              }}
              options={markAttOptions}
            />
            <TextArea
              rows={3}
              placeholder="comments"
              onChange={(e) => {
                setAttModalComment(e.target.value);
              }}
            />
          </Space>
        </Modal>
      ) : null}
    </>
  );
}

export default Index;

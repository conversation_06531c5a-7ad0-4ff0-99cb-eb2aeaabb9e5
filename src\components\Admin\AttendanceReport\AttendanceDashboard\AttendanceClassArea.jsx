import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { Select, Form, Row, Col, Card } from 'antd';
import Chart from "react-apexcharts";
// import { 
//   ArrowDownOutlined 
// } from '@ant-design/icons';
// import IntlMessage from "components/util-  components/IntlMessage";
// import {
//   getAttendanceDashboardReport,
//   getClassStudents
// } from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
// import { 
//     MANAGE_CLASS, 
//     SCHOOL_YEAR 
// } from "constants/AppConstants";

// const setLocale = (localeKey, isLocaleOn = true) =>
//     isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function AttendanceClassArea() {
  // const [form] = Form.useForm();
  // const dispatch = useDispatch();
  const {
    //   AttendanceReportButtonSpinner,
    attendanceWeeklyArea,
    attendanceClassBar
    //   classStudents 
  } = useSelector((state) => state[ATTENDANCE_REPORT]);

  const [attCodes, setAttCodes] = useState([
    { label: "Present", value: "Present" },
    { label: "Unexcused Absence", value: "Unexcused Absence" },
    { label: "Excused Absence", value: "Excused Absence" },
    { label: "Late", value: "Late" },
    { label: "Unspecified", value: "Unspecified" }
  ]);

  const [attType, setAttType] = useState(attCodes.map((item) => item.value));
  const [weeks, setWeeks] = useState([]);
  const [areaData, setAreaData] = useState([]);
  const [colors, setColors] = useState([]);

  const [state, setState] = useState({
    series: [],
    options: {
      dataLabels: {
        enabled: false
      },
      chart: {
        toolbar: {
          show: false
        }
      },
      legend: {
        show: false
      },
      colors: [],
      stroke: {
        curve: 'smooth'
      },
      xaxis: {
        categories: [],
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm'
        }
      },
    }
  });

  useEffect(() => {
    extractAttendanceData();
  }, [attendanceWeeklyArea, attType]);

  const extractAttendanceData = () => {
    const { transformedData, weeks, colors } = transformAttendanceData(attendanceWeeklyArea);

    setWeeks(weeks);
    setAreaData(transformedData);
    setColors(colors);
    setState({ ...state, series: transformedData, options: { ...state.options, xaxis: { categories: weeks }, colors: colors } });
  }

  const handleChange = (value) => {
    setAttType(value);
    // extractAttendanceData();
  };
  const transformAttendanceData = (attendanceData) => {
    if (!attendanceData) return { transformedData: [], weeks: [], colors: [] };
    const transformedData = [];
    const weeks = Object.keys(attendanceData).map(week => `week-${week}`);
    const colorMap = {
      "Present": "#54B227",
      "Unexcused Absence": "#FF754A",
      "Excused Absence": "#D5433A",
      "Late": "#FBD409",
      "Unspecified": "#B0AFAD"
    };

    // const attendanceTypes = attType.length ? attType : Object.keys(colorMap);

    attType.forEach(type => {
      const data = weeks.map(week => {
        const weekNumber = week.split('-')[1];
        return attendanceData[weekNumber][type] || 0;
      });

      transformedData.push({
        name: type,
        data: data,
        color: colorMap[type]
      });
    });

    return { transformedData, weeks, colors: Object.values(colorMap) };
  };


  return (
    <>
      <Card className="p-2" style={{ height: "450px" }}>
        <div className="d-flex justify-content-between mb-3">
          <div>
            <h1 style={{ color: "#3C3C3C" }}>Attendance by Date</h1>
          </div>
          <div>
            <Select mode="multiple" defaultValue={attType} onChange={handleChange}
              style={{ width: '200px', color: "black", fontWeight: 900 }}
              maxTagCount={1}
              options={attCodes}
            />
          </div>
        </div>
        <div>
          <Chart options={state.options} series={state.series} type="area" height={350} />
        </div>
      </Card>
    </>
  );
}

export default AttendanceClassArea;

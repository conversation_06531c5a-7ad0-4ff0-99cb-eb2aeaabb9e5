import React, { useEffect, useState} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Switch } from "antd";
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from "react-redux";
import { COUNTRY } from "constants/AppConstants";
import {
    CountryAddDrawerStatus,
    createCountry,
    getCountry,
    onCloseError
  } from "store/slices/Country/manageCountrySlice.js";
  import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const [switchValue, setSwitchValue] = useState('checked');
const dispatch = useDispatch();
    const {DrawerStatus, filter, sorting, CountryAddDrawer, CountryButtonAndModelLabel, CountryErrors, CountryShowMessage, CountryButtonSpinner, CountryEditData, tablePagination } = useSelector(
        (state) => state[COUNTRY]
      );
const onClose = () => {
    dispatch(CountryAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (CountryEditData && CountryEditData.id) {
    // If editing, include the id in the form values
    formValues.id = CountryEditData.id;
    formValues.is_default = CountryEditData.is_default == 1 ? true : false;
  }

  await dispatch(createCountry(formValues))
};
useEffect(() => {
    if (Object.keys(CountryErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getCountry({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [CountryErrors]);
useEffect(() => {
  CountryEditData.is_default === 1 ? setSwitchValue('checked') : setSwitchValue();
}, [dispatch]);
const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
  };
  const getSwitchValue = (checked) => {
    setSwitchValue(checked);
  };

  return (
    <>

      <Drawer
        title={CountryButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CountryAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: CountryEditData?.name,
            country_code: CountryEditData?.country_code,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale("name")}
                rules={[
                  {
                    required: true,
                    message: setLocale("name_error"),
                  },
                ]}
                validateStatus={CountryShowMessage && CountryErrors.name ? "error" : ""}
                help={CountryShowMessage && CountryErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="country_code"
                label={setLocale("countries.country_code")}
                rules={[
                  {
                    required: true,
                    message: setLocale("countries.country_code_error"),
                  },
                ]}
                validateStatus={CountryShowMessage && CountryErrors.country_code ? "error" : ""}
                help={CountryShowMessage && CountryErrors.country_code}
              >
                <Input />
              </Form.Item>
            </Col>
            {!(CountryEditData && CountryEditData.id) &&
              <Col span={12}>
                <Form.Item
                  name="is_default"
                  // valuePropName={switchValue}
                  label="Status"
                  rules={[
                    {
                      required: false,
                      message: "Status Error",
                    },
                  ]}
                  validateStatus={CountryShowMessage && CountryErrors.is_default ? "error" : ""}
                      extra={CountryShowMessage && CountryErrors.is_default}


                >
                  <Switch
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    onChange={getSwitchValue}
                    checked={switchValue}
                    name="is_default"
                  />
                </Form.Item>
              </Col>
              }

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CountryButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale("cancel")}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


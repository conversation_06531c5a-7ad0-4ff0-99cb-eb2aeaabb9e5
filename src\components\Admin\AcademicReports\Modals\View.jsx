import React, { useState, useEffect } from "react";
import { Col, Row, Card, Avatar, Skeleton, Breadcrumb } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { Icon } from "components/util-components/Icon";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
    ArrowLeftOutlined,
    ContainerOutlined
} from "@ant-design/icons";
import {
    viewAcademicReports
} from "store/slices/AcademicReports/manageAcademicReportsSlice.js";
import Flex from "components/shared-components/Flex";
import IntlMessage from "components/util-components/IntlMessage"
import { ACADEMIC_REPORTS } from "constants/AppConstants";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function View() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const params = useParams();
    const { ViewAcademicReportsData, ViewAcademicReportsLoader } = useSelector(
        (state) => state[ACADEMIC_REPORTS]
    );


    useEffect(() => {
        dispatch(viewAcademicReports(params?.id));
    }, []);

    return (
        <>
            <Breadcrumb className="my-2 mx-2">
                <Breadcrumb.Item>
                    <Link to="/app/default">{setLocale('home')}</Link>
                </Breadcrumb.Item>
                <Breadcrumb.Item>{setLocale('Academic Reports')}</Breadcrumb.Item>
            </Breadcrumb>
            {ViewAcademicReportsLoader ? <><Skeleton active ></Skeleton></> :
                <>
                    <Card
                        title={
                            <h5>
                                <span style={{ cursor: "pointer", float: "right" }}
                                    onClick={() => navigate(-1)}
                                >
                                    <ArrowLeftOutlined /> Back
                                </span>{" "}
                            </h5>
                        }
                        className="profile-card"
                    >
                        <Row justify="center">
                            <Col sm={24} md={23}>
                                <div className="d-md-flex">
                                    <div className="rounded shadow-sm mx-auto">
                                        <Avatar style={{ backgroundColor: "#fde3cf", color: "#f56a00" }}>
                                            {ViewAcademicReportsData?.name?.charAt(0).toUpperCase() +
                                                ViewAcademicReportsData?.name?.charAt(1).toUpperCase()}
                                        </Avatar>
                                    </div>
                                    <div className="ml-md-4 w-100">
                                        <Flex
                                            alignItems="center"
                                            mobileFlex={false}
                                            className="mb-3 text-md-left text-center"
                                        >
                                            <h2 className="mb-0 mt-md-0 mt-2">
                                                {ViewAcademicReportsData?.name}
                                            </h2>
                                        </Flex>
                                        <Row gutter="16">
                                            <Col xs={24} sm={24} md={8}>
                                                <Row className="mb-2 mt-2 mt-md-0 ">
                                                    <Col xs={12} sm={12} md={24}>
                                                        <Icon
                                                            type={ContainerOutlined}
                                                            className="text-primary font-size-md"
                                                        />
                                                        <span className="text-muted ml-2">Created At:</span>
                                                        <span className="font-weight-semibold ml-2">
                                                            {ViewAcademicReportsData?.created_at}
                                                        </span>
                                                    </Col>
                                                </Row>
                                            </Col>
                                        </Row>
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </Card>
                </>
            }
        </>
    );
}
export default View;

import moment from "moment";
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DatePicker,
  Row,
  Col,
  Select,
  Tag,
  Table,
  Avatar,
  Card,
  Modal,
  Input,
  Space,
} from "antd";
import { useSelector, useDispatch } from "react-redux";
import { EditOutlined, UserOutlined } from "@ant-design/icons";
import {
  getClassStudentsForAtt,
  getAttendanceCodes,
  markAttFullClass,
  modifySingleStudentAtt,
  markBulkStudentAtt,
  getAttendanceLatestPolicy,
} from "store/slices/AttendanceManagement/attendanceSlice";
import { useParams } from "react-router-dom";
import { env } from "configs/EnvironmentConfig"
import { ATTENDANCE_SLICE } from "constants/attendance/index";
import { MANAGE_CLASS } from "constants/AppConstants";
import IntlMessage from "components/util-components/IntlMessage";
import { useNavigate, Link } from "react-router-dom";
import { viewManageClass } from "store/slices/ManageClass/manageManageClassSlice";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { TextArea } = Input;

// const disabledRangeDate = current => {
//   // Disable all dates except today and the last three days
//   return current && !(current.isSameOrBefore(moment(), 'day'));
//   // return current && !(current.isSameOrAfter(moment().subtract(3, 'days'), 'day') && current.isSameOrBefore(moment(), 'day'));
// };

const getColor = (color) => {
  switch (color) {
    case "Present":
      return "green";
    case "Unexcused Absence":
      return "red";
    case "Excused Absence":
      return "red";
    case "expulsion":
      return "red";
    case "Late":
      return "gold";
    default:
      return "gray";
  }
};

function OnceDayAttendance(props) {
  const dispatch = useDispatch();
  const { id } = useParams();
  const [currentDate, setCurrentDate] = useState(moment());
  /** open For Att Modal for Modification */
  const [attModalOpen, setAttModalOpen] = useState(false);
  const [attModalStatus, setAttModalStatus] = useState(5);
  const [attModalComment, setAttModalComment] = useState("");
  const [attModalTitle, setAttModalTitle] = useState("");
  const [modalClassStudentId, setModalClassStudentId] = useState(0);

  /**attendance bulk action  */
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [attBulkModalOpen, setAttBulkModalOpen] = useState(false);
  const [isDisabled, setIsDisabled] = useState(true);

  /** fetch Data from store for display  */
  const {
    isAttMarked,
    classStudentsForAttendance,
    attendanceCodes,
    AttendanceButtonSpinner,
    attendanceLatestPolicy,
    AttendanceLoading,
  } = useSelector((state) => state[ATTENDANCE_SLICE]);
  const { ViewManageClassData, userRoles, teacherData, } = useSelector((state) => state[MANAGE_CLASS]);
  const [organization, setOrganization] = useState(
    ViewManageClassData.organization_id
  );
  const [canEdit, setCanEdit] = useState(true);


  useEffect(() => {
    if (userRoles && teacherData?.to_date) {
      const today = new Date();
      const toDate = new Date(teacherData.to_date);

      // If toDate is before today, editing is not allowed
      if (toDate < today) {
        setCanEdit(false);
      } else {
        setCanEdit(true);
      }
    }
  }, [teacherData]);

  console.log(userRoles, teacherData, canEdit);

  const disabledRangeDate = (current) => {
    const startDate = moment(ViewManageClassData.start_date);
    const endDate = moment(ViewManageClassData.end_date).endOf("day");
    return current && (current.isBefore(startDate) || current.isAfter(endDate));
  };
  const disabledFields = () => {
    const startDate = moment(ViewManageClassData.start_date);
    const endDate = moment(ViewManageClassData.end_date).endOf("day");
    setIsDisabled(
      currentDate && (currentDate < startDate || currentDate > endDate)
    );
  };

  useEffect(() => {
    /**set date formate  */
    setCurrentDate(moment(currentDate).format("YYYY-MM-DD"));
    const date = moment(currentDate).format("YYYY-MM-DD");
    const obj = { id, currentDate: date, organization };
    /**dispatch requests to get Data */
    dispatch(getAttendanceLatestPolicy({ organization: organization }));
    dispatch(getAttendanceCodes());
    dispatch(getClassStudentsForAtt(obj));
    disabledFields();
  }, []);

  let selectedColumns = [];
  /**set value of students into selected */
  selectedColumns = classStudentsForAttendance
    ? classStudentsForAttendance.map(
      ({
        id,
        full_name,
        class_student_id,
        profile_picture,
        att_status,
        status,
        att_id,
        comment,
        enc_id,
        student_id,
      }) => ({
        key: class_student_id,
        name: full_name,
        class_student_id,
        avatarUrl: profile_picture,
        status,
        att_status,
        att_id,
        comment,
        enc_id,
        student_id,
      })
    )
    : [];

  /**mark option in dropdown */
  let markAttOptions = [];
  markAttOptions = attendanceCodes
    ? attendanceCodes.map((code) => {
      let color = getColor(code.attendance_code);
      return {
        value: code.id,
        label: (
          <Tag color={color} style={{ width: "94%" }}>
            {code.attendance_code}
          </Tag>
        ),
      };
    })
    : [];

  /*filter attendance code on the base of ID */
  const getAttCode = (attCode) =>
    attendanceCodes.filter((code) => code.id == attCode)[0];

  /**mark attendance of all students at same time */
  const markAttendanceAll = async (value) => {
    const attMarkData = {
      attendance_code_id: value,
      attendance_policy_id: attendanceLatestPolicy,
      attendance_date: moment(currentDate).format("YYYY-MM-DD"),
      students: selectedColumns,
    };
    /**first mark attendance  */
    await dispatch(markAttFullClass(attMarkData));
    /** get marked attendace as get at the time of page load same function */
    /** get update data */
    await dispatch(getClassStudentsForAtt({ id, currentDate, organization }));
  };

  const onChangeDate = async (date, dateString) => {
    /**set formated date into page state */
    setCurrentDate(moment(date).format("YYYY-MM-DD"));
    const currentDate = moment(date).format("YYYY-MM-DD");
    disabledFields();
    /**make object of all variable to send to request */
    const obj = { id, currentDate, organization };
    await dispatch(getClassStudentsForAtt(obj));
  };

  /** open model for modification of Single Student */
  const attModel = (record) => {
    setAttModalOpen(true);
    setAttModalStatus(record.status);
    setAttModalTitle(record.name);
    setAttModalComment(record.comment);
    setModalClassStudentId(record.class_student_id);
  };
  /** on close and Save button Att modal will close state FALSE */
  const handleOk = () => {
    const modifyAtt = {
      attendance_code_id: attModalStatus,
      attendance_policy_id: attendanceLatestPolicy,
      attendance_date: moment(currentDate).format("YYYY-MM-DD"),
      comments: attModalComment,
      class_student_id: modalClassStudentId,
    };
    singleStudentAtt(modifyAtt);
  };

  /**send a single student call to update att */
  const singleStudentAtt = async (modifyAtt) => {
    /**post comments and update status */
    await dispatch(modifySingleStudentAtt(modifyAtt));
    setAttModalOpen(false);
    /** get update data */
    await dispatch(getClassStudentsForAtt({ id, currentDate, organization }));
  };

  const handleCancel = () => {
    setAttModalOpen(false);
  };

  /** mark attendace single student */
  const markSingleStudentAtt = (e, record) => {
    const modifyAtt = {
      attendance_code_id: e,
      attendance_policy_id: attendanceLatestPolicy,
      attendance_date: moment(currentDate).format("YYYY-MM-DD"),
      class_student_id: record.class_student_id,
      comments: record.comment,
    };
    singleStudentAtt(modifyAtt);
  };
  /** mark bulk student attendance  */
  const markBulkStudentAttendance = async () => {
    const modifyAtt = {
      attendance_code_id: attModalStatus,
      attendance_policy_id: attendanceLatestPolicy,
      attendance_date: moment(currentDate).format("YYYY-MM-DD"),
      comments: attModalComment,
      class_student_ids: selectedRowKeys,
    };

    await dispatch(markBulkStudentAtt(modifyAtt));

    setAttBulkModalOpen(false);
    setSelectedRowKeys([]);
    /** get update data */
    dispatch(getClassStudentsForAtt({ id, currentDate, organization }));
  };

  const filterAttendanceByCode = (e) => {
    if (e) {
      const obj = { id, currentDate, organization, attendance_code_id: e };
      dispatch(getClassStudentsForAtt(obj));
    } else {
      const obj = { id, currentDate, organization };
      dispatch(getClassStudentsForAtt(obj));
    }
  };
  /** select multiple students for bluk attendance  */
  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  /**columns for attendance table */
  const attcolumns = [
    {
      title: setLocale("name"),
      dataIndex: "name",
      key: "name",
      render: (name, record) => (
        <>
          <Avatar
            src={
              record.avatarUrl ? (
                env.FILE_ENDPOINT_URL + record.avatarUrl
              ) : (
                <Avatar icon={<UserOutlined />} />
              )
            }
          />
          {/* <a href='#' style={{ marginLeft: '10px' }}>{name}</a> */}
          <Link
            to={`/app/student_view/${record.enc_id}`}
            style={{ marginLeft: "10px" }}
          >
            {name + " (" + record.student_id + ")"}
          </Link>
        </>
      ),
    },
    {
      title: setLocale("status"),
      key: "status",
      render: (record) => {
        const code = getAttCode(record.status);
        let color = code ? getColor(code.attendance_code) : "gray";
        return true && code && record.att_status ? (
          <Tag color={color}>{code.attendance_code.toUpperCase()}</Tag>
        ) : (
          <Select
            size={"small"}
            defaultValue="unspecified"
            disabled={isDisabled}
            style={{ width: 200 }}
            onChange={(e) => {
              markSingleStudentAtt(e, record);
            }}
            options={markAttOptions}
          />
        );
      },
    },
    {
      title: setLocale("assignment.comments"),
      dataIndex: "comment",
      key: "comment",
      render: (text) => <>{text}</>,
    },
    {
      title: setLocale("operation"),
      key: "action",
      onCell: (record) => {
        return true
          ? {
            onClick: () => {
              attModel(record);
            },
          }
          : {};
      },
      render: (record) => (
        <>
          {!isDisabled && true && (
            <a>
              <EditOutlined />
            </a>
          )}
        </>
      ),
    }

  ];

  const selectUndefined = () => {
    const keys = selectedColumns.filter(student => student.att_id === 0).map(student => student.key);
    const markedKeys = selectedColumns.filter(student => student.att_id !== 0).map(student => student.key);

    setSelectedRowKeys(prevSelectedKeys => {
      const isKeysSelected = keys.every(key => prevSelectedKeys.includes(key));
      const isMarkedKeysSelected = markedKeys.every(key => prevSelectedKeys.includes(key));

      if (isKeysSelected) {
        // If keys are selected, deselect them and select markedKeys
        return [...prevSelectedKeys.filter(key => !keys.includes(key)), ...markedKeys];
      } else if (isMarkedKeysSelected) {
        // If markedKeys are selected, deselect them and select keys
        return [...prevSelectedKeys.filter(key => !markedKeys.includes(key)), ...keys];
      } else {
        // Default case: Select keys if neither are fully selected
        return [...prevSelectedKeys, ...keys];
      }
    });
  }
  return (
    <>
      <Row>
        <Col className="mb-2 float-right" xs={24} sm={24} md={24} lg={24}>
          {isAttMarked && (
            <>
              <Select placeholder={"Filter by"} className="float-right" size={"small"}
                allowClear style={{ width: 250 }} options={markAttOptions} onChange={filterAttendanceByCode}
              />
            </>
          )}
        </Col>
      </Row>
      <Row>
        <Col xs={24} sm={24} md={24} lg={24}>
          <Card style={{ textAlign: "left" }} type="inner" title={setLocale("attendance.label")}
            extra={
              <>
                {
                  true && <>
                    <DatePicker format={"YYYY-MM-DD"} defaultValue={currentDate}
                      allowClear={false}
                      size={"small"}
                      className="mx-2"
                      style={{ width: 200 }}
                      disabledDate={disabledRangeDate}
                      onChange={onChangeDate}
                    />
                    {selectedColumns.length &&
                      !isAttMarked &&
                      selectedRowKeys.length === 0 ? (
                      <Select
                        size={"small"}
                        defaultValue="unspecified"
                        disabled={isDisabled}
                        style={{ width: 200 }}
                        onChange={markAttendanceAll}
                        options={markAttOptions}
                      />
                    ) : null}
                    {selectedRowKeys.length ? (
                      <Button
                        disabled={isDisabled}
                        onClick={() => {
                          setAttBulkModalOpen(true);
                        }}
                        loading={AttendanceButtonSpinner}
                      >
                        {setLocale("attendance.bulk_action")}
                      </Button>
                    ) : null}
                  </>
                }
              </>
            } >
            <Button disabled={isDisabled} onClick={() => { selectUndefined() }} loading={AttendanceButtonSpinner}>
              Inverse Selection
            </Button>
            <Table size="small" pagination={false} loading={AttendanceLoading} rowSelection={rowSelection} showHeader columns={attcolumns} dataSource={selectedColumns} />
          </Card>
        </Col>
      </Row>

      {/* Modal For Att modification and add some comments */}
      {attModalOpen && !isDisabled ? (
        <Modal
          title={attModalTitle}
          confirmLoading={AttendanceButtonSpinner}
          okText={setLocale("save")}
          open={attModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Select
              size={"small"}
              defaultValue={attModalStatus}
              style={{ width: "50%" }}
              onChange={(e) => {
                setAttModalStatus(e);
              }}
              options={markAttOptions}
            />
            <TextArea
              rows={3}
              value={attModalComment}
              placeholder="comments"
              onChange={(e) => {
                setAttModalComment(e.target.value);
              }}
            />
          </Space>
        </Modal>
      ) : null}
      {/* Model for Bulk Attendance action  */}

      {attBulkModalOpen && !isDisabled ? (
        <Modal
          title={setLocale("student.bulk_attendance")}
          confirmLoading={AttendanceButtonSpinner}
          okText={setLocale("save")}
          open={attBulkModalOpen}
          onOk={markBulkStudentAttendance}
          onCancel={() => {
            setAttBulkModalOpen(false);
          }}
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Select
              size={"small"}
              defaultValue={5}
              style={{ width: "50%" }}
              onChange={(e) => {
                setAttModalStatus(e);
              }}
              options={markAttOptions}
            />
            <TextArea
              rows={3}
              placeholder="comments"
              onChange={(e) => {
                setAttModalComment(e.target.value);
              }}
            />
          </Space>
        </Modal>
      ) : null}
    </>
  );
}

export default OnceDayAttendance;

import React from 'react'
import { useSelector, useDispatch } from "react-redux"
import { Table, Popconfirm, Typography, Row, Col, Tag, Card, Alert, Button, Skeleton } from 'antd'
import { env } from "configs/EnvironmentConfig"
import { DeleteOutlined, DownloadOutlined, EyeOutlined, FileOutlined } from '@ant-design/icons'
import {
  removeSingleRubric
} from "store/slices/Assignments/assignmentSlice"
import IntlMessage from "components/util-components/IntlMessage"
import { ASSIGNMENT_SLICE } from 'constants/assignments/index'
import AssignmentComments from './AssignmentComments'
import AddRubricOnly from '../AddRubricOnly'

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { Title } = Typography;

function AssignmentDetail() {
  const dispatch = useDispatch();
  const { singleAssignment, assigneeLoading } = useSelector((state) => state[ASSIGNMENT_SLICE]);

  const handleDelete = (id) => {
    dispatch(removeSingleRubric({ id: id }))
  }
  const columns = [
    {
      title: setLocale('assignment.rubric'),
      dataIndex: "rubric",
      key: "rubric",
    }, {
      title: setLocale('assignment.mark'),
      dataIndex: "mark",
      key: "mark",
    },
    {
      title: setLocale("operation"),
      dataIndex: "operation",
      render: (data, record) => (
        <>
          <Popconfirm title={setLocale("sure_to_delete")} onConfirm={() => handleDelete(record.id)}
            okText={setLocale("Yes")} cancelText={setLocale("No")} >
            <Button type="link" danger>
              <DeleteOutlined className="text-danger" />
            </Button>
          </Popconfirm>
        </>
      ),
    },
  ];

  return (
    <>
      <Skeleton avatar title={false} loading={assigneeLoading} active></Skeleton>
      {singleAssignment &&
        <Card
          type="inner" title={singleAssignment?.title}
          extra={<>
            <div className='assign-tag'>
              <Tag className='text-center tag cursor-pointer' color="gold"> {singleAssignment?.pendingCount} &nbsp; Pending</Tag>
              <Tag className='text-center tag cursor-pointer' color="green">{singleAssignment?.submittedCount} &nbsp; Submitted</Tag>
              <Tag className='text-center tag cursor-pointer' color="red">{singleAssignment?.reassignedCount} &nbsp; Re-Assigned</Tag>
            </div>
          </>}>
          <Row gutter={4}>
            <Col xs={24} sm={24} md={14} lg={14} xl={14}>
              <>
                <Tag className='text-center tag cursor-pointer' color="green">
                  <p className='mb-1'>Due Date: {singleAssignment?.due_date} </p>
                </Tag>
                <div className='mb-4'>{singleAssignment?.instructions} </div>
                {singleAssignment?.assignmentAttachments.length ?
                  <Title level={4}>{setLocale('assignment.files')}</Title>
                  : null}
                {singleAssignment?.assignmentAttachments.map((attachment, index) => (
                  <Alert key={index}
                    message={<FileOutlined />}
                    type="success"
                    action={attachment.attachment_type_code === 'file' ? (
                      <Button key={index} size="small" type="link" target="_blank"
                        href={env.FILE_ENDPOINT_URL + attachment.attachment_path} icon={<DownloadOutlined />} >
                        Download
                      </Button>
                    )
                      : (
                        <Button skey={index} size="small" type="link" target="_blank" href={attachment.attachment_path} icon={<EyeOutlined />} >
                          Preview
                        </Button>
                      )
                    }
                  />
                ))}
              </>
            </Col>
            <Col xs={24} sm={24} md={10} lg={10} xl={10}>
              <Tag className='text-center tag cursor-pointer' color="green">
                <p className='mb-1'>{setLocale('assignment.total_marks')} {singleAssignment?.total_marks} </p>
              </Tag>
              {
                singleAssignment?.assignment_rubrics &&
                <>
                  <Title level={4}>{setLocale('assignment.rubrics')}</Title>
                  <Table size="small" dataSource={singleAssignment?.assignment_rubrics} columns={columns} rowKey={(record) => record.id} pagination={false} />

                  <AddRubricOnly></AddRubricOnly>
                </>
              }
            </Col>
          </Row>

          <Row gutter={4} className='mt-4'>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              {singleAssignment ? <AssignmentComments></AssignmentComments> : null}
            </Col>
          </Row>

        </Card>
      }
    </>
  )
}

export default AssignmentDetail;
import { Select } from 'antd'
import React from 'react'

export const Range = ({onChange}) => {
  const { Option } = Select;
  
  // Generate array of days from 1 to 30
  const years = Array.from({length: 30}, (_, i) => i + 1);

  return (
    <Select
      placeholder="Select Years"
      style={{ minWidth: 120, width: '100%' }}
      onChange={onChange}
    >
      {years.map(year => (
        <Option key={year} value={year}>{year + " years"}</Option>
      ))}
    </Select>
  )
}

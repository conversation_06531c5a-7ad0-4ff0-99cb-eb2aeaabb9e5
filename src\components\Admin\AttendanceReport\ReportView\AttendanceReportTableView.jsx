import moment from "moment";
import React, { useEffect, useState } from "react";
import {
  message,
  Table,
  Badge,
  Typography,
  Row,
  Space,
  Col,
  Button,
} from "antd";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { Link } from "react-router-dom";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { Text } = Typography;

const AttendanceReportTableView = (props) => {
  const { data, reportName } = props;
  const [filteredData, setFilteredData] = useState(data);

  const [messageApi, contextHolder] = message.useMessage();
  const success = (msg) => {
    messageApi.open({
      type: "error",
      content: msg,
      style: { marginTop: "90vh" },
    });
  };

  const exportToExcel = () => {
    if (!filteredData.length) {
      success("Nothing to export!");
      return;
    }
    const keys = Object.keys(filteredData[0]); // Extract keys dynamically
    // Exclude columns 'student_id', 'attendance_short_code', 'attendance_date_register', 'overde_attendance_short_code'
    const filteredKeys = keys.filter(
      (key) =>
        key !== "student_id" &&
        key !== "attendance_short_code" &&
        key !== "attendance_date_register" &&
        key !== "overde_attendance_short_code"
    );

    const transformedData = [
      [reportName],
      filteredKeys, // Include the header row without excluded columns
      ...filteredData.map((item) =>
        Object.values(item).filter(
          (_, index) =>
            keys[index] !== "student_id" &&
            keys[index] !== "attendance_short_code" &&
            keys[index] !== "attendance_date_register" &&
            keys[index] !== "overde_attendance_short_code"
        )
      ), // Extract values dynamically and skip excluded columns
    ];

    const ws = XLSX.utils.aoa_to_sheet(transformedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, `${reportName}.xlsx`);
  };

  // Step 1: Get distinct values for  columns
  const distinctClasses = [...new Set(data.map((record) => record.class))];
  const distinctAttStatus = [
    ...new Set(data.map((record) => record.attendance_status)),
  ];
  const distinctAttDate = [
    ...new Set(data.map((record) => record.attendance_date)),
  ];
  const distinctAttStudents = [
    ...new Set(data.map((record) => record.student_name)),
  ];

  // Step 2: Generate filter options dynamically
  const filterOptions = distinctClasses.map((classValue) => ({
    text: classValue,
    value: classValue,
  }));
  const filterOptionsForAttStatus = distinctAttStatus.map((value) => ({
    text: value,
    value: value,
  }));
  const filterOptionsForAttDate = distinctAttDate.map((value) => ({
    text: value,
    value: value,
  }));
  const filterOptionsForStudents = distinctAttStudents.map((value) => ({
    text: value,
    value: value,
  }));

  const columns = [
    {
      title: setLocale("attendance_report.student_name"),
      dataIndex: "student_name",
      key: "student_name",
      render: (text, record) => (
        <>
          <Link to={`/app/student_view/${record.enc_id}`}>
            {record.student_name + " (" + record.std_id + ")"}
          </Link>
        </>
      ),
      filters: filterOptionsForStudents,
      onFilter: (value, record) => record.student_name.startsWith(value),
      filterSearch: true,
      sorter: (a, b) => a.student_name.localeCompare(b.student_name),
    },
    {
      title: setLocale("classes.label"),
      dataIndex: "class",
      key: "class",
      //   filters: filterOptions,
      //   onFilter: (value, record) => record.class.startsWith(value),
      //   filterSearch: true,
    },
    {
      title: setLocale("attendance_report.attendance_status"),
      // dataIndex: 'record',
      key: "attendance_code",
      render: (record) => (
        <span>
          {record?.overde_attendance_status ? (
            <>
              <Badge status="processing" />{" "}
              <Text type="warning">{record?.attendance_status}</Text>
              <br />
              {record?.overde_attendance_status}
            </>
          ) : (
            <>{record?.attendance_status}</>
          )}
        </span>
      ),
      filters: filterOptionsForAttStatus,
      onFilter: (value, record) => record.attendance_status.startsWith(value),
      filterSearch: true,
    },
    {
      title: setLocale("attendance.attendance_date"),
      dataIndex: "attendance_date",
      key: "attendance_date",
      filters: filterOptionsForAttDate,
      onFilter: (value, record) => record.attendance_date.startsWith(value),
      filterSearch: true,
    },
    {
      title: setLocale("attendance_report.created_by"),
      dataIndex: "created_by",
      key: "created_by",
    },
    {
      title: setLocale("attendance.created_date"),
      dataIndex: "created_date",
      key: "created_date",
    },
    {
      title: setLocale("attendance_report.overde_attendance_detail"),
      // dataIndex: 'overde_attendance_code',
      key: "overde_attendance_code",
      render: (record) => (
        <>
          {record?.overde_attendance_status ? (
            <>
              {record?.overde_comment}
              <br />
              {record?.updated_by}
              <br />
              {record?.updated_date}
            </>
          ) : (
            ""
          )}
        </>
      ),
    },
  ];
  const handleTableChange = (pagination, filters, sorter, extra) => {
    // Update filteredData whenever the table's filters change
    setFilteredData(extra.currentDataSource);
  };
  return (
    <>
      {contextHolder}
      <Row gutter={16} className="my-2">
        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <Space wrap>
            <h4>{reportName}</h4>
          </Space>
        </Col>
        <Col
          xs={24}
          sm={24}
          md={12}
          lg={12}
          xl={12}
          style={{ textAlign: "right" }}
        >
          <Button type="primary" onClick={exportToExcel}>
            {setLocale("attendance_report.export_to_excel")}
          </Button>
        </Col>
      </Row>
      <Table
        dataSource={data}
        columns={columns}
        bordered
        pagination={false}
        onChange={handleTableChange}
      />
    </>
  );
};

export default AttendanceReportTableView;

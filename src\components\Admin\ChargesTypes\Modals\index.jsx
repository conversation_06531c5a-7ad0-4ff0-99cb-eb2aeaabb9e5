import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Col, Drawer, Form, Input, Row, Select, Skeleton, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CHARGES_CATEGORIES, CHARGES_TYPES, TAX_TYPE } from "constants/AppConstants";
import {
  ChargesTypesAddDrawerStatus,
  createChargesTypes,
  getChargesTypes,
  onCloseError
} from "store/slices/ChargesTypes/manageChargesTypesSlice.js";
import { getChargesCategories } from "store/slices/ChargesCategories/manageChargesCategoriesSlice";
import IntlMessage from "components/util-components/IntlMessage";
import { getTaxType } from 'store/slices/TaxType/manageTaxTypeSlice';
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [checked, setChecked] = useState(false);
  const [isTaxable, setIsTaxable] = useState(false);
  const [isNsf, setIsNsf] = useState(false);
  const [checkedAllGrade, setCheckedAllGrade] = useState(false);
  const [checkedGradeFilter, setCheckedGradeFilter] = useState(false);
  const [isDiscountVal, setIsDiscountVal] = useState(false);
  const [loading, setLoading] = useState(true);
  const { ChargesTypesAddDrawer,
    ChargesTypesButtonAndModelLabel,
    ChargesTypesErrors,
    ChargesTypesShowMessage,
    ChargesTypesButtonSpinner,
    ChargesTypesEditData,
    tablePagination,
    sorting, filter,
    DrawerStatus,
  } = useSelector((state) => state[CHARGES_TYPES]);
  const { ChargesCategoriesResult } = useSelector((state) => state[CHARGES_CATEGORIES]);
  const { TaxTypeResult } = useSelector(
    (state) => state[TAX_TYPE]
  );
  const onClose = () => {
    dispatch(ChargesTypesAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const isDiscountValLabel = `${isDiscountVal ? "Checked" : "Unchecked"}`;
  const checkBoxLabel = `${checked ? "Checked" : "Unchecked"}`;
  const isTaxableLabel = `${isTaxable ? "Checked" : "Unchecked"}`;
  const isNsfLabel = `${isNsf ? "Checked" : "Unchecked"}`;
  const checkForAllGradeLabel = `${checkedAllGrade ? "Checked" : "Unchecked"}`;
  const checkForGradeFilterLabel = `${checkedGradeFilter ? "Checked" : "Unchecked"}`;
  const enableStatus = !checked;
  const isTaxableStatus = !isTaxable;

  useEffect(() => {
    dispatch(getTaxType({ perPage: 1000 }));
    dispatch(getChargesCategories({ perPage: 1000 })).then((result) => {
      setLoading(false);
    });
    ChargesTypesEditData?.is_recurring === "Yes" ? setChecked(true) : setChecked(false);
    ChargesTypesEditData?.is_discount === "Yes" ? setIsDiscountVal(true) : setIsDiscountVal(false);
    ChargesTypesEditData?.all_grades === "Yes" ? setCheckedAllGrade(true) : setCheckedAllGrade(false);
    ChargesTypesEditData?.allow_garde_filter === "Yes" ? setCheckedGradeFilter(true) : setCheckedGradeFilter(false);
    ChargesTypesEditData?.is_taxable === "Yes" ? setIsTaxable(true) : setIsTaxable(false);
    ChargesTypesEditData?.is_nsf === "Yes" ? setIsNsf(true) : setIsNsf(false);

  }, []);


  const onSubmit = async (formValues) => {

    if (ChargesTypesEditData && ChargesTypesEditData.id) {
      // If editing, include the id in the form values
      formValues.id = ChargesTypesEditData.id;
    }

    await dispatch(createChargesTypes(formValues))
  };
  useEffect(() => {
    if (Object.keys(ChargesTypesErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getChargesTypes({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [ChargesTypesErrors]);
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  const onChangeCheckBox = (e) => {
    setChecked(e.target.checked);
  };

  const onChangeIsTaxable = (e) => {
    setIsTaxable(e.target.checked);
  };

  const onChangeIsNsf = (e) => {
    setIsNsf(e.target.checked);
  };

  const isDiscount = (e) => {
    setIsDiscountVal(e.target.checked);
  };

  const allGrades = (e) => {
    setCheckedAllGrade(e.target.checked);
  };


  const gradeFilter = (e) => {
    setCheckedGradeFilter(e.target.checked);
  };


  return (
    <>

      <Drawer
        title={ChargesTypesButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={ChargesTypesAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Skeleton active loading={loading}>
          <Form
            layout="vertical"
            onFinish={onSubmit}
            form={form}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            initialValues={{
              ...ChargesTypesEditData,
              is_recurring: ChargesTypesEditData?.is_recurring === "Yes" ? true : false,
              is_taxable: ChargesTypesEditData?.is_taxable === "Yes" ? true : false,
              is_discount: ChargesTypesEditData?.is_discount === "Yes" ? true : false,
              is_nsf: ChargesTypesEditData?.is_nsf === "Yes" ? true : false,
              all_grades: ChargesTypesEditData?.all_grades === "Yes" ? true : false,
              allow_garde_filter: ChargesTypesEditData?.allow_garde_filter === "Yes" ? true : false,
              tax_type_id: ChargesTypesEditData?.tax_type?.map(t => t.id) || []
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="charge_category_id"
                  label={setLocale("charges_and_invoices.charge_category")}
                  rules={[
                    {
                      required: true,
                      message: setLocale("charges_and_invoices.charge_category_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.charge_category_id ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.charge_category_id}
                >
                  <Select
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => (
                      (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                    )}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                    }
                    options={ChargesCategoriesResult.data ?? []}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="charge_type"
                  label={setLocale('name')}
                  rules={[
                    {
                      required: true,
                      message: setLocale('name_error'),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.charge_type ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.charge_type}
                >
                  <Input />
                </Form.Item>
              </Col>


              <Col span={12}>
                <Form.Item
                  name="charge_type_tags"
                  label={setLocale('Charge Type Tags')}
                  rules={[
                    {
                      required: false,
                      message: setLocale('name_error'),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.charge_type ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.charge_type}
                >
                  <Select
                    showSearch
                    allowClear
                    mode='multiple'
                    optionFilterProp="children"
                    options={[
                      {
                        value: "charge_date",
                        label: "Charge Date",
                      },
                      // {
                      //   value: "charge_rate",
                      //   label: "Charge Rate",
                      // }
                    ]}
                  />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="is_discount"
                  valuePropName="checked"
                  label={setLocale("charges_and_invoices.is_discount")}
                  rules={[
                    {
                      required: false,
                      message: setLocale("charges_and_invoices.is_discount_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.is_discount ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.is_discount}
                >
                  <Checkbox onChange={isDiscount}>{isDiscountValLabel}</Checkbox>
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="all_grades"
                  valuePropName="checked"
                  label={setLocale("charges_and_invoices.all_grades")}
                  rules={[
                    {
                      required: false,
                      message: setLocale("charges_and_invoices.all_grades_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.all_grades ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.all_grades}
                >
                  <Checkbox onChange={allGrades}>{checkForAllGradeLabel}</Checkbox>
                </Form.Item>
              </Col>


              <Col span={8}>
                <Form.Item
                  name="allow_garde_filter"
                  valuePropName="checked"
                  label={setLocale("charges_and_invoices.allow_garde_filter")}
                  rules={[
                    {
                      required: false,
                      message: setLocale("charges_and_invoices.allow_garde_filter_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.allow_garde_filter ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.allow_garde_filter}
                >
                  <Checkbox onChange={gradeFilter}>{checkForGradeFilterLabel}</Checkbox>
                </Form.Item>
              </Col>


              <Col span={8}>
                <Form.Item
                  name="is_taxable"
                  valuePropName="checked"
                  label={setLocale("charges_and_invoices.is_taxable")}
                  rules={[
                    {
                      required: false,
                      message: setLocale("charges_and_invoices.is_taxable_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.is_taxable ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.is_taxable}
                >
                  <Checkbox onChange={onChangeIsTaxable}>{isTaxableLabel}</Checkbox>
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="is_nsf"
                  valuePropName="checked"
                  label={setLocale("charges_and_invoices.is_nsf")}
                  rules={[
                    {
                      required: false,
                      message: setLocale("charges_and_invoices.is_nsf_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.is_nsf ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.is_nsf}
                >
                  <Checkbox onChange={onChangeIsNsf}>{isNsfLabel}</Checkbox>
                </Form.Item>
              </Col>


              <Col span={8}>
                <Form.Item
                  name="is_recurring"
                  valuePropName="checked"
                  label={setLocale("charges_and_invoices.is_recurring")}
                  rules={[
                    {
                      required: false,
                      message: setLocale("charges_and_invoices.is_recurring_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.is_recurring ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.is_recurring}
                >
                  <Checkbox onChange={onChangeCheckBox}>{checkBoxLabel}</Checkbox>
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="recurring_interval"
                  label={setLocale("charges_and_invoices.recurring_interval")}
                  rules={[
                    {
                      required: checked,
                      message: setLocale("charges_and_invoices.recurring_interval_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.recurring_interval ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.recurring_interval}
                >
                  <Select
                    showSearch
                    disabled={enableStatus}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    options={[
                      {
                        value: "Monthly",
                        label: setLocale("charges_and_invoices.monthly"),
                      },
                      {
                        value: "3 Months",
                        label: setLocale("charges_and_invoices.three_months"),
                      },
                      {
                        value: "4 Months",
                        label: setLocale("charges_and_invoices.four_months"),
                      },
                      // {
                      //   value: "Quarterly",
                      //   label: setLocale("charges_and_invoices.quarterly"),
                      // },
                      {
                        value: "Interim",
                        label: setLocale("charges_and_invoices.weekly"),
                      },
                      {
                        value: "Custom",
                        label: setLocale("charges_and_invoices.custom"),
                      },
                    ]}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="tax_type_id"
                  label={setLocale("charges_and_invoices.tax_type_id")}
                  rules={[
                    {
                      required: isTaxable,
                      message: setLocale("charges_and_invoices.tax_type_id_error"),
                    },
                  ]}
                  validateStatus={ChargesTypesShowMessage && ChargesTypesErrors.tax_type_id ? "error" : ""}
                  help={ChargesTypesShowMessage && ChargesTypesErrors.tax_type_id}
                >
                  <Select
                    showSearch
                    disabled={isTaxableStatus}
                    optionFilterProp="children"
                    mode="multiple"
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    options={TaxTypeResult?.data ?? []}
                  />
                </Form.Item>
              </Col>

            </Row>

            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={ChargesTypesButtonSpinner}
              >
                {setLocale("save")}
              </Button>
              <Button onClick={onClose}>{setLocale('cancel')}</Button>
            </Space>
          </Form>
        </Skeleton>
      </Drawer>
    </>
  );
};
export default Index;


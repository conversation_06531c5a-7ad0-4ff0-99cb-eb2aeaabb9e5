import React,{useEffect} from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { DESIGNATION } from "constants/AppConstants";
import {
    DesignationAddDrawerStatus,
    createDesignation,
    getDesignation,
    onCloseError
  } from "store/slices/Designation/manageDesignationSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;

const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {
        DesignationAddDrawer,
        DesignationButtonAndModelLabel,
        sorting,
        filter,
        DesignationErrors,
        DesignationShowMessage,
        DesignationButtonSpinner,
        DesignationEditData,
        tablePagination,
        DrawerStatus
    } = useSelector(
    (state) => state[DESIGNATION]
    );
const onClose = () => {
    dispatch(DesignationAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
}
const onSubmit = async (formValues) => {
  if (DesignationEditData && DesignationEditData.id) {
    // If editing, include the id in the form values
    formValues.id = DesignationEditData.id;
  }

  await dispatch(createDesignation(formValues));
};

  useEffect(() => {
    if (Object.keys(DesignationErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getDesignation({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [DesignationErrors]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

  return (
    <>

      <Drawer
        title={DesignationButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={DesignationAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: DesignationEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('nameError'),
                  },
                ]}
                validateStatus={DesignationShowMessage && DesignationErrors.name ? "error" : ""}
                help={DesignationShowMessage && DesignationErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={DesignationButtonSpinner}
            >
              {DesignationButtonAndModelLabel}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


import React, { useEffect } from 'react';
import { But<PERSON>, Col, Drawer, Form, Input, Row, Select, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { ENROLLMENT_STATUS } from "constants/AppConstants";
import {
  EnrollmentStatusAddDrawerStatus,
  createEnrollmentStatus,
  getEnrollmentStatus,
  onCloseError
} from "store/slices/EnrollmentStatus/manageEnrollmentStatusSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { TextArea } = Input;


const Index = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {DrawerStatus, filter, sorting, EnrollmentStatusAddDrawer, EnrollmentStatusButtonAndModelLabel, EnrollmentStatusErrors, EnrollmentStatusShowMessage, EnrollmentStatusButtonSpinner, EnrollmentStatusEditData, tablePagination } = useSelector(
    (state) => state[ENROLLMENT_STATUS]
  );
  const onClose = () => {
    dispatch(EnrollmentStatusAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
  const onSubmit = async (formValues) => {

    if (EnrollmentStatusEditData && EnrollmentStatusEditData.id) {
      // If editing, include the id in the form values
      formValues.id = EnrollmentStatusEditData.id;
    }

    await dispatch(createEnrollmentStatus(formValues))
  };
  useEffect(() => {
    if (Object.keys(EnrollmentStatusErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getEnrollmentStatus({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [EnrollmentStatusErrors]);
  const onFinishFailed = (errorsInfo) => {
    console.log("Form submission failed:", errorsInfo);
  };

  return (
    <>

      <Drawer
        title={EnrollmentStatusButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={EnrollmentStatusAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
          paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            enrollment_status: EnrollmentStatusEditData?.enrollment_status,
            status_code: EnrollmentStatusEditData?.status_code,
            type: EnrollmentStatusEditData?.type,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="enrollment_status"
                label={setLocale('name')}
                rules={[
                  {
                    required: true,
                    message: setLocale('name_error'),
                  },
                ]}
                validateStatus={EnrollmentStatusShowMessage && EnrollmentStatusErrors.enrollment_status ? "error" : ""}
                help={EnrollmentStatusShowMessage && EnrollmentStatusErrors.enrollment_status}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status_code"
                label={setLocale('enrollment_status.status_code')}
                rules={[
                  {
                    required: true,
                    message: setLocale('enrollment_status.status_code_error'),
                  },
                ]}
                validateStatus={EnrollmentStatusShowMessage && EnrollmentStatusErrors.status_code ? "error" : ""}
                help={EnrollmentStatusShowMessage && EnrollmentStatusErrors.status_code}
              >
                <Input />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="type"
                label={setLocale("enrollment_status.type")}
                rules={[
                  {
                    required: true,
                    message: setLocale("enrollment_status.type_error"),
                  },
                ]}
                validateStatus={EnrollmentStatusShowMessage && EnrollmentStatusErrors.type ? "error" : ""}
                help={EnrollmentStatusShowMessage && EnrollmentStatusErrors.type}
              >
                <Select
                  className="rounded-0"
                  showSearch
                  optionLabelProp="label"
                  allowClear
                  optionFilterProp="children"
                  // filterOption={(input, option) => (
                  //   (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())
                  // )}
                  // filterSort={(optionA, optionB) =>
                  //   (optionA?.label.toLowerCase() ?? '').localeCompare(optionB?.label.toLowerCase())
                  // }
                  options={[
                    { value: "active", label: setLocale("active") },
                    { value: "inactive", label: setLocale("inactive") },
                  ]}
                />
              </Form.Item>
            </Col>

          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={EnrollmentStatusButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale('Cancel')}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


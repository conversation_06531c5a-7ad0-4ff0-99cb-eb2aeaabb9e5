import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Switch } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { CITY } from "constants/AppConstants";
import {
    CityAddDrawerStatus,
    createCity,
    getCity,
    onCloseError
  } from "store/slices/City/manageCitySlice.js";
import IntlMessage from "components/util-components/IntlMessage";
const setLocale = (localeKey, isLocaleOn = true) =>
    isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();
const { TextArea } = Input;


const Index = () => {
const [form] = Form.useForm();
const dispatch = useDispatch();
    const {DrawerStatus, filter, sorting, CityAddDrawer, CityButtonAndModelLabel, CityErrors, CityShowMessage, CityButtonSpinner, CityEditData, tablePagination } = useSelector(
        (state) => state[CITY]
      );
const [switchValue, setSwitchValue] = useState('checked');
  const onClose = () => {
    dispatch(CityAddDrawerStatus({ errorStatus: 0, status: false }));
    dispatch(onCloseError());
  }
const onSubmit = async (formValues) => {

  if (CityEditData && CityEditData.id) {
    // If editing, include the id in the form values
    formValues.id = CityEditData.id;
    formValues.is_default = CityEditData.is_default == 1 ? true : false;
  }

  await dispatch(createCity(formValues))
};
useEffect(() => {
    if (Object.keys(CityErrors).length === 0 && DrawerStatus === 0) {
      dispatch(getCity({
        page: tablePagination.current,
        perPage: tablePagination.pageSize,
        filter: filter, sorting: sorting
      }));
    }
  }, [CityErrors]);

useEffect(() => {
  CityEditData.is_default === 1 ? setSwitchValue('checked') : setSwitchValue();
}, [dispatch]);

const onFinishFailed = (errorsInfo) => {
  console.log("Form submission failed:", errorsInfo);
};

const getSwitchValue = (checked) => {
  setSwitchValue(checked);
};

  return (
    <>

      <Drawer
        title={CityButtonAndModelLabel}
        width={window.innerWidth > 800 ? "45%" : window.innerWidth - 100}
        onClose={onClose}
        open={CityAddDrawer}
        maskClosable={false}
        zIndex={1002}
        bodyStyle={{
            paddingBottom: 80,
        }}
      >
        <Form
          layout="vertical"
          onFinish={onSubmit}
          form={form}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          initialValues={{
            name: CityEditData?.name,
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={setLocale("name")}
                rules={[
                  {
                    required: true,
                    message: setLocale("name_error"),
                  },
                ]}
                validateStatus={CityShowMessage && CityErrors.name ? "error" : ""}
                help={CityShowMessage && CityErrors.name}
              >
                <Input />
              </Form.Item>
            </Col>
            {!(CityEditData && CityEditData.id) &&
              <Col span={12}>
                <Form.Item
                  name="is_default"
                  // valuePropName={switchValue}
                  label="Is Default"
                  rules={[
                    {
                      required: false,
                      message: "Status Error",
                    },
                  ]}
                  validateStatus={CityShowMessage && CityErrors.is_default ? "error" : ""}
                      extra={CityShowMessage && CityErrors.is_default}


                >
                  <Switch
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    onChange={getSwitchValue}
                    checked={switchValue}
                    name="is_default"
                  />
                </Form.Item>
              </Col>
            }
          </Row>

          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={CityButtonSpinner}
            >
              {setLocale("save")}
            </Button>
            <Button onClick={onClose}>{setLocale("cancel")}</Button>
          </Space>
        </Form>
      </Drawer>
    </>
  );
};
export default Index;


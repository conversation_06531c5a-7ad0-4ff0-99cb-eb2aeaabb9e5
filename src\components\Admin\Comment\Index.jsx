import React, { useState, useEffect, useRef } from "react";
import { COMMENT } from 'constants/AppConstants';
import { useSelector, useDispatch } from "react-redux";
import {
  Breadcrumb,
  Col,
  Row,
  Skeleton,
  Tabs,
  TabsProps,
} from "antd";
import {
  setMyCommentComponent,
  setAllCommentComponent,
  updateSortFilters,
  getComment
} from "store/slices/Comment/manageCommentSlice.js";
import IntlMessage from "components/util-components/IntlMessage";
import { Link } from "react-router-dom";

import AllComments from './AllComments';
import MyComments from './MyComments';
import OrganizationSelect from "../OrganizationDropdown/OrganizationSelect";
import { USER_INFORMATION } from "constants/AuthConstant";

const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

function Index() {
  const dispatch = useDispatch();
  const {
    MyCommentComponent,
    AllCommentComponent,
    tablePagination,
    sorting,
    filter,
  } = useSelector((state) => state[COMMENT]);



  const onChange = (key: string) => {
    console.log(key);
    if (key === 'all') {
      dispatch(setAllCommentComponent(true));
      dispatch(setMyCommentComponent(false));
    } else {
      dispatch(setAllCommentComponent(false));
      dispatch(setMyCommentComponent(true));
    }
  };

  const items: TabsProps["items"] = [
    {
      key: "all",
      label: "All",
      children: AllCommentComponent && <AllComments />,
    },
    {
      key: "myComments",
      label: "My Comment",
      children: MyCommentComponent && <MyComments />,
    },
  ];

  const getModuleData = async (page, perPage, filterData, sortingData) => {
    await dispatch(
      getComment({
        page: page,
        perPage: perPage,
        filter: filterData,
        sorting: sortingData,
      })
    );
  };

  return (
    <>
      <Row gutter={16}>
        <Col xs={24} sm={24} md={12} lg={12}>
          <Breadcrumb className="my-2 mx-2">
            <Breadcrumb.Item>
              <Link to="/app/default">{setLocale("home")}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{setLocale("Comment")}</Breadcrumb.Item>
          </Breadcrumb>
        </Col>
        <Col xs={24} sm={24} md={12} lg={12} style={{ textAlign: 'right' }}>
          <OrganizationSelect
            updateSortFilters={updateSortFilters}
            filter={filter}
            sorting={sorting}
            tablePagination={tablePagination}
            getModuleData={getModuleData}
          />
        </Col>
      </Row>

      <>
        <div className="code-box">
          <section className="code-box-demo">
            <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
          </section>
        </div>
      </>
    </>
  );
}

export default Index;

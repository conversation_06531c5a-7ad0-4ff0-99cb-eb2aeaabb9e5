import React, { useState, useEffect, useRef } from "react";
import moment from "moment";
import { useSelector, useDispatch } from "react-redux";
import {
  Form,
  Row,
  Col,
  Card,
  DatePicker,
  Button,
  Skeleton,
  Divider,
  Tabs,
  Select,
  Table,
  Badge,
} from "antd";
// import Flex from 'components/shared-components/Flex'
// import {
//   EyeOutlined,
//   DownloadOutlined,
//   ArrowDownOutlined
// } from '@ant-design/icons';
import Chart from "react-apexcharts";
import IntlMessage from "components/util-components/IntlMessage";
import {
  // getSingleStudentAttReport,
  // makeStudentAttNull,
  // changeReportView,
  getAttendanceDashboardReport,
  getMultipleClassStudents,
  updateSortFilters,
} from "store/slices/AttendanceReport/manageAttendanceReportSlice.js";
import { ATTENDANCE_REPORT } from "constants/attendance-reports/index";
import { MANAGE_CLASS, SCHOOL_YEAR } from "constants/AppConstants";
import AttendanceCard from "./AttendanceCard";
import AttendanceDount from "./AttendanceDount";
import AttendanceClassBar from "./AttendanceClassBar";
import AttendanceClassArea from "./AttendanceClassArea";
import AttendanceStudentAttention from "./AttendanceStudentAttention";
// import ChartWidget from 'components/shared-components/ChartWidget';
// import ApexChart from "react-apexcharts";
// import { apexSparklineChartDefultOption, COLORS } from 'constants/ChartConstant';
// import Position from "views/app-views/components/data-display/tabs/Position";
import { USER_INFORMATION } from "constants/AuthConstant";
import OrganizationSelect from "components/Admin/OrganizationDropdown/OrganizationSelect";
import { getManageClass } from "store/slices/ManageClass/manageManageClassSlice";
const userInformation = JSON.parse(localStorage.getItem(USER_INFORMATION));
const selectedOrganization = userInformation.organization_id;
const userOrganizations = userInformation.organizations;

const setLocale = (localeKey, isLocaleOn = true) =>
  isLocaleOn ? <IntlMessage id={localeKey} /> : localeKey.toString();

const { RangePicker } = DatePicker;
const { Option } = Select;

function Index() {
  const [defaultDate, setDefaultDate] = useState([
    moment().subtract(30, "days"),
    moment(),
  ]);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const {
    // studentAttReport,
    AttendanceReportButtonSpinner,
    //   attendanceDashboardReport,
    AttendanceReportLoading,
    classStudents,
    filter,
    sorting,
    tablePagination,
  } = useSelector((state) => state[ATTENDANCE_REPORT]);
  const { ManageClassResult } = useSelector((state) => state[MANAGE_CLASS]);

  useEffect(() => {
    onFinish({ date: defaultDate });
  }, []);

  const onFinish = async (values) => {
    const dateRange = values["date"];
    /** make date formate */
    values.from_date = moment(dateRange[0]).format("YYYY-MM-DD");
    values.end_date = moment(dateRange[1]).format("YYYY-MM-DD");
    values.organization_id = filter?.organization_id ?? selectedOrganization;
    //   const formData = {
    //       from_date: values.from_date,
    //       end_date: values.end_date,
    //   }
    await dispatch(getAttendanceDashboardReport(values));
  };

  const getStudentByClass = (value) => {
    form.setFieldsValue({
      student_id: [],
    });
    dispatch(getMultipleClassStudents({ id: value }));
  };

  const onChangeOrganization = (value) => {
    dispatch(getManageClass({ organization_id: value })).then((result) => {
      // setPageLoad(false)
    });
  };
  return (
    <>
      <Card className="pl-3 card">
        <Form
          layout="vertical"
          initialValues={{ date: defaultDate }}
          onFinish={onFinish}
          form={form}
        >
          <Row gutter={12}>
            <Col xs={24} sm={24} md={6} lg={6} xl={6}>
              <Form.Item
                name="date"
                label={setLocale("attendance_report.date_from_to")}
                rules={[
                  {
                    required: true,
                    message: setLocale("attendance_report.daterange_error"),
                  },
                ]}
              >
                <RangePicker style={{ width: "100%" }} />
              </Form.Item>
            </Col>

            {userOrganizations.length > 1 && (
              <>
                <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                  <Form.Item
                    name="organization_id"
                    label={setLocale("Organization")}
                    rules={[
                      {
                        required: false,
                        message: setLocale("Please Select Class"),
                      },
                    ]}
                  >
                    <OrganizationSelect
                      updateSortFilters={updateSortFilters}
                      filter={filter}
                      onChange={onChangeOrganization}
                      sorting={sorting}
                      tablePagination={tablePagination}
                      getModuleData={onFinish}
                      runUseEffect={false}
                      dropStyle={true}
                    />
                  </Form.Item>
                </Col>
              </>
            )}

            <Col xs={24} sm={24} md={4} lg={4} xl={4}>
              <Form.Item
                name="class"
                label={setLocale("classes.label")}
                rules={[
                  {
                    required: false,
                    message: setLocale("classes.label_error"),
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  optionLabelProp="label"
                  onChange={getStudentByClass}
                  maxTagCount={1}
                >
                  {ManageClassResult
                    ? ManageClassResult.map((clas, index) => (
                        <Option value={clas.id} key={index} label={clas.name}>
                          {clas.name}
                        </Option>
                      ))
                    : null}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={4} lg={4} xl={4}>
              <Form.Item
                name="student_id"
                label={setLocale("attendance_report.select_student")}
                rules={[
                  {
                    required: false,
                    message: setLocale("attendance_report.student_error"),
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  optionLabelProp="label"
                  maxTagCount={1}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) >= 0
                  }
                >
                  {classStudents
                    ? classStudents.map((clsStd, index) => (
                        <Option
                          value={clsStd.students.id}
                          key={index}
                          label={clsStd.students.full_name}
                        >
                          {clsStd.students.full_name +
                            " (" +
                            clsStd.students.student_id +
                            ")"}
                        </Option>
                      ))
                    : null}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={24} md={4} lg={4} xl={4}>
              <Form.Item label=" " className="text-right">
                <Button
                  loading={AttendanceReportButtonSpinner}
                  type="primary"
                  htmlType="submit"
                >
                  {setLocale("attendance_report.generate_report")}
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
      {AttendanceReportLoading ? (
        <>
          {" "}
          <Skeleton active /> <Skeleton active />
        </>
      ) : (
        <>
          <AttendanceCard />
          <Row gutter={16}>
            <Col xs={24} sm={24} md={24} lg={12} xl={12}>
              <AttendanceClassBar />
            </Col>
            <Col xs={24} sm={24} md={24} lg={12} xl={12}>
              <AttendanceDount />
            </Col>
          </Row>
          <Row gutter={16}>
            <Col xs={24} sm={24} md={24} lg={12} xl={12}>
              <AttendanceClassArea />
            </Col>
            <Col xs={24} sm={24} md={24} lg={12} xl={12}>
              <AttendanceStudentAttention />
            </Col>
          </Row>
        </>
      )}
    </>
  );
}

export default Index;
